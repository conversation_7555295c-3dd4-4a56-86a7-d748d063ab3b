include: ../analysis_options.yaml

linter:
  rules:
    avoid_catches_without_on_clauses: true
    avoid_catching_errors: false # TODO(ianh): we should refactor the tool codebase to avoid relying on this so much
    library_private_types_in_public_api: false # Tool does not have any public API.
    no_runtimeType_toString: false # We use runtimeType for debugging in the tool.
    only_throw_errors: true
    prefer_relative_imports: true
    public_member_api_docs: false # Tool does not have any public API.
    unawaited_futures: true
