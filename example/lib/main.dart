import 'package:flutter/material.dart';
import 'example_selector.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Common Package Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const ExampleSelectorHome(),
    );
  }
}
