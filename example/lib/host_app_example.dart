// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:get_it/get_it.dart';

/// Minimal example app that demonstrates the modular system with a basic custom module.
///
/// This example shows the essential steps to use the modular system in a host app:
///
/// 1. Initialize only the core modules needed from the common package
/// 2. Create and register a simple custom module
/// 3. Use the module in a basic UI
///
/// IMPORTANT: Host applications should ONLY use the methods provided in init_common_package.dart
/// to interact with the module system.
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize minimal flavor configuration
  FlavorConfig(
    flavor: 'dev',
    values: CommonFlavorValues(
      baseUrl: 'https://api.example.com',
      initializeFirebaseSdk: false,
      oneSignalAppId: null,
    ),
  );

  // Initialize only the essential modules
  await initCommonPackage(
    features: [
      CommonPackageModuleNames.core, // Core module is essential
      CommonPackageModuleNames.ui,   // UI module for basic components
    ],
  );

  // Register and initialize a simple custom module
  registerCustomModule(UserProfileModule(), source: 'host_app');
  await initializeSpecificModules(['host_user_profile']);

  runApp(const HostAppExampleApp());
}

/// A simple custom user profile module from the host app.
class UserProfileModule implements FeatureModule {
  @override
  String get name => 'host_user_profile';

  @override
  List<Type> get dependencies => <Type>[UserProfileService];

  @override
  Future<void> register(GetIt getIt) async {
    // Register user profile service
    getIt.registerLazySingleton<UserProfileService>(
        () => UserProfileServiceImpl());
  }
}

/// Example app that demonstrates a host app using the common package modularization.
class HostAppExampleApp extends StatelessWidget {
  const HostAppExampleApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Host App Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const HostAppHome(),
    );
  }
}

/// Home screen for the host app example.
class HostAppHome extends StatefulWidget {
  const HostAppHome({Key? key}) : super(key: key);

  @override
  State<HostAppHome> createState() => _HostAppHomeState();
}

class _HostAppHomeState extends State<HostAppHome> {
  int _currentIndex = 0;
  final List<Widget> _pages = <Widget>[
    const UserProfilePage(),
    const ModuleInfoPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Minimal Host App Example'),
      ),
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (int index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.info),
            label: 'Modules',
          ),
        ],
      ),
    );
  }
}

/// User profile page that uses the user profile module.
class UserProfilePage extends StatefulWidget {
  const UserProfilePage({Key? key}) : super(key: key);

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage> {
  late UserProfileService _userProfileService;
  late UserProfile _userProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _userProfileService = getIt<UserProfileService>();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _userProfile = await _userProfileService.getUserProfile();
    } catch (e) {
      debugPrint('Error loading user profile: $e');
      _userProfile = UserProfile(
        name: 'Error loading profile',
        email: '',
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final CommonColors colors = getIt<CommonColors>();
    final CommonTextStyles textStyles = getIt<CommonTextStyles>();
    final CommonButtonStyles buttonStyles = getIt<CommonButtonStyles>();

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          const SizedBox(height: 20),
          CircleAvatar(
            radius: 50,
            child: Text(
              _userProfile.name.isNotEmpty ? _userProfile.name[0].toUpperCase() : '?',
              style: textStyles.h600(colors.background),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            _userProfile.name,
            style: textStyles.h600(colors.textActive),
          ),
          const SizedBox(height: 8),
          Text(
            _userProfile.email,
            style: textStyles.bodyMedium(colors.textActive),
          ),
          const SizedBox(height: 32),
          CommonButton(
            onPressed: _loadUserProfile,
            style: buttonStyles.primary(ButtonSize.medium),
            child: Text(
              'Refresh Profile',
              style: textStyles.button(ButtonSize.medium, colors.background),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'This simple example shows how a host app can use a custom module.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }
}

/// Module info page that shows information about initialized modules.
class ModuleInfoPage extends StatelessWidget {
  const ModuleInfoPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final CommonColors colors = getIt<CommonColors>();
    final CommonTextStyles textStyles = getIt<CommonTextStyles>();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'Initialized Modules',
            style: textStyles.h600(colors.textActive),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: <Widget>[
                ...getInitializedModules().map((String module) => Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          module,
                          style: textStyles.h400(color: colors.textActive),
                        ),
                      ),
                    )),
                const SizedBox(height: 16),
                const Text(
                  'This page shows the initialized modules in the application. '
                  'The host app only initializes essential modules and adds its own custom module.',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Mock models and services for the example

/// Simple user profile model.
class UserProfile {
  final String name;
  final String email;

  UserProfile({
    required this.name,
    required this.email,
  });
}

/// User profile service interface.
abstract class UserProfileService {
  Future<UserProfile> getUserProfile();
}

/// Implementation of the user profile service.
class UserProfileServiceImpl implements UserProfileService {
  @override
  Future<UserProfile> getUserProfile() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Return a mock profile
    return UserProfile(
      name: 'John Doe',
      email: '<EMAIL>',
    );
  }
}


