# Flutter Common Package Example

This example demonstrates how to use the Flutter Common Package in a Flutter application.

> **Note**: This package is proprietary software owned by Trusting Social and is intended for internal use only within Trusting Social and its authorized partners and clients.

## Getting Started

1. Make sure you have Flutter installed and set up on your machine.
2. Clone this repository.
3. Run `flutter pub get` to install dependencies.
4. Run the example selector app with `flutter run -t lib/example_selector.dart`.

## Available Example

The example app demonstrates all aspects of the Flutter Common Package in a single comprehensive example:

**Comprehensive Modular System Example** (`lib/host_app_example.dart`): A complete demonstration of the modular system that includes:

1. **Modular Initialization**:
   - Selective initialization of only needed modules
   - Improved startup time and reduced resource usage
   - Listing initialized and available modules

2. **Custom Modules**:
   - Creating custom modules that implement FeatureModule
   - Registering modules with source tracking for name uniqueness
   - Different registration approaches (separate steps vs. single step)

3. **Host App Integration**:
   - Using the modular system in a practical application
   - Building features that use both common package and custom modules
   - Demonstrating UI components and design system usage

To run a specific example, use the example selector app or run the specific file directly:

```bash
# Run the example selector
flutter run -t lib/example_selector.dart

# Or run a specific example directly
flutter run -t lib/host_app_example.dart
```

## What This Example Demonstrates

This example demonstrates the following components from the Flutter Common Package:

### Initialization

The example demonstrates both traditional and modular initialization:

#### Traditional Initialization

```dart
// Initialize flavor configuration
FlavorConfig(
  flavor: 'dev',
  values: CommonFlavorValues(
    baseUrl: 'https://api.example.com',
    initializeFirebaseSdk: false,
    oneSignalAppId: null,
  ),
);

// Initialize the common package with all features
await initCommonPackage();
```

#### Modular Initialization

```dart
// Initialize flavor configuration
FlavorConfig(
  flavor: 'dev',
  values: CommonFlavorValues(
    baseUrl: 'https://api.example.com',
    initializeFirebaseSdk: false,
    oneSignalAppId: null,
  ),
);

// Initialize only specific features
await initCommonPackage(
  features: [ModuleNames.core, ModuleNames.network, ModuleNames.ui],
);
```

#### Custom Modules

The example demonstrates how to create and register custom modules:

```dart
// Create a custom module that implements FeatureModule
class CustomAuthModule implements FeatureModule {
  @override
  String get name => 'auth';

  @override
  List<Type> get dependencies => [AuthService, UserRepository];

  @override
  Future<void> register(GetIt getIt) async {
    // Register dependencies
    getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl());
    getIt.registerLazySingleton<UserRepository>(() => UserRepositoryImpl());
  }
}

// For host apps: Register and initialize custom modules using methods from init_common_package.dart
// Method 1: Register and initialize in separate steps
registerCustomModule(CustomAuthModule(), source: 'host_app');
await initializeSpecificModules(['auth']);

// Method 2: Register and initialize in a single step
await registerAndInitializeCustomModule(CustomAuthModule(), source: 'host_app');
```

### Common Widgets

#### Buttons

```dart
CommonButton(
  onPressed: () {
    debugPrint('Primary button pressed');
  },
  style: buttonStyles.primaryButtonStyle,
  child: Text(
    'Primary Button',
    style: textStyles.buttonText(colors.background),
  ),
)
```

#### Radio Buttons

```dart
CommonRadio<String>(
  title: 'Option 1',
  value: 'option1',
  isSelected: _selectedOption == 'option1',
  onChange: (value) {
    setState(() {
      _selectedOption = value;
    });
  },
)
```

### Design System

The example demonstrates how to access and use the design system components:

```dart
final CommonButtonStyles buttonStyles = getIt.get<CommonButtonStyles>();
final CommonColors colors = getIt.get<CommonColors>();
final CommonTextStyles textStyles = getIt.get<CommonTextStyles>();
```

## Structure

- `lib/example_selector.dart`: The main entry point for the example selector app.
- `lib/host_app_example.dart`: Comprehensive example demonstrating all aspects of the modular system.
- `lib/extensions.dart`: Extension methods used by the examples.

Note: We've combined all modular system examples into a single comprehensive example to reduce redundancy and improve maintainability.

## Comprehensive Modular System Example

The Comprehensive Modular System Example (`lib/host_app_example.dart`) demonstrates all aspects of the modular system in a single app. It shows how to:

1. Initialize only the modules needed from the common package using `initCommonPackage`
2. Create custom modules that implement `FeatureModule` for specific features
3. Register custom modules directly with GetIt instead of using ModuleRegistry
4. Use these modules to build a simple but practical application

### Best Practices for Host Apps

**IMPORTANT**: Host applications should ONLY use the methods provided in `init_common_package.dart` to interact with the module system. Direct usage of `ModuleRegistry` is not supported for host applications and may break in future versions.

#### Recommended Approach:

```dart
// Initialize only the modules you need from the common package
await initCommonPackage(
  features: [
    CommonPackageModuleNames.core,
    CommonPackageModuleNames.ui,
  ],
);

// Register and initialize custom host app modules using methods from init_common_package.dart
// Method 1: Register and initialize in separate steps
registerCustomModule(UserProfileModule(), source: 'host_app');
await initializeSpecificModules(['host_user_profile']);

// Method 2: Register and initialize in a single step
await registerAndInitializeCustomModule(SettingsModule(), source: 'host_app');
```

#### Avoid This Approach:

```dart
// DO NOT use ModuleRegistry or ModuleRegistryBuilder directly in host apps
final ModuleRegistryBuilder hostBuilder = ModuleRegistry.builder(getIt); // AVOID THIS

hostBuilder.register(UserProfileModule(), source: 'host_app'); // AVOID THIS

final ModuleRegistry hostRegistry = hostBuilder.build(); // AVOID THIS

await hostRegistry.initializeAllModules(); // AVOID THIS
```

The example includes:

- A user profile feature using a custom `UserProfileModule`
- A settings feature using a custom `SettingsModule`
- A module info page showing information about initialized modules

This example is particularly useful for understanding how to integrate the Flutter Common Package into a real application.

## Screenshots

(Add screenshots here once the example is running)

## Next Steps

- Try modifying the examples to use other components from the Flutter Common Package.
- Explore the source code to understand how the components are implemented.
- Check the documentation for more information on available components and their usage.
- Create your own custom modules to extend the functionality of the Flutter Common Package.
