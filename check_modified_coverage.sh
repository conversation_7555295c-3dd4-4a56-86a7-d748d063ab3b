#!/bin/bash

# Set colors for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
coverage_threshold=80
lcov_path="./coverage/lcov.info"
check_mode="uncommitted"
generate_coverage=false
show_uncovered_lines=false
sort_by_coverage=false
output_format="text"
output_file=""

# Function to display usage information
function show_help {
  echo -e "${BLUE}Usage:${NC} $0 [options]"
  echo
  echo "Options:"
  echo "  -h, --help                 Show this help message"
  echo "  -t, --threshold <number>   Set coverage threshold percentage (default: coverage_threshold)"
  echo "  -m, --mode <mode>          Set check mode: uncommitted, last_commit, branch (default: uncommitted)"
  echo "  -g, --generate             Generate coverage report before checking"
  echo "  -u, --show-uncovered       Show uncovered lines for files below threshold"
  echo "  -s, --sort                 Sort files by coverage percentage (ascending)"
  echo "  -f, --format <format>      Output format: text, json, html (default: text)"
  echo "  -o, --output <file>        Write output to file instead of stdout"
  echo
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -t|--threshold)
      coverage_threshold="$2"
      shift 2
      ;;
    -m|--mode)
      check_mode="$2"
      shift 2
      ;;
    -g|--generate)
      generate_coverage=true
      shift
      ;;
    -u|--show-uncovered)
      show_uncovered_lines=true
      shift
      ;;
    -s|--sort)
      sort_by_coverage=true
      shift
      ;;
    -f|--format)
      output_format="$2"
      shift 2
      ;;
    -o|--output)
      output_file="$2"
      shift 2
      ;;
    *)
      echo -e "${RED}Error:${NC} Unknown option $1"
      show_help
      exit 1
      ;;
  esac
done

# Function to generate coverage report
function generate_coverage_report {
  echo -e "${BLUE}Generating coverage report...${NC}"
  flutter test --coverage
  if [ $? -ne 0 ]; then
    echo -e "${RED}Error:${NC} Failed to generate coverage report."
    exit 1
  fi
}

# Generate coverage if requested
if [ "$generate_coverage" = true ]; then
  generate_coverage_report
fi

echo -e "${CYAN}🔍 Verify coverage on modified lib/* folder${NC}"
echo -e "${CYAN}===================================================${NC}"

# Get modified files based on check mode
echo -e "${BLUE}Checking changes in lib/* folder (mode: $check_mode)${NC}"
files_string=""

case $check_mode in
  "uncommitted")
    files_string=$(git diff --name-only HEAD -- lib/ && git diff --name-only --staged -- lib/)
    ;;
  "last_commit")
    files_string=$(git diff --name-only HEAD~1 HEAD -- lib/)
    ;;
  *)
    echo -e "${RED}Error:${NC} Unknown check mode: $check_mode"
    exit 1
    ;;
esac

if [ -z "$files_string" ]; then
  echo -e "${GREEN}✅ No files changed in lib/* folder${NC}"
  exit 0
fi

# Convert files string to array
declare -a files
while IFS= read -r line; do
  files+=("$line")
done <<< "$files_string"

# Create arrays to store results
declare -a file_results
declare -a file_coverage_values
declare -a file_paths
declare -a file_covered_lines
declare -a file_total_lines
declare -a file_uncovered_lines

passed=true
total_files=0
files_below_threshold=0
total_lines_all=0
covered_lines_all=0

# Check if lcov.info exists
if [ ! -f "$lcov_path" ]; then
  echo -e "${RED}❌ Coverage file not found. Run 'flutter test --coverage' first or use -g option.${NC}"
  exit 1
fi

# Process each file
for file in "${files[@]}"; do
  # Skip non-Dart files
  if [[ ! $file =~ \.dart$ ]]; then
    continue
  fi

  # Get the file path relative to the project root
  relative_file_path=$file

  # Find the section for this file in lcov.info
  file_section=$(grep -n "SF:$relative_file_path" "$lcov_path" | cut -d':' -f1)

  if [ -z "$file_section" ]; then
    file_results+=("${YELLOW}⚠️ $file: file's coverage unavailable${NC}")
    file_coverage_values+=(-1)
    file_paths+=("$file")
    file_covered_lines+=(-1)
    file_total_lines+=(-1)
    continue
  fi

  # Find the end of the section
  end_section=$(tail -n +$file_section "$lcov_path" | grep -n "end_of_record" | head -1 | cut -d':' -f1)
  end_line=$((file_section + end_section - 1))

  # Extract the section for this file
  file_coverage=$(sed -n "${file_section},${end_line}p" "$lcov_path")

  # Get LF (total lines) and LH (covered lines) from the section
  total_lines=$(echo "$file_coverage" | grep "LF:" | cut -d':' -f2)
  covered_lines=$(echo "$file_coverage" | grep "LH:" | cut -d':' -f2)

  if [ -z "$total_lines" ] || [ -z "$covered_lines" ]; then
    file_results+=("${YELLOW}⚠️ $file: couldn't parse coverage data${NC}")
    file_coverage_values+=(-1)
    file_paths+=("$file")
    file_covered_lines+=(-1)
    file_total_lines+=(-1)
    continue
  fi

  # Calculate coverage percentage
  if [ "$total_lines" -eq 0 ]; then
    coverage=0
  else
    coverage=$(echo "scale=2; $covered_lines * 100 / $total_lines" | bc)
  fi

  # Update totals
  total_files=$((total_files + 1))
  total_lines_all=$((total_lines_all + total_lines))
  covered_lines_all=$((covered_lines_all + covered_lines))

  # Check if coverage meets threshold
  if (( $(echo "$coverage < $coverage_threshold" | bc -l) )); then
    file_results+=("${RED}❌ $file: $coverage% ($covered_lines/$total_lines lines covered)${NC}")
    passed=false
    files_below_threshold=$((files_below_threshold + 1))

    # Get uncovered lines if requested
    if [ "$show_uncovered_lines" = true ]; then
      # Extract line coverage data
      line_data=$(echo "$file_coverage" | grep "^DA:")
      uncovered_lines=""

      # Process each line to find uncovered ones
      while IFS= read -r line_info; do
        line_num=$(echo "$line_info" | cut -d':' -f2 | cut -d',' -f1)
        hit_count=$(echo "$line_info" | cut -d',' -f2)
        if [ "$hit_count" -eq 0 ]; then
          if [ -z "$uncovered_lines" ]; then
            uncovered_lines="$line_num"
          else
            uncovered_lines="$uncovered_lines,$line_num"
          fi
        fi
      done <<< "$line_data"

      file_uncovered_lines+=("$uncovered_lines")
    else
      file_uncovered_lines+=("")
    fi
  else
    file_results+=("${GREEN}✅ $file: $coverage% ($covered_lines/$total_lines lines covered)${NC}")
    file_uncovered_lines+=("")
  fi

  file_coverage_values+=("$coverage")
  file_paths+=("$file")
  file_covered_lines+=("$covered_lines")
  file_total_lines+=("$total_lines")
done

# Calculate overall coverage
if [ "$total_lines_all" -eq 0 ]; then
  overall_coverage=0
else
  overall_coverage=$(echo "scale=2; $covered_lines_all * 100 / $total_lines_all" | bc)
fi

# Sort results by coverage if requested
if [ "$sort_by_coverage" = true ]; then
  # Create a temporary file for sorting
  temp_file=$(mktemp)

  # Write data to temp file for sorting
  for i in "${!file_coverage_values[@]}"; do
    echo "${file_coverage_values[$i]}|${file_results[$i]}|${file_paths[$i]}|${file_covered_lines[$i]}|${file_total_lines[$i]}|${file_uncovered_lines[$i]}" >> "$temp_file"
  done

  # Sort the file and read back
  sorted_data=$(sort -n -t'|' -k1 "$temp_file")

  # Clear arrays
  file_results=()
  file_coverage_values=()
  file_paths=()
  file_covered_lines=()
  file_total_lines=()
  file_uncovered_lines=()

  # Repopulate arrays with sorted data
  while IFS='|' read -r coverage result path covered_lines total_lines uncovered; do
    file_coverage_values+=("$coverage")
    file_results+=("$result")
    file_paths+=("$path")
    file_covered_lines+=("$covered_lines")
    file_total_lines+=("$total_lines")
    file_uncovered_lines+=("$uncovered")
  done <<< "$sorted_data"

  # Remove temp file
  rm "$temp_file"
fi

# Generate output based on format
output=""

case $output_format in
  "text")
    # Display results
    for i in "${!file_results[@]}"; do
      output+="${file_results[$i]}\n"

      # Show uncovered lines if requested and available
      if [ "$show_uncovered_lines" = true ] && [ -n "${file_uncovered_lines[$i]}" ]; then
        output+="   ${YELLOW}Uncovered lines:${NC} ${file_uncovered_lines[$i]}\n"
      fi
    done

    output+="\n${CYAN}===================================================${NC}\n"
    output+="Summary:\n"
    output+="  Total files checked: $total_files\n"
    output+="  Files below threshold: $files_below_threshold\n"
    output+="  Overall coverage: $overall_coverage% ($covered_lines_all/$total_lines_all lines)\n"
    output+="\n"

    if [ "$passed" = false ]; then
      output+="${RED}❌ Failed to meet file's coverage threshold${NC}\n"
    else
      output+="${GREEN}✅ All files meet coverage threshold${NC}\n"
    fi
    ;;

  "json")
    # Create JSON output
    output="{\n"
    output+="  \"summary\": {\n"
    output+="    \"total_files\": $total_files,\n"
    output+="    \"files_below_threshold\": $files_below_threshold,\n"
    output+="    \"overall_coverage\": $overall_coverage,\n"
    output+="    \"covered_lines\": $covered_lines_all,\n"
    output+="    \"total_lines\": $total_lines_all,\n"
    output+="    \"passed\": $passed\n"
    output+="  },\n"
    output+="  \"files\": [\n"

    for i in "${!file_paths[@]}"; do
      output+="    {\n"
      output+="      \"path\": \"${file_paths[$i]}\",\n"

      if [ "${file_coverage_values[$i]}" -eq -1 ]; then
        output+="      \"coverage\": null,\n"
        output+="      \"covered_lines\": null,\n"
        output+="      \"total_lines\": null,\n"
      else
        output+="      \"coverage\": ${file_coverage_values[$i]},\n"
        output+="      \"covered_lines\": ${file_covered_lines[$i]},\n"
        output+="      \"total_lines\": ${file_total_lines[$i]},\n"
      fi

      if [ -n "${file_uncovered_lines[$i]}" ]; then
        output+="      \"uncovered_lines\": [${file_uncovered_lines[$i]}],\n"
      else
        output+="      \"uncovered_lines\": [],\n"
      fi

      output+="      \"passed\": $(if [[ "${file_results[$i]}" == *"✅"* ]]; then echo "true"; else echo "false"; fi)\n"

      if [ $i -eq $((${#file_paths[@]} - 1)) ]; then
        output+="    }\n"
      else
        output+="    },\n"
      fi
    done

    output+="  ]\n"
    output+="}\n"
    ;;

  "html")
    # Create HTML output
    output="<!DOCTYPE html>\n<html>\n<head>\n"
    output+="  <title>Coverage Report</title>\n"
    output+="  <style>\n"
    output+="    body { font-family: Arial, sans-serif; margin: 20px; }\n"
    output+="    .pass { color: green; }\n"
    output+="    .fail { color: red; }\n"
    output+="    .warning { color: orange; }\n"
    output+="    table { border-collapse: collapse; width: 100%; }\n"
    output+="    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n"
    output+="    th { background-color: #f2f2f2; }\n"
    output+="    tr:nth-child(even) { background-color: #f9f9f9; }\n"
    output+="    .summary { margin-top: 20px; padding: 10px; background-color: #f2f2f2; }\n"
    output+="  </style>\n"
    output+="</head>\n<body>\n"
    output+="  <h1>Coverage Report</h1>\n"
    output+="  <table>\n"
    output+="    <tr>\n"
    output+="      <th>File</th>\n"
    output+="      <th>Coverage</th>\n"
    output+="      <th>Status</th>\n"
    if [ "$show_uncovered_lines" = true ]; then
      output+="      <th>Uncovered Lines</th>\n"
    fi
    output+="    </tr>\n"

    for i in "${!file_paths[@]}"; do
      output+="    <tr>\n"
      output+="      <td>${file_paths[$i]}</td>\n"

      if [ "${file_coverage_values[$i]}" -eq -1 ]; then
        output+="      <td class=\"warning\">N/A</td>\n"
        output+="      <td class=\"warning\">⚠️ Coverage unavailable</td>\n"
      else
        if (( $(echo "${file_coverage_values[$i]} < $coverage_threshold" | bc -l) )); then
          output+="      <td class=\"fail\">${file_coverage_values[$i]}% (${file_covered_lines[$i]}/${file_total_lines[$i]})</td>\n"
          output+="      <td class=\"fail\">❌ Below threshold</td>\n"
        else
          output+="      <td class=\"pass\">${file_coverage_values[$i]}% (${file_covered_lines[$i]}/${file_total_lines[$i]})</td>\n"
          output+="      <td class=\"pass\">✅ Passed</td>\n"
        fi
      fi

      if [ "$show_uncovered_lines" = true ]; then
        output+="      <td>${file_uncovered_lines[$i]}</td>\n"
      fi

      output+="    </tr>\n"
    done

    output+="  </table>\n"
    output+="  <div class=\"summary\">\n"
    output+="    <h2>Summary</h2>\n"
    output+="    <p>Total files checked: $total_files</p>\n"
    output+="    <p>Files below threshold: $files_below_threshold</p>\n"
    output+="    <p>Overall coverage: $overall_coverage% ($covered_lines_all/$total_lines_all lines)</p>\n"

    if [ "$passed" = false ]; then
      output+="    <p class=\"fail\">❌ Failed to meet file's coverage threshold</p>\n"
    else
      output+="    <p class=\"pass\">✅ All files meet coverage threshold</p>\n"
    fi

    output+="  </div>\n"
    output+="</body>\n</html>\n"
    ;;

  *)
    echo -e "${RED}Error:${NC} Unknown output format: $output_format"
    exit 1
    ;;
esac

# Output results
if [ -n "$output_file" ]; then
  echo -e "$output" > "$output_file"
  echo -e "${GREEN}Report written to $output_file${NC}"
else
  echo -e "$output"
fi

# Exit with appropriate status
if [ "$passed" = false ]; then
  exit 1
fi
exit 0
