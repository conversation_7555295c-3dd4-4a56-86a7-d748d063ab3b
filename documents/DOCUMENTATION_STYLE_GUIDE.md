# Documentation Style Guide

This document outlines the standards and best practices for documenting code in the Flutter Common Package. Following these guidelines ensures consistency and clarity across the codebase.

> **Important**: The Flutter Common Package is proprietary software owned by Trusting Social. All documentation should include appropriate proprietary notices and should not be shared outside of Trusting Social and its authorized partners and clients.

## Dartdoc Comments

### Class Documentation

Every public class should have a Dartdoc comment that includes:

1. A brief description of what the class does
2. A more detailed explanation of its purpose and functionality
3. Example usage code
4. See also references to related classes

Example:

```dart
/// A standardized button widget that follows the company's design system.
///
/// The [CommonButton] widget provides a consistent button implementation
/// that can be used across all projects. It wraps Flutter's [ElevatedButton]
/// with additional functionality for width control.
///
/// Example usage:
/// ```dart
/// CommonButton(
///   onPressed: () {
///     // Handle button press
///   },
///   style: getIt.get<CommonButtonStyles>().primaryButtonStyle,
///   child: Text('Submit'),
/// )
/// ```
///
/// See also:
///  * [CommonRadio], which provides a standardized radio button implementation.
class CommonButton extends StatelessWidget {
  // ...
}
```

### Property Documentation

Every public property should have a Dartdoc comment that explains:

1. What the property represents
2. How it affects the behavior of the class
3. Any constraints or requirements

Example:

```dart
/// Whether the button should wrap its content or take full width.
/// If true, the button will size itself to fit its content.
/// If false, the button will expand to fill the available width.
final bool isWrapContent;
```

### Constructor Documentation

Every public constructor should have a Dartdoc comment that explains:

1. What the constructor creates
2. Required parameters
3. Optional parameters and their default values
4. Any side effects

Example:

```dart
/// Creates a standardized button widget.
///
/// The [onPressed], [child], and [style] parameters are required.
/// The [isWrapContent] parameter defaults to true, making the button size itself to fit its content.
const CommonButton({
  required this.onPressed,
  required this.child,
  required this.style,
  super.key,
  this.isWrapContent = true,
});
```

### Method Documentation

Every public method should have a Dartdoc comment that explains:

1. What the method does
2. Parameters and their purpose
3. Return value and its meaning
4. Any side effects or exceptions

Example:

```dart
/// Validates the input against the specified pattern.
///
/// The [input] is the string to validate.
/// The [pattern] is the regular expression to match against.
///
/// Returns true if the input matches the pattern, false otherwise.
///
/// Throws [ArgumentError] if the pattern is invalid.
bool validate(String input, String pattern) {
  // Implementation
}
```

## Private Members

While private members (those prefixed with `_`) are not included in generated documentation, it's still good practice to document them for maintainability:

```dart
/// Builds the radio button circle with appropriate styling based on selection state.
/// Uses the company's design system colors for consistent appearance.
Widget _itemRadio() {
  // Implementation
}
```

## Code Examples

Code examples should:

1. Be concise but complete enough to understand
2. Use realistic variable and function names
3. Include comments for complex parts
4. Be properly indented and formatted

## References

When referencing other classes or methods, use square brackets:

```dart
/// See also:
///  * [CommonButton], which provides a standardized button implementation.
```

## Formatting

- Use triple slashes (`///`) for Dartdoc comments
- Leave a blank line between the description and other sections
- Use bullet points (`*`) for lists
- Use backticks (`` ` ``) for inline code references
- Use triple backticks (`` ``` ``) for code blocks

## Proprietary Notices

All source files should include a proprietary notice at the top of the file. For Dart files, use the following format:

```dart
// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
```

## Documentation Generation

To generate documentation:

```bash
dart doc .
```

This will create documentation in the `doc/api` directory.

> **Note**: Generated documentation is for internal use only and should not be published to public websites or repositories.
