# Modular System V2

This document describes the improved modular feature system implemented in the Flutter Common Package. The system allows for selective initialization of features based on the application's needs, improving startup time, reducing resource usage, and enhancing testability.

> **Important**: The Flutter Common Package is proprietary software owned by Trusting Social. All documentation should include appropriate proprietary notices and should not be shared outside of Trusting Social and its authorized partners and clients.

## Overview

The modular feature system is built around the concept of feature modules. Each module is responsible for registering a specific set of dependencies that are related to a particular feature or functionality. Modules can depend on other modules, and the system ensures that dependencies are initialized in the correct order.

## Key Components

### FeatureModule

The `FeatureModule` interface defines the contract for all feature modules. Each module must implement:

- `name`: A unique identifier for the module
- `dependencies`: A list of types that the module provides
- `register`: A method to register the module's dependencies

```dart
abstract class FeatureModule {
  String get name;
  List<Type> get dependencies;
  Future<void> register(GetIt getIt);
}
```

### ModuleRegistry and ModuleRegistryBuilder

The modular system now uses a builder pattern for registering modules:

```dart
// Create a registry builder
final builder = ModuleRegistry.builder(GetIt.instance);

// Register modules
builder.register(CoreModule(), source: 'common_package')
       .register(NetworkModule(), source: 'common_package')
       .register(CustomModule(), source: 'host_app');

// Build the registry (validation happens automatically)
final registry = builder.build();

// Initialize modules
await registry.initializeModules();
```

This approach provides several benefits:
- Clear separation between configuration and usage phases
- Automatic validation during the build phase
- Immutability after building
- Prevention of runtime errors

### EventBus

The system now includes an `EventBus` for communication between modules:

```dart
// Register an event listener
final eventBus = getIt<EventBus>();
eventBus.on<UserLoggedInEvent>().listen((event) {
  // Handle the event
  print('User logged in: ${event.userId}');
});

// Fire an event
eventBus.fire(UserLoggedInEvent('user123'));
```

This helps break circular dependencies and promotes loose coupling between modules.



## Available Modules

The Flutter Common Package includes the following modules:

1. **CoreModule**: Essential dependencies like device information, storage, and utility functions
2. **NetworkModule**: Networking dependencies like HTTP clients and connectivity management
3. **AnalyticsModule**: Analytics and logging dependencies
4. **DataCollectionModule**: Data collection and device identification
5. **NotificationModule**: Push notification services
6. **UiModule**: UI components and utilities
7. **UtilityModule**: Utility functions, downloaders, in-app review, and helpers
8. **EkycModule**: Electronic Know Your Customer services

## Usage

### Basic Usage

To use the modular feature system, use the `features` parameter with `initCommonPackage`:

```dart
import 'package:flutter_common_package/base/module/module_names.dart';

// Initialize all features
await initCommonPackage();

// Initialize only specific features
await initCommonPackage(
  features: [CommonPackageModuleNames.core, CommonPackageModuleNames.network, CommonPackageModuleNames.ui],
);

// Auto-initialize all features
await initCommonPackage(autoInitialize: true);

// Auto-initialize specific features
await initCommonPackage(
  features: [CommonPackageModuleNames.core, CommonPackageModuleNames.network, CommonPackageModuleNames.ui],
  autoInitialize: true,
);
```

### Module Initialization

The modular system provides clear, explicit methods for initializing modules:

```dart
// Register modules and initialize them in one step
await initCommonPackage(
  features: [CommonPackageModuleNames.core, CommonPackageModuleNames.ui],
  autoInitialize: true,
);

// Or initialize modules manually after registration
await initCommonPackage(); // Just register modules without initializing

// Initialize all registered modules
await initializeAllModules();

// Initialize specific modules
await initializeSpecificModules([CommonPackageModuleNames.core, CommonPackageModuleNames.ui]);

// Explicitly initialize no modules (no-op)
await initializeNoModules();
```

The initialization API is designed to be clear and explicit:

- `initializeAllModules()`: Initializes all registered modules
- `initializeSpecificModules(moduleNames)`: Initializes only the specified modules
- `initializeNoModules()`: Explicitly does nothing (useful for conditional initialization)

This approach makes the code more readable and self-documenting, as the intent is clear from the method name.

When using `initCommonPackage`:
- Set `autoInitialize: true` to automatically initialize modules after registration
- Use the `features` parameter to specify which modules to register
- If `features` is null, all modules will be registered
- If `features` is empty, no modules will be registered

The initialization process respects module dependencies, ensuring that if module A depends on module B, module B will be initialized first.

### Creating Custom Modules

You can create custom modules for your application by implementing the `FeatureModule` interface:

```dart
class CustomAuthModule implements FeatureModule {
  @override
  String get name => 'auth';

  @override
  List<Type> get dependencies => [AuthService];

  @override
  Future<void> register(GetIt getIt) async {
    getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl());
  }
}
```

Then register your custom module using the builder pattern:

```dart
// Create a registry builder
final builder = ModuleRegistry.builder(GetIt.instance);

// Register your custom module
builder.register(CustomAuthModule(), source: 'host_app');

// Build the registry (validation happens automatically)
final registry = builder.build();

// Initialize it
await registry.initializeModule('auth');
```

### Module Name Uniqueness

The `source` parameter in `register` is required and helps ensure module name uniqueness across different sources (e.g., 'common_package', 'host_app'). This prevents naming conflicts when modules from different sources use the same name.

The system automatically validates module names during the build phase:

```dart
// Register modules from different sources
final builder = ModuleRegistry.builder(GetIt.instance);
builder.register(CoreModule(), source: 'common_package')
       .register(CustomAuthModule(), source: 'host_app');

// Validation happens automatically when building
final registry = builder.build();
```

### Avoiding Circular Dependencies

The system now detects circular dependencies during the build phase:

```dart
// This will throw an exception if there's a circular dependency
final registry = builder.build();
```

To avoid circular dependencies, consider these approaches:

1. **Use the EventBus**: For loose coupling between modules
2. **Restructure module boundaries**: Ensure clear separation of concerns
3. **Use dependency injection**: Inject dependencies rather than creating them directly

Example using EventBus:

```dart
class UserModule implements FeatureModule {
  @override
  Future<void> register(GetIt getIt) async {
    final eventBus = getIt<EventBus>();

    getIt.registerLazySingleton<UserService>(() => UserServiceImpl());

    // Listen for analytics events
    eventBus.on<AnalyticsReadyEvent>().listen((_) {
      // Now we can safely use AnalyticsService
      final analyticsService = getIt<AnalyticsService>();
      // Use the service
    });
  }
}
```

## Benefits

The modular feature system provides several benefits:

1. **Reduced Startup Time**: Initialize only the features your app needs
2. **Lower Resource Usage**: Avoid loading unnecessary dependencies
3. **Improved Testability**: Easily mock or replace modules for testing
4. **Better Dependency Management**: Clear dependencies between modules
5. **Easier Extension**: Add new features without modifying existing code
6. **Automatic Validation**: Catches configuration errors early
7. **Clear API**: The builder pattern makes the registration process explicit and intentional


## Conclusion

The improved modular system provides a more robust, maintainable, and error-resistant approach to managing dependencies in your application. By following the principles outlined in this document, you can create applications that are easier to develop, test, and maintain.
