@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
participant "**HostApp**" as app
participant "**Face-Authen <PERSON>**" as faModule
participant "**API Gateway**" as be
participant "**eKYC SDK**" as ekycSDK

title Face Authentication Module

== Initialize SDK ==
note right of app
    Params:
    - **session_token**: optional
end note
app -> faModule: call **initialize** function

faModule -> be: GET /ekyc/client-settings
be --> faModule: response

alt #Tomato statusCode != 200
    faModule --> app: finish flow with error data
else #LightGreen statusCode == 200
    faModule --> faModule: continue flow
end alt

faModule -> ekycSDK: init eKYC SDK (UI Only) with **client_settings**
ekycSDK --> faModule: result

alt #LightGreen result is success
    faModule --> app: return success result, host app can call **startCapturing** to continue
else #Tomato result is fail
    faModule --> app: finish flow with error data
end alt

== Selfie Capturing ==

note right of app
    Params:
    - **liveness_model**: required
end note
app -> faModule: call **startCapturing** function

alt #Tomato SDK isn't initialized
    faModule --> app: finish flow with error data (not initialized)
else #LightGreen SDK is initialized
    faModule --> faModule: continue flow
end alt

faModule -> ekycSDK: start selfie capturing
loop #LightGray
    ekycSDK --> faModule: batch frames data
    faModule -> be: POST POST /ekyc/frames
    be --> faModule: response

    alt #LightGreen statusCode == 200
        faModule -> faModule: save file_ids to temporary variable
    end alt

end loop

ekycSDK -> faModule: selfie capture result

alt result is success
    note right faModule
        - in some liveness mode, SDK can restart capturing process,
        therefore, all batch frames will contain invalid data and need to be removed
        refer: [[https://ekyc.trustingsocial.com/sdks/React-Native-SDK#3341-remove-invalid-frame-batch-ids remove-invalid-frame-batch-ids]]
    end note
    faModule -> faModule: remove invalid batch frames

    note right faModule
        - handle logic for direction of image , e.g. frontal, frontal.main, left, ...
    end note
    faModule -> faModule: processing captured images

    faModule -> be: POST POST /ekyc/images
    be --> faModule: response

    alt #LightGreen statusCode == 200
        faModule -> faModule: go to step **Verify Face**
    else #Tomato statusCode != 200
        faModule --> app: finish flow with error data
    end alt

else #Tomato result is fail
    faModule --> app: finish flow with error data
end alt

== Verify Face ==
note right app
    Host App will handle success data and call API to verify face
    - **image_ids**: list of image ids
    - **file_ids**: list of frame batch ids
end note
faModule --> app: finish flow with success data (image_ids, file_ids, ...)

@enduml