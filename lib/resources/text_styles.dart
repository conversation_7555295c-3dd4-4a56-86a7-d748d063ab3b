// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../init_common_package.dart';
import 'resources.dart';

class CommonTextStyles {
  final String fontFamily;

  CommonTextStyles({required this.fontFamily});

  static const double fontSizeBodySmall = 12;
  static const double fontSizeH400 = 20;
  static const double fontSizeH600 = 30;
  final CommonColors commonColors = getIt.get<CommonColors>();

  // H700
  TextStyle h700(Color color) =>
      TextStyle(fontFamily: fontFamily, color: color, fontSize: 40, fontWeight: FontWeight.w700);

  // H600
  TextStyle h600(Color color) => TextStyle(
      fontFamily: fontFamily, color: color, fontSize: fontSizeH600, fontWeight: FontWeight.w700);

  // H500
  TextStyle h500({Color? color}) => TextStyle(
      fontFamily: fontFamily,
      color: color ?? commonColors.textActive,
      fontSize: 24,
      fontWeight: FontWeight.w700);

  // H400
  TextStyle h400({Color? color}) => TextStyle(
      fontFamily: fontFamily,
      color: color ?? commonColors.textActive,
      fontSize: fontSizeH400,
      fontWeight: FontWeight.w700);

  // H300
  TextStyle h300({Color? color}) => TextStyle(
      fontFamily: fontFamily,
      color: color ?? commonColors.textActive,
      fontSize: 16,
      fontWeight: FontWeight.w700);

  // H200
  TextStyle h200({Color? color}) => TextStyle(
      fontFamily: fontFamily,
      color: color ?? commonColors.textActive,
      fontSize: 14,
      fontWeight: FontWeight.w700);

  // H200
  TextStyle h100({Color? color}) => TextStyle(
      fontFamily: fontFamily,
      color: color ?? commonColors.textPassive,
      fontSize: 12,
      fontWeight: FontWeight.w700);

  TextStyle bodyLarge(Color color) =>
      TextStyle(color: color, fontFamily: fontFamily, fontSize: 16, fontWeight: FontWeight.w400);

  TextStyle button(ButtonSize size, Color color, {double? fontSize}) {
    switch (size) {
      case ButtonSize.small:
        return _btnBodySmall(color, fontSize: fontSize);
      case ButtonSize.medium:
        return _btnBodyMedium(color, fontSize: fontSize);
      case ButtonSize.large:
        return _btnBodyLarge(color, fontSize: fontSize);
      case ButtonSize.xLarge:
        return _btnBodyLarge(color, fontSize: fontSize);
    }
  }

  TextStyle _btnBodyLarge(Color color, {double? fontSize}) => TextStyle(
        color: color,
        fontFamily: fontFamily,
        fontSize: fontSize ?? 16,
        fontWeight: FontWeight.w700,
      );

  TextStyle _btnBodyMedium(Color color, {double? fontSize}) => TextStyle(
        color: color,
        fontFamily: fontFamily,
        fontSize: fontSize ?? 14,
        fontWeight: FontWeight.w700,
      );

  TextStyle _btnBodySmall(Color color, {double? fontSize}) => TextStyle(
        color: color,
        fontFamily: fontFamily,
        fontSize: fontSize ?? 12,
        fontWeight: FontWeight.w700,
      );

  TextStyle bodyMedium(Color color,
          {TextDecoration? decoration, FontStyle fontStyle = FontStyle.normal}) =>
      TextStyle(
          color: color,
          decoration: decoration,
          fontFamily: fontFamily,
          fontSize: 14,
          fontStyle: fontStyle,
          fontWeight: FontWeight.w500);

  TextStyle get bodyMediumHighlighted =>
      bodyMedium(commonColors.highlighted, decoration: TextDecoration.underline);

  TextStyle bodySmall({Color? color}) => TextStyle(
      color: color ?? commonColors.textPassive,
      fontFamily: fontFamily,
      fontSize: fontSizeBodySmall,
      fontWeight: FontWeight.w500);

  TextStyle bodyXSmall({Color? color}) => TextStyle(
      color: color ?? commonColors.textPassive,
      fontFamily: fontFamily,
      fontSize: 10,
      fontWeight: FontWeight.w500);
}
