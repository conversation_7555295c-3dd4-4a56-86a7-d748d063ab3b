// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

// ignore_for_file: constant_identifier_names
class HeaderKey {
  static const String appVersion = 'X-APP-VERSION';
  static const String timeZoneOffset = 'X-TIME-ZONE-OFFSET';
  static const String timeZone = 'X-TIME-ZONE';
  static const String deviceId = 'X-DEVICE-ID';
  static const String deviceModel = 'X-DEVICE-MODEL';
  static const String platform = 'X-PLATFORM';
  static const String osVersion = 'X-OS-VERSION';
  static const String appBuildNumber = 'X-APP-BUILD-NUMBER';
  static const String osBuildNumber = 'X-OS-BUILD-NUMBER';
  static const String language = 'X-LANGUAGE';
  static const String sessionToken = 'X-SESSION';
}

/// Package name
const String FLUTTER_COMMON_PACKAGE = 'flutter_common_package';

/// number of items in each page
const int defaultNumberItemPerPage = 10;
const int defaultFirstPage = 1;

/// Logging
const String buttonNameUserActionKey = 'button_name';
const String argumentUserActionKey = 'argument';

enum CommonScreen {
  webViewPage(webViewPageName);

  /// DO NOT change the value of these fields without confirm with backend
  static const String webViewPageName = 'web_view_page';

  static CommonScreen? byValue(String value) {
    switch (value) {
      case webViewPageName:
        return webViewPage;
      default:
        return null;
    }
  }

  const CommonScreen(this.name);

  final String name;

  String get routeName => '/$name';
}
