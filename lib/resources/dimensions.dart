// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/widgets.dart';

class CommonDimensions {
  static const double commonCornerRadius = 16.0;
  static const double buttonCornerRadius = 99.0;
  static const double topBarHeight = 108.0;
}

enum ButtonSize {
  small,
  medium,
  large,
  xLarge;

  /// Padding top is more than padding bottom 1 pixel to balance the text
  /// because the ProximaSoft font is placed closer to the top
  EdgeInsets get padding {
    switch (this) {
      case small:
        return const EdgeInsets.only(left: 12, right: 12, top: 9, bottom: 8);
      case ButtonSize.medium:
        return const EdgeInsets.only(left: 20, right: 20, top: 11, bottom: 10);
      case ButtonSize.large:
        return const EdgeInsets.only(left: 24, right: 24, top: 13, bottom: 12);
      case ButtonSize.xLarge:
        return const EdgeInsets.only(left: 24, right: 24, top: 17, bottom: 16);
    }
  }
}

EdgeInsets paddingFromSize(Size size) =>
    EdgeInsets.symmetric(vertical: size.height, horizontal: size.width);
