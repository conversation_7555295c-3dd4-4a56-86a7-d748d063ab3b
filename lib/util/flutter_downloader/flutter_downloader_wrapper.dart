// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';
import 'package:flutter_downloader/flutter_downloader.dart';

class FlutterDownloaderWrapper {
  Future<void> initialize({bool? debug}) async {
    // Init plugin flutter_downloader
    // Refer: https://pub.dev/packages/flutter_downloader#usage
    await FlutterDownloader.initialize(debug: debug ?? kDebugMode);
  }

  /// Register callback to process download status
  ///
  /// This callback must be top-level function or static function
  ///
  /// Code in callback will run on background isolate, so we have to handle comunication
  /// between background isolate and man thread via own isolate
  ///
  /// Refer docs: https://pub.dev/packages/flutter_downloader#update-download-progress
  Future<void> registerCallback(
    DownloadCallback callback, {
    int step = 10, // 10: default of flutter_downloader library
  }) async {
    await FlutterDownloader.registerCallback(callback, step: step);
  }

  /// Each download progress will be enqueued,
  /// and processed by [WorkManager] on Android and [NSURLSessionDownloadTask] on iOS to run download tasks in background.
  ///
  /// The maximum number of tasks running at a moment default is 3
  /// That mean we can download 3 file in parallel
  ///
  /// The maximum download parallel can configure follow docs
  /// [android]: https://pub.dev/packages/flutter_downloader#configure-maximum-number-of-concurrent-download-tasks
  /// [ios]: https://pub.dev/packages/flutter_downloader#optional-configuration
  ///
  Future<String?> enqueue({
    required String url,
    required String savedDir,
    String? fileName,
    Map<String, String> headers = const <String, String>{},
    bool showNotification = true,
    bool openFileFromNotification = true,
    bool requiresStorageNotLow = true,
    bool saveInPublicStorage = false,
    bool allowCellular = true,
    int timeout = 15000,
  }) async {
    return FlutterDownloader.enqueue(
      url: url,
      headers: headers,
      savedDir: savedDir,
      showNotification: showNotification,
      openFileFromNotification: openFileFromNotification,
      requiresStorageNotLow: requiresStorageNotLow,
      saveInPublicStorage: saveInPublicStorage,
      fileName: fileName,
      allowCellular: allowCellular,
      timeout: timeout,
    );
  }

  /// Opens the file downloaded.
  /// Returns true if the downloaded file can be opened, false otherwise.
  Future<bool> open({required String taskId}) async {
    return FlutterDownloader.open(taskId: taskId);
  }
}
