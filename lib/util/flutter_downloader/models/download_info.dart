// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

class DownloadInfo {
  final String id;
  final CommonDownloadTaskStatus status;
  final int progress;

  DownloadInfo({
    required this.id,
    required this.status,
    required this.progress,
  });

  DownloadInfo.failed({
    this.id = '',
    this.status = CommonDownloadTaskStatus.failed,
    this.progress = 0,
  });
}

enum CommonDownloadTaskStatus {
  /// Status of the task is either unknown or corrupted.
  undefined(0),

  /// The task is scheduled, but is not running yet.
  enqueued(1),

  /// The task is in progress.
  running(2),

  /// The task has completed successfully.
  complete(3),

  /// The task has failed.
  failed(4),

  /// The task was canceled and cannot be resumed.
  canceled(5),

  /// The task was paused and can be resumed.
  paused(6);

  final int value;

  const CommonDownloadTaskStatus(this.value);

  static CommonDownloadTaskStatus fromValue(int value) {
    switch (value) {
      case 0:
        return undefined;
      case 1:
        return enqueued;
      case 2:
        return running;
      case 3:
        return complete;
      case 4:
        return failed;
      case 5:
        return canceled;
      case 6:
        return paused;
      default:
        return undefined;
    }
  }
}
