// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

/// iOS Device: OS detect OTP automatically when user receive SMS & show OTP code in keyboard
/// reference: https://developer.apple.com/documentation/security/password_autofill/about_the_password_autofill_workflow
/// Android Device: OS request user permission to read SMS content if user ALLOW, application will extract code from SMS content and return to app
/// reference: https://developers.google.com/identity/sms-retriever/user-consent/overview
abstract class OtpAutoFill {
  /// this function just work on Android Device
  Future<void> startUserConsentListening({
    required String Function(String?) onExtractCode,
    required void Function(String) onCode,
    void Function(OtpAutoFillException e) onError,
    String? senderNumber,
    bool autoStop = true,
  });

  /// this function just work on Android Device
  Future<void> stopListening();
}

enum OtpAutoFillExceptionReason { timeout, nativePlatform, unsupported, others }

class OtpAutoFillException implements Exception {
  static const String unknownMessage = 'unknown';

  OtpAutoFillExceptionReason reason;
  String? message;

  OtpAutoFillException({required this.reason, this.message});

  @override
  String toString() {
    return 'OtpAutoFillException{reason: $reason, message: $message}';
  }
}
