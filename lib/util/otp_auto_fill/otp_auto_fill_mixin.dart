// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../../data/repository/logging/logging_repo.dart';
import '../../init_common_package.dart';
import '../device_platform.dart';
import 'otp_auto_fill.dart';

abstract class OtpAutoFillListener {
  void onOtpCodeReceived(String code);

  void onOtpAutoFillError(OtpAutoFillException e);
}

mixin OtpAutoFillMixin {
  /// find out 6 digit code from sms content
  final String defaultSMSCodePattern = r'(\d{6})';

  final OtpAutoFill _autoFill = getIt.get<OtpAutoFill>();

  final DevicePlatform deviceNativePlatform = getIt.get<DevicePlatform>();

  final LoggingRepo loggingRepo = getIt.get<LoggingRepo>();

  OtpAutoFillListener? otpAutoFillListener;

  /// this function just work on Android Device
  /// for Android Device: call [_autoFill.startUserConsentListening] to request User <PERSON>sent to read & extract OTP code from SMS
  /// iOS don't support parse OTP code from SMS code, OS will automatically extract OTP code from SMS & show above virtual keyboard
  void listenForCode({String? smsCodeRegexPattern}) {
    if (deviceNativePlatform.isAndroid()) {
      _autoFill.startUserConsentListening(
          onExtractCode: createExtractCode(smsCodeRegexPattern: smsCodeRegexPattern),
          onCode: (String code) {
            otpAutoFillListener?.onOtpCodeReceived(code);
          },
          onError: (OtpAutoFillException e) {
            loggingRepo.logErrorEvent(
              errorType: EventType.otpAutoFill.name,
              args: <String, dynamic>{'method_name': 'listenForCode', 'error': e.toString()},
            );
            otpAutoFillListener?.onOtpAutoFillError(e);
          });
    }
  }

  String Function(String?) createExtractCode({String? smsCodeRegexPattern}) {
    final RegExp exp = RegExp(smsCodeRegexPattern ?? defaultSMSCodePattern);
    return (String? smsContent) => exp.stringMatch(smsContent ?? '') ?? '';
  }

  /// this function just work on Android Device.
  /// iOS don't support parse OTP code from SMS code
  Future<void> stopListening() {
    if (deviceNativePlatform.isAndroid()) {
      return _autoFill.stopListening();
    } else {
      return Future<void>.value();
    }
  }
}
