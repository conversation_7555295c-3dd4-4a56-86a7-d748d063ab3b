// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:synchronized/synchronized.dart';

import 'extension.dart';
import 'local_storage_helper.dart';

class CommonSecureStorageHelperImpl implements CommonLocalStorageHelper {
  @visibleForTesting
  @protected
  FlutterSecureStorage secureStorage;

  /// If [isAllDataLoadedIntoMemory] is true, then the all data is loaded into memory
  @visibleForTesting
  bool isAllDataLoadedIntoMemory = false;

  @visibleForTesting
  final Map<String, String?> memoryData = <String, String?>{};

  final Lock _loadAllLocalDataLock = Lock();

  CommonSecureStorageHelperImpl({required this.secureStorage});

  /// To reduce the time of reading value from secure storage,
  /// this method should be called ONCE in the application lifecycle, before any [read]
  @override
  Future<void> loadAllDataIntoMemory() async {
    await _loadAllLocalDataLock.synchronized(_handleLoadLocalDataIntoMemory);
  }

  Future<void> _handleLoadLocalDataIntoMemory() async {
    if (isAllDataLoadedIntoMemory) return;

    final Map<String, String> localData = await secureStorage.readAll();

    memoryData.clear();
    memoryData.addAll(localData);

    commonLog('loadDataIntoMemory ${memoryData.length}');
    isAllDataLoadedIntoMemory = true;
  }

  @override
  Future<bool> containsKey({required String key}) async {
    await loadKeyIntoMemoryIfNeeded(key: key);
    return memoryData.containsKey(key);
  }

  @override
  Future<void> write({required String key, required String? value}) async {
    await secureStorage.write(key: key, value: value);
    memoryData[key] = value;
  }

  @override
  Future<String?> read({required String key}) async {
    await loadKeyIntoMemoryIfNeeded(key: key);
    return memoryData[key];
  }

  @visibleForTesting
  Future<void> loadKeyIntoMemoryIfNeeded({required String key}) async {
    if (await isNeedToLoadKeyIntoMemory(key: key)) {
      memoryData[key] = await secureStorage.read(key: key);
    }
  }

  /// Return True when the key is available in local storage, but not in the memory.
  /// checking [isAllDataLoadedIntoMemory] first to so if [loadAllDataIntoMemory] is already called,
  /// then we don't have to check it in local storage (which is cost much time)
  @visibleForTesting
  Future<bool> isNeedToLoadKeyIntoMemory({required String key}) async {
    return !isAllDataLoadedIntoMemory &&
        !memoryData.containsKey(key) &&
        await secureStorage.containsKey(key: key);
  }

  @override
  Future<bool> delete({required String key}) async {
    // only delete if the key is available
    final bool isKeyAvailable = await containsKey(key: key);
    if (!isKeyAvailable) {
      return false;
    }

    await secureStorage.delete(key: key);
    memoryData.remove(key);
    return true;
  }

  @override
  Future<void> deleteAllData() async {
    await secureStorage.deleteAll();
    memoryData.clear();
  }

  @override
  Future<Map<String, String?>> readAll() async {
    if (!isAllDataLoadedIntoMemory) {
      await loadAllDataIntoMemory();
    }
    return memoryData;
  }
}
