// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../../resources/colors.dart';
import '../../resources/text_styles.dart';
import '../../resources/ui_strings.dart';
import 'count_down_timer_widget.dart';

class CountDownAndResendOtpWidget extends StatefulWidget {
  final CommonColors commonColors;
  final CommonTextStyles commonTextStyles;
  final String? textOtpResendText;
  final String? textOtpDoNotReceived;
  final VoidCallback? onResendOtp;
  final VoidCallback? onFinish;
  final int resendInSec;

  const CountDownAndResendOtpWidget({
    required this.commonColors,
    required this.commonTextStyles,
    required this.resendInSec,
    super.key,
    this.onResendOtp,
    this.textOtpResendText,
    this.textOtpDoNotReceived,
    this.onFinish,
  });

  @override
  State<CountDownAndResendOtpWidget> createState() => CountDownAndResendOtpWidgetState();
}

class CountDownAndResendOtpWidgetState extends State<CountDownAndResendOtpWidget> {
  @visibleForTesting
  bool isCountDownFinish = false;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Flexible(
          fit: FlexFit.tight,
          child: Text(
            widget.textOtpDoNotReceived ?? CommonStrings.otpDoNotReceived,
            style: widget.commonTextStyles.bodyLarge(widget.commonColors.textPassive),
          ),
        ),
        _itemCountDownTimer(),
        _buildItemResendOtp(),
      ],
    );
  }

  Widget _itemCountDownTimer() {
    if (isCountDownFinish) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        CountDownTimerWidget(
          durationInSec: widget.resendInSec,
          onFinish: () {
            setCountDownFinish(true);
          },
        ),
        Text(' - ', style: widget.commonTextStyles.bodyLarge(widget.commonColors.textPassive)),
      ],
    );
  }

  void setCountDownFinish(bool isFinish) {
    setState(() {
      isCountDownFinish = isFinish;
      widget.onFinish?.call();
    });
  }

  Widget _buildItemResendOtp() {
    final Color color =
        isCountDownFinish ? widget.commonColors.highlighted : widget.commonColors.textPassive;
    return InkWell(
      onTap: _onTapResendOtp,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Text(widget.textOtpResendText ?? CommonStrings.otpResend,
            style: widget.commonTextStyles.bodyLarge(color)),
      ),
    );
  }

  void _onTapResendOtp() {
    // do nothing when not finish count down
    if (!isCountDownFinish) {
      return;
    }
    isCountDownFinish = false;
    widget.onResendOtp?.call();
  }
}
