// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../feature/keyboard_visible/keyboard_controller.dart';
import '../../init_common_package.dart';
import '../../resources/resources.dart';
import 'common_pin_theme.dart';
import 'common_pin_code_fields.dart';
import 'pin_code_utils.dart';

class CommonPinCode extends StatefulWidget {
  final int? pinLength;
  final void Function(String)? onSubmit;
  final void Function(String)? onChange;
  final TextEditingController textController;
  final bool showCursor;
  final bool autoFocus;
  final bool isClearOnTouch;

  /// This defines how the elements in the pin code field align. Default to [MainAxisAlignment.spaceBetween]
  final MainAxisAlignment mainAxisAlignment;

  /// Enabled Color fill for individual pin fields, default is [false]
  final bool enableActiveFill;

  /// The style of the text, default is [CommonTextStyles.h700(CommonColors.textActive)]
  final TextStyle? textStyle;

  /// The color of the cursor, default is [CommonColors.inputFocusedColor]
  final Color? cursorColor;

  /// Position of cursor, default is [CursorPosition.center]
  final CursorPosition cursorPosition;

  final CommonPinTheme? pinTheme;

  /*
  * true: auto hide keyboard when tap/complete input
  * false: don't hide keyboard
  * default autoFocus = true
  * */
  final bool autoUnFocus;
  final Widget? obscuringWidget;
  final String? hintCharacter;
  final double? cursorWidth;
  final double? cursorHeight;
  final Duration animationDuration;
  final bool autoDismissKeyboard;
  final FocusNode? focusNode;
  final bool enablePinAutofill;
  final BorderBuilder? borderBuilder;
  final BorderRadiusBuilder? borderRadiusBuilder;
  final IndexedWidgetBuilder? separatorBuilder;

  /// When provided [separatorBuilder] make sure to set [spaceBetweenItems] equal the width of the separator
  final double spaceBetweenItems;

  const CommonPinCode(
      {required this.textController,
      super.key,
      this.pinLength,
      this.onSubmit,
      this.onChange,
      this.isClearOnTouch = true,
      this.autoUnFocus = false,
      this.showCursor = false,
      this.autoFocus = true,
      this.obscuringWidget,
      this.hintCharacter,
      this.cursorWidth,
      this.cursorHeight,
      this.animationDuration = const Duration(milliseconds: 150),
      this.autoDismissKeyboard = true,
      this.focusNode,
      this.spaceBetweenItems = 8,
      this.mainAxisAlignment = MainAxisAlignment.spaceBetween,
      this.pinTheme,
      this.enableActiveFill = false,
      this.textStyle,
      this.cursorColor,
      this.cursorPosition = CursorPosition.center,
      this.enablePinAutofill = false,
      this.separatorBuilder,
      this.borderBuilder,
      this.borderRadiusBuilder});

  @override
  State<CommonPinCode> createState() => _CommonPinCodeState();
}

class _CommonPinCodeState extends State<CommonPinCode> {
  final CommonColors commonColors = getIt.get<CommonColors>();
  final CommonTextStyles commonTextStyles = getIt.get<CommonTextStyles>();
  final CommonKeyboardController _keyboardController = CommonKeyboardController();

  @visibleForTesting
  double? pinCodeTextFieldWidth;

  @override
  void initState() {
    super.initState();
    _keyboardController.register();
  }

  @override
  void dispose() {
    _keyboardController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.pinTheme?.fieldHeight ?? PinCodeUtils.defaultTextFieldHeight,
      child: _keyboardController.initKeyboardProvider(
        child: LayoutBuilder(builder: (BuildContext context, BoxConstraints boxConstraints) {
          getWidthPinCodeItemByPinLengthAndSpace(boxConstraints.maxWidth);
          final CommonPinTheme pinTheme = initPinTheme();

          return CommonPinCodeFields(
            appContext: context,
            showCursor: widget.showCursor,
            cursorColor: widget.cursorColor ?? commonColors.inputFocusedColor,
            cursorWidth: widget.cursorWidth ?? 2,
            cursorHeight: widget.cursorHeight ?? 0,
            obscureText: widget.obscuringWidget != null,
            obscuringWidget: widget.obscuringWidget,
            hintCharacter: widget.hintCharacter,
            animationDuration: widget.animationDuration,
            focusNode: widget.focusNode,
            length: pinLength,
            pinTheme: pinTheme.toPinTheme(),
            textStyle: widget.textStyle ?? commonTextStyles.h700(commonColors.textActive),
            animationType: AnimationType.none,
            keyboardType: TextInputType.number,
            inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly],
            errorTextSpace: 8,
            autoFocus: widget.autoFocus,
            controller: widget.textController,
            onTap: () {
              if (widget.isClearOnTouch) {
                widget.textController.clear();
              }
            },
            autoUnfocus: widget.autoUnFocus,
            onCompleted: (String verificationCode) => widget.onSubmit?.call(verificationCode),
            onChanged: (String value) => widget.onChange?.call(value),
            beforeTextPaste: (String? text) {
              //if you return true then it will show the paste confirmation dialog. Otherwise if false, then nothing will happen.
              //but you can show anything you want here, like your pop up saying wrong paste format or etc
              return false;
            },
            autoDismissKeyboard: widget.autoDismissKeyboard,
            mainAxisAlignment: widget.mainAxisAlignment,
            separatorBuilder: widget.separatorBuilder ??
                (BuildContext context, int index) {
                  return SizedBox(width: widget.spaceBetweenItems);
                },
            enableActiveFill: widget.enableActiveFill,
            cursorPosition: widget.cursorPosition,
            enablePinAutofill: widget.enablePinAutofill,
            borderBuilder: widget.borderBuilder,
            borderRadiusBuilder: widget.borderRadiusBuilder,
          );
        }),
      ),
    );
  }

  void getWidthPinCodeItemByPinLengthAndSpace(double maxWidth) {
    if (pinCodeTextFieldWidth != null) {
      return;
    }

    final double? fieldWidth = widget.pinTheme?.fieldWidth;
    if (fieldWidth != null) {
      pinCodeTextFieldWidth = fieldWidth;
      return;
    }

    final double getWidthWidget = PinCodeUtils.instance.getPinCodeTextFieldWidth(
      maxWidth: maxWidth,
      maxPinCodeLength: pinLength,
      spaceBetweenItems: widget.spaceBetweenItems,
    );
    pinCodeTextFieldWidth = getWidthWidget;
  }

  @visibleForTesting
  CommonPinTheme initPinTheme() {
    final CommonPinTheme pinTheme = widget.pinTheme ?? CommonPinTheme();

    return pinTheme.copyWith(fieldWidth: pinCodeTextFieldWidth);
  }

  int get pinLength => widget.pinLength ?? PinCodeUtils.defaultMaxPinLength;
}
