// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

/// A standardized button widget that follows the company's design system.
///
/// The [CommonButton] widget provides a consistent button implementation
/// that can be used across all projects. It wraps Flutter's [ElevatedButton]
/// with additional functionality for width control.
///
/// Example usage:
/// ```dart
/// CommonButton(
///   onPressed: () {
///     // Handle button press
///   },
///   style: getIt.get<CommonButtonStyles>().primaryButtonStyle,
///   child: Text('Submit'),
/// )
/// ```
///
/// For full-width buttons:
/// ```dart
/// CommonButton(
///   onPressed: () {
///     // Handle button press
///   },
///   style: getIt.get<CommonButtonStyles>().primaryButtonStyle,
///   child: Text('Submit'),
///   isWrapContent: false, // Makes the button take full width
/// )
/// ```
///
/// See also:
///  * [CommonRadio], which provides a standardized radio button implementation.
class CommonButton extends StatelessWidget {
  /// Callback that is called when the button is tapped.
  /// If null, the button will be disabled.
  final VoidCallback? onPressed;

  /// The widget to display as the button's content.
  /// Typically a [Text] widget.
  final Widget child;

  /// The style to apply to the button.
  /// It's recommended to use styles from [CommonButtonStyles].
  final ButtonStyle style;

  /// Whether the button should wrap its content or take full width.
  /// If true, the button will size itself to fit its content.
  /// If false, the button will expand to fill the available width.
  final bool isWrapContent;

  /// Creates a standardized button widget.
  ///
  /// The [onPressed], [child], and [style] parameters are required.
  /// The [isWrapContent] parameter defaults to true, making the button size itself to fit its content.
  const CommonButton({
    required this.onPressed,
    required this.child,
    required this.style,
    super.key,
    this.isWrapContent = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isWrapContent ? null : double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: style,
        child: child,
      ),
    );
  }
}
