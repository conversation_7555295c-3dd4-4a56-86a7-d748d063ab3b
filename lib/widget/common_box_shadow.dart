// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/cupertino.dart';

import '../init_common_package.dart';
import '../resources/colors.dart';

class CommonBoxShadow {
  BoxShadow getBoxShadow({
    Offset offset = const Offset(0, 0),
    Color? color,
    double spreadRadius = 0,
    BlurStyle blurStyle = BlurStyle.outer,
    double blurRadius = 5,
  }) =>
      BoxShadow(
        color: color ?? getIt.get<CommonColors>().primary,
        spreadRadius: spreadRadius,
        blurStyle: blurStyle,
        blurRadius: blurRadius,
        offset: offset, // c changes position of shadow
      );
}
