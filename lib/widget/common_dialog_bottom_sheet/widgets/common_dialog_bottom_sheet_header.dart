// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../../../init_common_package.dart';
import '../../../resources/colors.dart';
import '../../../resources/global.dart';
import '../../../resources/images.dart';
import '../../common_image_provider.dart';

class CommonDialogSheetHeader extends StatelessWidget {
  final Widget child;
  final EdgeInsets padding;

  final bool isShowButtonClose;
  final Widget? buttonClose;
  final VoidCallback? onClickClose;

  const CommonDialogSheetHeader({
    required this.child,
    required this.padding,
    this.isShowButtonClose = false,
    this.buttonClose,
    this.onClickClose,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Center(
          child: Padding(
            padding: padding,
            child: child,
          ),
        ),
        _itemButtonClose(context)
      ],
    );
  }

  Widget _itemButtonClose(BuildContext context) {
    if (isShowButtonClose) {
      return Align(
        alignment: Alignment.topRight,
        child: GestureDetector(
          onTap: onClickClose,
          child: buttonClose ??
              Container(
                height: 48,
                width: 48,
                padding: const EdgeInsets.all(17),
                margin: const EdgeInsets.only(top: 8, right: 8),
                decoration: BoxDecoration(
                  color: getIt.get<CommonColors>().background,
                  shape: BoxShape.circle,
                ),
                child: getIt.get<CommonImageProvider>().asset(
                      CommonImages.icClosePopup,
                      package: FLUTTER_COMMON_PACKAGE,
                    ),
              ),
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}
