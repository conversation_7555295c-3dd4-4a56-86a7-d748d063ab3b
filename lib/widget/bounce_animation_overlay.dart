// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

class BounceAnimationOverlay extends StatefulWidget {
  final Widget child;

  const BounceAnimationOverlay({
    required this.child,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => BounceAnimationOverlayState();
}

class BounceAnimationOverlayState extends State<BounceAnimationOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    super.initState();

    controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 750));
    scaleAnimation = CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: ScaleTransition(scale: scaleAnimation, child: widget.child),
      ),
    );
  }
}
