// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../init_common_package.dart';
import '../resources/resources.dart';

class DashSeparator extends StatelessWidget {
  final Color? color;

  DashSeparator({
    super.key,
    this.width = 3,
    this.height = 1,
    Color? color,
  }) : color = color ?? getIt.get<CommonColors>().textHint;

  final double width;
  final double height;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final double boxWidth = constraints.constrainWidth();
        final double dashWidth = width;
        final double dashHeight = height;
        final int dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.horizontal,
          children: List<Widget>.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(
                decoration: BoxDecoration(color: color ?? getIt.get<CommonColors>().textHint),
              ),
            );
          }),
        );
      },
    );
  }
}
