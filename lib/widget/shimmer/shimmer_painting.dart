// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/widgets.dart';

import 'shimmer_animation.dart';

class ShimmerPainting extends StatefulWidget {
  final Widget child;

  const ShimmerPainting({required this.child, super.key});

  @override
  State<ShimmerPainting> createState() => _ShimmerPaintingState();
}

class _ShimmerPaintingState extends State<ShimmerPainting> with SingleTickerProviderStateMixin {
  Listenable? _shimmerChanges;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_shimmerChanges != null) {
      _shimmerChanges!.removeListener(_onShimmerChange);
    }
    _shimmerChanges = ShimmerAnimation.of(context)?.shimmerChanges;
    if (_shimmerChanges != null) {
      _shimmerChanges!.addListener(_onShimmerChange);
    }
  }

  @override
  void dispose() {
    _shimmerChanges?.removeListener(_onShimmerChange);
    super.dispose();
  }

  void _onShimmerChange() {
    setState(() {
      // update the shimmer painting.
    });
  }

  @override
  Widget build(BuildContext context) {
    // Collect ancestor shimmer information.
    final ShimmerAnimationState shimmer = ShimmerAnimation.of(context)!;
    if (!shimmer.isSized) {
      // The ancestor Shimmer widget isn’t laid
      // out yet. Return an empty box.
      return const SizedBox();
    }
    final Size shimmerSize = shimmer.size;
    final Gradient gradient = shimmer.gradient;
    if (context.findRenderObject() == null) {
      return const SizedBox.shrink();
    }
    final Offset offsetWithinShimmer = shimmer.getDescendantOffset(
      descendant: context.findRenderObject() as RenderBox,
    );

    if (offsetWithinShimmer.dx.isNaN || offsetWithinShimmer.dy.isNaN) {
      return const SizedBox.shrink();
    }

    return ShaderMask(
      blendMode: BlendMode.srcATop,
      shaderCallback: (Rect bounds) {
        return gradient.createShader(
          Rect.fromLTWH(
            -offsetWithinShimmer.dx,
            -offsetWithinShimmer.dy,
            shimmerSize.width,
            shimmerSize.height,
          ),
        );
      },
      child: widget.child,
    );
  }
}
