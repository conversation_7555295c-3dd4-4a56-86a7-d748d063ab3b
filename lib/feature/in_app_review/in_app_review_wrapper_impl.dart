import 'package:in_app_review/in_app_review.dart';

import '../../util/extension.dart';
import 'in_app_review_wrapper.dart';

class InAppReviewWrapperImpl extends InAppReviewWrapper {
  final InAppReview inAppReview;

  InAppReviewWrapperImpl({
    required this.inAppReview,
  });

  @override
  Future<bool> isAvailable() async {
    try {
      return await inAppReview.isAvailable();
    } on Exception catch (e) {
      commonLog('Error checking isAvailable() for in_app_review: $e');
      return false;
    }
  }

  @override
  Future<void> requestReview() async {
    try {
      return await inAppReview.requestReview();
    } on Exception catch (e) {
      commonLog('Error checking requestReview() for in_app_review: $e');
      return;
    }
  }

  @override
  Future<void> openStoreListing({
    String? appStoreId,
    String? microsoftStoreId,
  }) async {
    try {
      return await inAppReview.openStoreListing(
        appStoreId: appStoreId,
        microsoftStoreId: microsoftStoreId,
      );
    } on Exception catch (e) {
      commonLog('Error checking openStoreListing() for in_app_review: $e');
      return;
    }
  }
}
