// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

// wrap FirebaseCrashlytics instance for testing
class FirebaseCrashlyticsWrapper {
  static FirebaseCrashlytics? _instance;

  static FirebaseCrashlytics get instance {
    return _instance ??= FirebaseCrashlytics.instance;
  }

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static set instanceForTesting(FirebaseCrashlytics instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = FirebaseCrashlytics.instance;
  }
}
