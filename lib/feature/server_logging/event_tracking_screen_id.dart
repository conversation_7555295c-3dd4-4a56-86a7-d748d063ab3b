// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../../util/extensible_enum.dart';

class EventTrackingScreenId extends ExtensibleEnum {
  // PO confirm when screen undefined -> send "null" value
  // e.g. "01.null.999"
  // ref: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3380117684/Event+Tracking+Framework
  // "bbbb = 'null' in case the screen number is not tobe defined."
  static const EventTrackingScreenId undefined = EventTrackingScreenId('null');

  // https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3771858945/Product+-+Screen+flow+-+scan+NFC+flow
  static const EventTrackingScreenId webViewScreen = EventTrackingScreenId('0001');

  const EventTrackingScreenId(super.name);
}
