// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/services.dart';
import 'package:store_checker/store_checker.dart';

import '../../data/repository/logging/logging_repo.dart';
import '../../init_common_package.dart';
import '../../util/extension.dart';

mixin InstallSourceCheckerModule {
// Platform messages are asynchronous, so we initialize in an async method.
  Future<void> logInstallationSource() async {
    final LoggingRepo loggingRepo = getIt.get<LoggingRepo>();

    Source installationSource;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      //get origin of installed application
      installationSource = await StoreChecker.getSource;
    } on PlatformException {
      installationSource = Source.UNKNOWN;
    }

    String source = 'unknown_source';
    // Set source text state
    switch (installationSource) {
      case Source.IS_INSTALLED_FROM_PLAY_STORE:
        // Installed from Play Store
        source = 'play_store';
        break;
      case Source.IS_INSTALLED_FROM_LOCAL_SOURCE:
        // Installed using adb commands or side loading or any cloud service
        source = 'local_source';
        break;
      case Source.IS_INSTALLED_FROM_AMAZON_APP_STORE:
        // Installed from Amazon app store
        source = 'amazon_store';
        break;
      case Source.IS_INSTALLED_FROM_HUAWEI_APP_GALLERY:
        // Installed from Huawei app store
        source = 'huawei_app_gallery';
        break;
      case Source.IS_INSTALLED_FROM_SAMSUNG_GALAXY_STORE:
        // Installed from Samsung app store
        source = 'samsung_galaxy_store';
        break;
      case Source.IS_INSTALLED_FROM_SAMSUNG_SMART_SWITCH_MOBILE:
        // Installed from Samsung Smart Switch Mobile
        source = 'samsung_smart_switch_mobile';
        break;
      case Source.IS_INSTALLED_FROM_XIAOMI_GET_APPS:
        // Installed from Xiaomi app store
        source = 'xiaomi_get_apps';
        break;
      case Source.IS_INSTALLED_FROM_OPPO_APP_MARKET:
        // Installed from Oppo app store
        source = 'oppo_app_market';
        break;
      case Source.IS_INSTALLED_FROM_VIVO_APP_STORE:
        // Installed from Vivo app store
        source = 'vivo_app_store';
        break;
      case Source.IS_INSTALLED_FROM_OTHER_SOURCE:
        // Installed from other market store
        source = 'other_source';
        break;
      case Source.IS_INSTALLED_FROM_APP_STORE:
        // Installed from iOS app store
        source = 'app_store';
        break;
      case Source.IS_INSTALLED_FROM_TEST_FLIGHT:
        // Installed from Test Flight
        source = 'test_flight';
        break;
      case Source.UNKNOWN:
        // Installed from Unknown source
        source = 'unknown_source';
        break;
      case Source.IS_INSTALLED_FROM_PLAY_PACKAGE_INSTALLER:
        source = 'play_package_installer';
        break;
      case Source.IS_INSTALLED_FROM_RU_STORE:
        source = 'ru_store';
        break;
    }

    commonLog('install source $source');

    loggingRepo
        .logEvent(eventType: EventType.installSource, data: <String, dynamic>{'source': source});
  }
}
