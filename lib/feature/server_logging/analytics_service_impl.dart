// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';

import '../../util/extension.dart';
import 'firebase_analytics.dart';

/// Implementation of analytics service using Firebase Analytics.
///
/// This class provides analytics functionality using Firebase Analytics
/// and optionally Datadog.
class AnalyticsServiceImpl {
  /// The Firebase Analytics wrapper.
  final FirebaseAnalyticsWrapper? _firebaseAnalyticsWrapper;

  /// The Datadog SDK instance, if available.
  final DatadogSdk? _datadogSdk;

  /// Creates a new [AnalyticsServiceImpl] with the provided dependencies.
  ///
  /// [firebaseAnalyticsWrapper] The Firebase Analytics wrapper to use, or null if not available.
  /// [datadogSdk] Optional Datadog SDK instance for additional analytics.
  AnalyticsServiceImpl(this._firebaseAnalyticsWrapper, [this._datadogSdk]);

  Future<String?> getAppInstanceId() async {
    if (_firebaseAnalyticsWrapper == null) {
      return null;
    }

    try {
      return await _firebaseAnalyticsWrapper.analytics.appInstanceId;
    } on Exception catch (e) {
      commonLog('Error when get Firebase App Instance Id: $e');
      return null;
    }
  }

  Future<void> setDeviceId(String? deviceId) async {
    if (deviceId == null) {
      return;
    }

    try {
      // Set device ID in Firebase Analytics if available
      if (_firebaseAnalyticsWrapper != null) {
        await _firebaseAnalyticsWrapper.analytics.setUserId(id: deviceId);
      }

      // Set device ID in Datadog if available
      _datadogSdk?.setUserInfo(extraInfo: <String, String?>{'deviceId': deviceId});
    } on Exception catch (e) {
      commonLog('Error when setting device ID in analytics services: $e');
    }
  }
}
