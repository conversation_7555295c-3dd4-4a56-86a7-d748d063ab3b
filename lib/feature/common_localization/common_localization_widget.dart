// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../common_package/common_package.dart';

class CommonLocalization extends StatelessWidget {
  final Widget child;
  final CommonLocalizationConfigs configs;

  const CommonLocalization({
    required this.child,
    required this.configs,
    super.key,
  });

  static Future<void> ensureInitialized() async => await EasyLocalization.ensureInitialized();

  @override
  Widget build(BuildContext context) {
    return EasyLocalization(
      supportedLocales: configs.supportedLocales,
      path: configs.path,
      fallbackLocale: configs.fallbackLocale,
      startLocale: configs.startLocale,
      child: child,
    );
  }
}
