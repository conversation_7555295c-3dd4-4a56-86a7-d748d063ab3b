// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

// ignore_for_file: avoid_catches_without_on_clauses

import 'package:device_info_plus/device_info_plus.dart';

import '../../../../data/repository/logging/log_error_mixin.dart';
import 'ios_device_identifier.dart';

class IosDeviceIdentifierImpl extends IosDeviceIdentifier with LogErrorMixin {
  final DeviceInfoPlugin deviceInfo;

  IosDeviceIdentifierImpl({
    required this.deviceInfo,
  });

  @override
  Future<String?> getIosId() async {
    try {
      final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor;
    } catch (err) {
      logPlatformErrorEvent(
        errorType: 'ios_device_identifier',
        action: 'get_ios_id',
        error: err,
      );
      return null;
    }
  }
}
