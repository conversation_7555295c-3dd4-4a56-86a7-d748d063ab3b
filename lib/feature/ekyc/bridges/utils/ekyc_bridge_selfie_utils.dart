// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:trust_vision_plugin/result/selfie_image.dart';
import 'package:trust_vision_plugin/result/tv_image_class.dart';

import '../../../../util/extension.dart';
import '../models/ekyc_bridge_image.dart';
import '../models/ekyc_bridge_image_direction.dart';
import '../models/ekyc_bridge_liveness_mode.dart';

class EkycBridgeSelfieImageUtils {
  static EkycBridgeSelfieImageUtils? _instance;

  static final EkycBridgeSelfieImageUtils _originalInstance =
      EkycBridgeSelfieImageUtils._internal();

  factory EkycBridgeSelfieImageUtils() {
    return _instance ??= _originalInstance;
  }

  EkycBridgeSelfieImageUtils._internal();

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static set instanceForTesting(EkycBridgeSelfieImageUtils instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }

  List<EkycBridgeImage> convertTVSelfieImageToBridgeImage({
    required EkycBridgeLivenessMode livenessMode,
    required List<SelfieImage> images,
  }) {
    return switch (livenessMode) {
      EkycBridgeLivenessMode.flash_16 ||
      EkycBridgeLivenessMode.flash_32 =>
        createFlashSelfieTVImages(images),
      EkycBridgeLivenessMode.active => createActiveSelfieImages(images),
      _ => <EkycBridgeImage>[],
    };
  }

  /// Get the direction of the selfie image by index in the list of selfie images.
  /// index 0: close face
  /// index 1: far face
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-1896?focusedCommentId=130915
  @visibleForTesting
  EkycBridgeImageDirection? getDirectionOfFlashSelfieImageByIndex(int index) {
    switch (index) {
      case 0:
        return EkycBridgeImageDirection.closeFace;
      case 1:
        return EkycBridgeImageDirection.farFace;
      default:
        return null;
    }
  }

  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3633316003/KH+th+c+hi+n+Selfie+Active#6.-Upload-image
  @visibleForTesting
  EkycBridgeImageDirection? getGestureDirectionOfActiveSelfieImage(String? direction) {
    switch (direction) {
      case 'left':
        return EkycBridgeImageDirection.left;
      case 'right':
        return EkycBridgeImageDirection.right;
      case 'up':
        return EkycBridgeImageDirection.up;
      case 'down':
        return EkycBridgeImageDirection.down;
      default:
        return null;
    }
  }

  @visibleForTesting
  List<EkycBridgeImage> createFlashSelfieTVImages(List<SelfieImage> images) {
    EkycBridgeImageDirection? direction;
    final List<EkycBridgeImage> selfieImages = <EkycBridgeImage>[];

    images.forEachIndexed((int index, SelfieImage selfieImage) {
      selfieImage.frontalImage?.let((TVImageClass it) {
        if (it.rawImageBase64 == null) {
          return;
        }

        direction = getDirectionOfFlashSelfieImageByIndex(index);

        final EkycBridgeImage frontalImage = EkycBridgeImage.fromTVImageClass(
          tvImage: it,
          direction: direction,
        );
        selfieImages.add(frontalImage);
      });
    });

    return selfieImages;
  }

  @visibleForTesting
  List<EkycBridgeImage> createActiveSelfieImages(List<SelfieImage> images) {
    final List<EkycBridgeImage> selfieImages = <EkycBridgeImage>[];

    for (final SelfieImage selfieImage in images) {
      EkycBridgeImageDirection? direction;
      selfieImage.frontalImage?.let((TVImageClass it) {
        if (it.rawImageBase64 == null) {
          return;
        }

        /// With [LivenessModeWrapper.active]
        /// Direction of last frontal image is "frontal.main"
        if (selfieImage.gestureType == EkycBridgeImageDirection.frontal.value) {
          direction = EkycBridgeImageDirection.frontalMain;
        } else {
          direction = EkycBridgeImageDirection.frontal;
        }

        final EkycBridgeImage frontalImage = EkycBridgeImage.fromTVImageClass(
          tvImage: it,
          direction: direction,
        );
        selfieImages.add(frontalImage);
      });

      selfieImage.gestureImage?.let((TVImageClass it) {
        if (it.rawImageBase64 == null) {
          return;
        }
        direction = getGestureDirectionOfActiveSelfieImage(selfieImage.gestureType);

        final EkycBridgeImage gestureImage = EkycBridgeImage.fromTVImageClass(
          tvImage: it,
          direction: direction,
        );
        selfieImages.add(gestureImage);
      });
    }

    return selfieImages;
  }
}
