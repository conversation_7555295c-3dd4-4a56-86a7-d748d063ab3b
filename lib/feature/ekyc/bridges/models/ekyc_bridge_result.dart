// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'ekyc_bridge_error_reason.dart';

class EkycBridgeResult<T> {
  final EkycBridgeErrorReason? errorReason;
  final T? data;

  bool get isSuccess => errorReason == null;

  factory EkycBridgeResult.succeed({T? data}) => EkycBridgeResult<T>._(data: data);

  factory EkycBridgeResult.failed({
    required EkycBridgeErrorReason errorReason,
  }) =>
      EkycBridgeResult<T>._(
        errorReason: errorReason,
      );

  const EkycBridgeResult._({
    this.errorReason,
    this.data,
  });
}
