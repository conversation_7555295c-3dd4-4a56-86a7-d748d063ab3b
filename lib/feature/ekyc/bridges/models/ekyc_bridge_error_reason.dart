// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

enum EkycBridgeErrorReason {
  /// init
  exceedLimit('exceed_limit'),
  initWithInvalidSession('init_with_invalid_session'),

  /// selfie
  selfieImageUploadFailed('selfie_image_upload_failed'),
  selfieVerifySanityFailed('selfie_verify_sanity_failed'),
  selfieVerifyLivenessFailed('selfie_verify_liveness_failed'),

  /// face matching
  cantStartFaceMatching('can_not_start_face_matching'),
  faceMatchingUnmatched('face_matching_unmatched'),

  /// common
  userCancelled('user_cancelled'),
  sessionExpired('session_expired'),
  unknown('unknown');

  final String value;

  const EkycBridgeErrorReason(this.value);
}
