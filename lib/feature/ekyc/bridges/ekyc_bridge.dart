// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'models/ekyc_bridge_frame_batch.dart';
import 'models/ekyc_bridge_liveness_mode.dart';
import 'models/ekyc_bridge_result.dart';
import 'models/ekyc_bridge_selfie_capturing_result.dart';

typedef OnNewFrameBatchListener = void Function(EkycBridgeFrameBatch frameBatch);

abstract class EkycBridge {
  /// Initializes the eKYC SDK with the provided configuration
  /// This method is required to be called before any other method
  Future<EkycBridgeResult<void>> initEkyc({
    required String? jsonConfigurationByServer,
    String? languageCode,
  });

  /// Starts the selfie capturing process with the specified liveness mode and frame batch listener.
  /// Refer: https://ekyc.trustingsocial.com/sdks/Flutter-SDK#3-selfie-capturing
  Future<EkycBridgeResult<EkycBridgeSelfieCapturingResult>> startSelfieCapturing({
    required EkycBridgeLivenessMode livenessMode,
    required OnNewFrameBatchListener onNewFrameBatchListener,
    bool skipConfirmScreen = false,
  });
}
