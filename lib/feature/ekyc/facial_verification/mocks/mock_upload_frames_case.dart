// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

enum MockUploadFramesCase {
  success('ekyc_upload_frames_success.json'),
  fail('ekyc_upload_frames_fail.json');

  final String value;

  const MockUploadFramesCase(this.value);
}

String getMockUploadFramesCaseFileName(MockUploadFramesCase mockCase) {
  return mockCase.value;
}
