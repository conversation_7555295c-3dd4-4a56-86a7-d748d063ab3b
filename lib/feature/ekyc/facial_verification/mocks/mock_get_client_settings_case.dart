// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

enum MockGetClientSettingsCase {
  success('ekyc_get_client_settings_success.json'),
  fail('ekyc_get_client_settings_fail.json');

  final String value;

  const MockGetClientSettingsCase(this.value);
}

String getMockGetClientSettingsCaseFileName(MockGetClientSettingsCase mockCase) {
  return mockCase.value;
}
