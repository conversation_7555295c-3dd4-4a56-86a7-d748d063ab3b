// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

enum MockUploadImagesCase {
  success('ekyc_upload_images_success.json'),
  fail('ekyc_upload_images_fail.json');

  final String value;

  const MockUploadImagesCase(this.value);
}

String getMockUploadImagesCaseFileName(MockUploadImagesCase mockCase) {
  return mockCase.value;
}
