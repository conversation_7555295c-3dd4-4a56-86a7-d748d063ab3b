// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

class CommonKeyboardController {
  late StreamSubscription<bool> _keyboardSubscription;

  /*
  * true: auto unFocus when keyboard hide
  * false: nothing
  * */
  final bool autoUnFocus;
  final void Function(bool)? onChangeKeyboardVisible;

  CommonKeyboardController({this.autoUnFocus = true, this.onChangeKeyboardVisible});

  Widget initKeyboardProvider({required Widget child}) {
    return KeyboardVisibilityProvider(child: child);
  }

  void register() {
    _keyboardSubscription = KeyboardVisibilityController().onChange.listen((bool visible) {
      if (!visible && autoUnFocus) FocusManager.instance.primaryFocus?.unfocus();
      onChangeKeyboardVisible?.call(visible);
    });
  }

  void dispose() => _keyboardSubscription.cancel();

  bool get isVisible => KeyboardVisibilityController().isVisible;
}
