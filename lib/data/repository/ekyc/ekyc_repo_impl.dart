// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/cupertino.dart';

import '../../../common_package/common_package.dart';
import '../../../resources/global.dart';
import '../../../util/functions.dart';
import '../../http_client/base_response.dart';
import '../../http_client/common_request_option.dart';
import '../../http_client/mock_config.dart';
import '../../request/ekyc_upload_frames_request.dart';
import '../../request/ekyc_upload_images_request.dart';
import '../../response/ekyc_client_settings_entity.dart';
import '../../response/ekyc_upload_frames_entity.dart';
import '../../response/ekyc_upload_images_entity.dart';
import '../base_repo.dart';
import 'ekyc_repo.dart';

class EkycRepoImpl extends BaseRepo implements EkycRepo {
  @visibleForTesting
  static const String ekycUrl = 'ekyc';

  static const String uploadImageUrl = '$ekycUrl/images';
  static const String uploadFramesUrl = '$ekycUrl/frames';
  static const String getClientSettingsUrl = '$ekycUrl/client-settings';

  EkycRepoImpl(super.client);

  @override
  Future<EkycClientSettingsEntity> getClientSettings({
    String? sessionToken,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> headers = getSessionHeader(sessionToken);
    final BaseResponse baseResponse = await client.get(
      getClientSettingsUrl,
      requestOption: CommonRequestOption(headers: headers),
      mockConfig: mockConfig,
    );

    final EkycClientSettingsEntity entity = commonUtilFunction.serialize(
          () => EkycClientSettingsEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        EkycClientSettingsEntity.unserializable();
    return entity;
  }

  @override
  Future<EkycUploadImagesEntity> uploadImages({
    required EkycUploadImagesRequest request,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> headers = getSessionHeader(request.sessionToken);
    final FormData formData = FormData.fromMap(
      request.toJson(),
    );

    final BaseResponse baseResponse = await client.post(
      uploadImageUrl,
      data: formData,
      requestOption: CommonRequestOption(headers: headers),
      mockConfig: mockConfig,
    );

    return commonUtilFunction.serialize(
          () => EkycUploadImagesEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        EkycUploadImagesEntity.unserializable();
  }

  @override
  Future<EkycUploadFramesEntity> uploadFrames({
    required EkycUploadFramesRequest request,
    MockConfig? mockConfig,
  }) async {
    final FormData formData = FormData.fromMap(
      request.toJson(),
    );
    final Map<String, dynamic> headers = getSessionHeader(request.sessionToken);

    final BaseResponse baseResponse = await client.post(
      uploadFramesUrl,
      data: formData,
      mockConfig: mockConfig,
      requestOption: CommonRequestOption(headers: headers),
    );

    return commonUtilFunction.serialize(
          () => EkycUploadFramesEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        EkycUploadFramesEntity.unserializable();
  }

  @visibleForTesting
  Map<String, String?> getSessionHeader(String? sessionToken) {
    return <String, String?>{
      HeaderKey.sessionToken: sessionToken,
    };
  }
}
