// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'common_http_client.dart';

class MockConfig {
  final bool enable;
  final String? fileName;
  final Duration delayTime;
  final int statusCode;

  const MockConfig({
    required this.enable,
    this.fileName,
    this.delayTime = const Duration(milliseconds: 1000),
    this.statusCode = CommonHttpClient.SUCCESS,
  });

  @override
  String toString() {
    return 'MockConfig{enable: $enable, fileName: $fileName, delayTime: $delayTime, statusCode: $statusCode}';
  }
}
