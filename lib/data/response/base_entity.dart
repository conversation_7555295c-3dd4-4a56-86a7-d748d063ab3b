// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../http_client/base_response.dart';

class BaseEntity {
  static const String verdictSuccess = 'success';

  final Map<String, dynamic>? data;
  final int? statusCode;
  final String? message;
  final String? time;
  final String? verdict;

  /// this field is available if there's an exception at local
  int? localExceptionCode;

  String? get userMessage => data?['user_message'] as String?;

  /// Ref to thread https://trustingsocial.slack.com/archives/C0446DCNV70/p1704773499688499?thread_ts=1703476999.105829&cid=C0446DCNV70
  /// BE defined this field as `user_message_title` at https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3442606082/Mobile+API+contract
  /// Pushing it here to make scalable for other APIs in the future
  String? get userMessageTitle => data?['user_message_title'] as String?;

  BaseEntity(
      {this.data, this.statusCode, this.message, this.time, this.verdict, this.localExceptionCode});

  BaseEntity.fromBaseResponse(BaseResponse? json)
      : data = json?.response?['data'] as Map<String, dynamic>?,
        statusCode = json?.statusCode,
        message = json?.response?['message'] as String?,
        time = json?.response?['time'] as String?,
        verdict = json?.response?['verdict'] as String?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'data': data,
        'status_code': statusCode,
        'message': message,
        'time': time,
        'verdict': verdict,
      };
}
