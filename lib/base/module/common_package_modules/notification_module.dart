// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

import '../../../data/repository/logging/logging_repo.dart';
import '../../../feature/onesignal/listener_handler.dart';
import '../../../flavors/flavor_config.dart';
import '../../../util/clear_all_notifications_wrapper.dart';
import '../feature_module.dart';
import '../module_names.dart';

/// Notification module that provides push notification dependencies.
///
/// This module registers services related to push notifications
/// using OneSignal.
class NotificationModule implements FeatureModule {
  @override
  String get name => CommonPackageModuleNames.notification;

  @override
  List<Type> get dependencies => [
        ClearAllNotificationsWrapper,
      ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register notification utilities
    getIt.registerLazySingleton<ClearAllNotificationsWrapper>(() => ClearAllNotificationsWrapper());

    // Initialize OneSignal if app ID is provided
    final String? oneSignalAppId = FlavorConfig.instance.values.oneSignalAppId;
    if (oneSignalAppId == null) return;

    OneSignal.shared.setLogLevel(OSLogLevel.verbose, OSLogLevel.none);

    // Disable automatically collect location
    OneSignal.shared.setLocationShared(false);

    await OneSignal.shared.setAppId(oneSignalAppId);

    // iOS-only method to open launch URLs in Safari when set to false
    OneSignal.shared.setLaunchURLsInApp(false);

    OneSignal.shared.disablePush(false);

    // Set up permission observer
    OneSignal.shared.setPermissionObserver((OSPermissionStateChanges changes) {
      if (kDebugMode) {
        print('PERMISSION STATE CHANGED: ${changes.jsonRepresentation()}');
      }

      if (changes.to.status == OSNotificationPermission.denied) {
        final LoggingRepo loggingRepo = getIt.get<LoggingRepo>();
        loggingRepo.logEvent(eventType: EventType.deniedNotification);
      }

      if (!getIt.isRegistered<OneSignalListenerHandler>()) return;
      final OneSignalListenerHandler oneSignalListenerHandler = getIt.get<OneSignalListenerHandler>();
      oneSignalListenerHandler.onPermissionObserver(changes);
    });

    // Set up subscription observer
    OneSignal.shared.setSubscriptionObserver((OSSubscriptionStateChanges changes) async {
      if (kDebugMode) {
        print('SUBSCRIPTION STATE CHANGED: ${changes.jsonRepresentation()}');
      }

      final LoggingRepo loggingRepo = getIt.get<LoggingRepo>();
      loggingRepo.logEvent(
          eventType: EventType.notificationToken,
          data: <String, dynamic>{'token': changes.to.pushToken});

      if (!getIt.isRegistered<OneSignalListenerHandler>()) return;
      final OneSignalListenerHandler oneSignalListenerHandler = getIt.get<OneSignalListenerHandler>();
      oneSignalListenerHandler.onSubscriptionObserver(changes);
    });
  }
}
