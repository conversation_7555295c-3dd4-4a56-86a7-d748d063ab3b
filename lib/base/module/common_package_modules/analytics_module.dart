// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:flutter_common_package/feature/server_logging/datadog_sdk_wrapper.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import '../../../data/repository/logging/logging_repo.dart';
import '../../../data/repository/logging/logging_repo_impl.dart';
import '../../../data/http_client/common_http_client.dart';
import '../../../feature/data_collection/device_identifier/device_identifier.dart';
import '../../../feature/server_logging/analytics_service_impl.dart';
import '../../../feature/server_logging/common_navigator_observer.dart';
import '../../../feature/server_logging/event_tracking_utils.dart';
import '../../../feature/server_logging/firebase_analytics.dart';
import '../../../feature/server_logging/firebase_crashlytics.dart';
import '../../../flavors/flavor_config.dart';
import '../../../util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import '../../../util/extension.dart';
import '../../../util/uuid/uuid_generator.dart';
import '../feature_module.dart';
import '../module_names.dart';

/// Analytics module that provides analytics and logging dependencies.
///
/// This module registers services related to analytics, logging,
/// and crash reporting. It supports both Firebase Analytics/Crashlytics
/// and Datadog for analytics and error tracking.
class AnalyticsModule implements FeatureModule {
  @override
  String get name => CommonPackageModuleNames.analytics;

  @override
  List<Type> get dependencies => [
        LoggingRepo,
        EventTrackingUtils,
        FirebaseAnalyticsWrapper,
        CommonNavigatorObserver,
        DatadogLogger,
        AnalyticsServiceImpl,
      ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register core dependencies
    _registerCoreDependencies(getIt);

    // Get Datadog configuration
    final DatadogConfiguration? datadogConfig =
        FlavorConfig.instance.values.commonDataDogConfig?.datadogConfiguration;
    final bool firebaseEnabled = FlavorConfig.instance.values.initializeFirebaseSdk;

    // Register navigator observer (used by both Firebase and Datadog)
    if (!getIt.isRegistered<CommonNavigatorObserver>()) {
      getIt.registerLazySingleton<CommonNavigatorObserver>(() => CommonNavigatorObserver());
    }

    // Initialize Firebase if enabled
    if (firebaseEnabled) {
      await _registerFirebaseDependencies(getIt, datadogConfig);
    }

    // Initialize Datadog if configured
    if (datadogConfig != null) {
      await _registerDatadogDependencies(getIt, datadogConfig, firebaseEnabled);
    }
  }

  /// Registers core dependencies that are required regardless of which analytics services are enabled.
  void _registerCoreDependencies(GetIt getIt) {
    // Register logging repository if not already registered
    if (!getIt.isRegistered<LoggingRepo>()) {
      getIt.registerLazySingleton<LoggingRepo>(
        () => LoggingRepoImpl(commonHttpClient: getIt.get<CommonHttpClient>()),
      );
    }

    // Register event tracking utilities if not already registered
    if (!getIt.isRegistered<EventTrackingUtils>()) {
      getIt.registerLazySingleton<EventTrackingUtils>(() => EventTrackingUtils(
            deviceInfoPluginWrapper: getIt.get<DeviceInfoPluginWrapper>(),
            loggingRepo: getIt.get<LoggingRepo>(),
            uuidGenerator: getIt.get<UUIDGenerator>(),
          ));
    }
  }

  /// Registers Firebase-related dependencies if Firebase is enabled.
  ///
  /// [getIt] The GetIt instance to register dependencies with.
  /// [datadogConfig] Optional Datadog configuration to integrate with Firebase.
  Future<void> _registerFirebaseDependencies(
    GetIt getIt,
    DatadogConfiguration? datadogConfig,
  ) async {
    // Register Firebase Analytics if not already registered
    _registerFirebaseAnalytics(getIt);

    // Register Firebase Analytics wrapper if not already registered
    if (!getIt.isRegistered<FirebaseAnalyticsWrapper>()) {
      final firebaseAnalyticsWrapper = _createFirebaseAnalyticsWrapper(getIt);
      getIt.registerLazySingleton<FirebaseAnalyticsWrapper>(() => firebaseAnalyticsWrapper);
    }

    // Register DatadogSdk if needed for Firebase integration
    if (datadogConfig != null) {
      _registerDatadogSdk(getIt);
    }

    // Register AnalyticsService implementation with Firebase
    if (!getIt.isRegistered<AnalyticsServiceImpl>()) {
      getIt.registerLazySingleton<AnalyticsServiceImpl>(() => AnalyticsServiceImpl(
            firebaseAnalyticsWrapper,
            datadogConfig != null ? getIt.get<DatadogSdk>() : null,
          ));
    }

    // Register and configure Firebase Crashlytics
    await _registerFirebaseCrashlytics(getIt);
  }

  /// Registers Firebase Analytics if not already registered.
  void _registerFirebaseAnalytics(GetIt getIt) {
    if (!getIt.isRegistered<FirebaseAnalytics>()) {
      try {
        getIt.registerSingleton<FirebaseAnalytics>(FirebaseAnalytics.instance);
      } catch (e) {
        // In test environment, FirebaseAnalytics.instance might not be available
        commonLog('Failed to register FirebaseAnalytics: $e');
      }
    }
  }

  /// Creates a FirebaseAnalyticsWrapper instance.
  FirebaseAnalyticsWrapper _createFirebaseAnalyticsWrapper(GetIt getIt) {
    return FirebaseAnalyticsWrapper(
      getIt.get<FirebaseAnalytics>(),
      FirebaseAnalyticsObserver(analytics: getIt.get<FirebaseAnalytics>()),
    );
  }

  /// Registers and configures Firebase Crashlytics.
  Future<void> _registerFirebaseCrashlytics(GetIt getIt) async {
    // Register FirebaseCrashlytics if not already registered
    if (!getIt.isRegistered<FirebaseCrashlytics>()) {
      try {
        getIt.registerSingleton<FirebaseCrashlytics>(FirebaseCrashlytics.instance);
      } catch (e) {
        // In test environment, FirebaseCrashlytics.instance might not be available
        commonLog('Failed to register FirebaseCrashlytics: $e');
        return;
      }
    }

    // Configure Firebase Crashlytics if available
    if (getIt.isRegistered<FirebaseCrashlytics>()) {
      final crashlytics = getIt.get<FirebaseCrashlytics>();

      // Configure error handlers
      FlutterError.onError = crashlytics.recordFlutterError;
      PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
        crashlytics.recordError(error, stack, fatal: false);
        return true;
      };

      // Set device ID in Crashlytics if available
      await _setDeviceIdInCrashlytics(getIt, crashlytics);
    }
  }

  /// Sets the device ID in Crashlytics if a DeviceIdentifier is available.
  Future<void> _setDeviceIdInCrashlytics(
    GetIt getIt,
    FirebaseCrashlytics crashlytics,
  ) async {
    if (getIt.isRegistered<DeviceIdentifier>()) {
      try {
        final String? deviceId = await getIt.get<DeviceIdentifier>().getDeviceId();
        crashlytics.setCustomKey('deviceId', deviceId ?? '');
      } catch (e) {
        commonLog('Failed to set device ID in Crashlytics: $e');
      }
    }
  }

  /// Registers Datadog-related dependencies.
  ///
  /// [getIt] The GetIt instance to register dependencies with.
  /// [datadogConfig] The Datadog configuration to use.
  /// [firebaseEnabled] Whether Firebase is enabled.
  Future<void> _registerDatadogDependencies(
    GetIt getIt,
    DatadogConfiguration datadogConfig,
    bool firebaseEnabled,
  ) async {
    // Register DatadogSdk if not already registered
    _registerDatadogSdk(getIt);

    // Get the DatadogSdk instance from GetIt
    final datadogSdk = getIt.get<DatadogSdk>();

    // Initialize Datadog
    await _initializeDatadog(datadogSdk, datadogConfig, getIt);

    // Register DatadogLogger if available
    _registerDatadogLogger(getIt, datadogSdk);

    // Register AnalyticsServiceImpl if not already registered by Firebase
    if (!getIt.isRegistered<AnalyticsServiceImpl>()) {
      getIt.registerLazySingleton<AnalyticsServiceImpl>(() => AnalyticsServiceImpl(
            firebaseEnabled && getIt.isRegistered<FirebaseAnalyticsWrapper>()
                ? getIt.get<FirebaseAnalyticsWrapper>()
                : null,
            datadogSdk,
          ));
    }
  }

  /// Registers the DatadogSdk if not already registered.
  void _registerDatadogSdk(GetIt getIt) {
    if (!getIt.isRegistered<DatadogSdk>()) {
      getIt.registerSingleton<DatadogSdk>(DatadogSdkWrapper.instance);
    }
  }

  /// Initializes Datadog with the provided configuration.
  Future<void> _initializeDatadog(
    DatadogSdk datadogSdk,
    DatadogConfiguration datadogConfig,
    GetIt getIt,
  ) async {
    try {
      await datadogSdk.initialize(
        datadogConfig,
        TrackingConsent.granted,
      );

      // Set device ID in Datadog if available
      await _setDeviceIdInDatadog(getIt, datadogSdk);
    } catch (e) {
      commonLog('Failed to initialize Datadog: $e');
    }
  }

  /// Sets the device ID in Datadog if a DeviceIdentifier is available.
  Future<void> _setDeviceIdInDatadog(
    GetIt getIt,
    DatadogSdk datadogSdk,
  ) async {
    if (getIt.isRegistered<DeviceIdentifier>()) {
      try {
        final String? deviceId = await getIt.get<DeviceIdentifier>().getDeviceId();
        datadogSdk.setUserInfo(extraInfo: <String, String?>{'deviceId': deviceId});
      } catch (e) {
        commonLog('Failed to set device ID in Datadog: $e');
      }
    }
  }

  /// Registers the DatadogLogger if available.
  void _registerDatadogLogger(GetIt getIt, DatadogSdk datadogSdk) {
    final DatadogLogger? logger = datadogSdk.logs?.createLogger(
      DatadogLoggerConfiguration(),
    );

    if (logger != null) {
      // Add custom tags if available
      final Map<String, String>? tags =
          FlavorConfig.instance.values.commonDataDogConfig?.logCustomTags;
      if (tags != null) {
        tags.forEach((String key, String val) {
          logger.addTag(key, val);
        });
      }

      getIt.registerLazySingleton<DatadogLogger>(() => logger);
    }
  }
}
