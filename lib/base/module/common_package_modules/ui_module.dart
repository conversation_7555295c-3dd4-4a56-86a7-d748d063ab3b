// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:get_it/get_it.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../global_key_provider.dart';
import '../../../util/alert_manager.dart';
import '../../../util/clipboard_wrapper.dart';
import '../../../util/common_toast.dart';
import '../../../util/otp_auto_fill/otp_auto_fill.dart';
import '../../../util/otp_auto_fill/sms_otp_auto_fill_impl.dart';
import '../../../util/url_launcher_wrapper.dart';
import '../../../widget/common_image_provider.dart';
import '../feature_module.dart';
import '../module_names.dart';

/// UI module that provides UI-related dependencies.
///
/// This module registers services related to UI components,
/// utilities, and helpers.
class UiModule implements FeatureModule {
  @override
  String get name => CommonPackageModuleNames.ui;

  @override
  List<Type> get dependencies => [
        AlertManager,
        CommonImageProvider,
        OtpAutoFill,
        ClipboardWrapper,
        UrlLauncherWrapper,
        GlobalKeyProvider,
      ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register UI components
    if (!getIt.isRegistered<AlertManager>()) {
      getIt.registerLazySingleton<AlertManager>(() => CommonToast(FlutterToastWrapper()));
    }

    if (!getIt.isRegistered<CommonImageProvider>()) {
      getIt.registerLazySingleton<CommonImageProvider>(() => CommonImageProviderImpl());
    }

    // Register OTP auto-fill
    if (!getIt.isRegistered<OtpAutoFill>()) {
      getIt.registerFactory<OtpAutoFill>(() => SmsOtpAutoFillImpl(OTPInteractor()));
    }

    // Register clipboard utilities
    if (!getIt.isRegistered<ClipboardWrapper>()) {
      getIt.registerLazySingleton<ClipboardWrapper>(() => ClipboardWrapper());
    }

    // Register URL launcher
    if (!getIt.isRegistered<UrlLauncherWrapper>()) {
      getIt.registerLazySingleton<UrlLauncherWrapper>(() => UrlLauncherWrapper());
    }

    // Register global key provider
    if (!getIt.isRegistered<GlobalKeyProvider>()) {
      getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());
    }

    // Configure visibility detector
    VisibilityDetectorController.instance.updateInterval = Duration(milliseconds: 100);
  }
}
