// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import '../../util/extension.dart';
import 'feature_module.dart';
import 'module_registry.dart';
import 'module_registration.dart';

/// Builder for creating and configuring a [ModuleRegistry].
///
/// The builder pattern provides a clear separation between the configuration
/// and usage phases of the module registry lifecycle. It ensures that all
/// validation happens before the registry is used.
///
/// IMPORTANT: This class is intended for INTERNAL USE ONLY.
/// Host applications should NOT use this class directly.
/// Instead, use the methods provided in `init_common_package.dart`.
///
/// Direct usage of this class by host applications is not supported and may
/// break in future versions without notice.
///
/// Example (for internal use only):
/// ```dart
/// // This example is for internal use only. Host applications should use
/// // the methods provided in init_common_package.dart instead.
/// final registry = ModuleRegistry.builder(GetIt.instance)
///   .register(CoreModule(), source: 'common_package')
///   .register(NetworkModule(), source: 'common_package')
///   .build(); // Validation happens automatically here
///
/// await registry.initializeAllRegisteredModules();
/// ```
class ModuleRegistryBuilder {
  /// The [GetIt] instance used for dependency injection.
  final GetIt _getIt;

  /// The set of registered modules with their registration details.
  final Map<String, ModuleRegistration> _moduleRegistrations = <String, ModuleRegistration>{};

  /// Flag indicating whether the registry has been built.
  bool _isBuilt = false;

  /// Creates a new [ModuleRegistryBuilder] with the provided [GetIt] instance.
  ModuleRegistryBuilder(this._getIt);

  /// Registers a module with the registry.
  ///
  /// The [source] parameter identifies where the module comes from
  /// (e.g., 'common_package', 'host_app') to provide clearer error messages.
  ///
  /// Validates immediately that this module doesn't conflict with existing ones.
  /// Returns this builder for method chaining.
  ///
  /// Throws an exception if a module with the same name is already registered
  /// from a different source or with a different type.
  /// Throws a [StateError] if the registry has already been built.
  ModuleRegistryBuilder register(FeatureModule module, {required String source}) {
    if (_isBuilt) {
      throw StateError('Cannot register modules after the registry is built');
    }

    final ModuleRegistration registration = ModuleRegistration(
      module: module,
      source: source,
      registeredAt: DateTime.now(),
    );

    // Validate this specific registration
    _validateSingleRegistration(module, source);

    _moduleRegistrations[module.name] = registration;
    return this;
  }

  /// Validates that a single module registration is valid and doesn't conflict with existing ones.
  ///
  /// Validates that the module has a non-empty name and that it doesn't conflict
  /// with existing modules.
  ///
  /// Throws an exception if validation fails.
  void _validateSingleRegistration(FeatureModule module, String source) {
    // Validate module name
    if (module.name.isEmpty) {
      throw Exception(
        'Invalid module: Module name cannot be empty. '
        'Module of type ${module.runtimeType} from "$source" must have a non-empty name.'
      );
    }

    // Validate dependencies list
    final List<Type> dependencies = module.dependencies ?? <Type>[];
    if (dependencies.isEmpty) {
      // This is just a warning, not an error
      'Module Registration'.commonLog(
        'Warning: Module "${module.name}" of type ${module.runtimeType} '
        'from "$source" has no dependencies declared. '
        'This might be intentional, but could indicate a misconfiguration.'
      );
    }

    // Check for conflicts with existing modules
    if (_moduleRegistrations.containsKey(module.name)) {
      final ModuleRegistration existing = _moduleRegistrations[module.name]!;

      // If the same module type is registered again, it's fine (will be replaced)
      if (existing.module.runtimeType == module.runtimeType) {
        return;
      }

      // Otherwise, we have a conflict
      throw Exception(
        'Module name conflict detected: "${module.name}" is already '
        'registered by ${existing.module.runtimeType} from "${existing.source}". '
        'New module ${module.runtimeType} from "$source" cannot use the same name. '
        'Each module must have a unique name across all sources.'
      );
    }
  }

  /// Builds the registry, performing comprehensive validation of all modules.
  ///
  /// After this is called, no more modules can be registered.
  /// Returns a new [ModuleRegistry] instance.
  ///
  /// Throws a [StateError] if the registry has already been built.
  ModuleRegistry build() {
    if (_isBuilt) {
      throw StateError('Registry is already built');
    }

    // Perform comprehensive validation of all modules
    _validateAllModules();

    _isBuilt = true;
    // Create a new ModuleRegistry instance
    final registry = ModuleRegistry(_getIt, _moduleRegistrations);
    return registry;
  }

  /// Validates all module relationships and dependencies.
  ///
  /// This method performs comprehensive validation of all modules, including
  /// checking for duplicate module names and circular dependencies.
  ///
  /// Throws an exception if any validation errors are found.
  void _validateAllModules() {
    // Check for duplicate module names across different sources
    _validateModuleNames();

    // Validate dependency graph for circular dependencies
    _validateDependencyGraph();
  }

  /// Validates all module names for uniqueness.
  ///
  /// Throws an exception if any duplicate module names are found.
  void _validateModuleNames() {
    // Group modules by name
    final Map<String, List<ModuleRegistration>> modulesByName = <String, List<ModuleRegistration>>{};

    for (final MapEntry<String, ModuleRegistration> entry in _moduleRegistrations.entries) {
      modulesByName.putIfAbsent(entry.key, () => <ModuleRegistration>[]).add(entry.value);
    }

    // Find duplicates
    final List<MapEntry<String, List<ModuleRegistration>>> duplicates = modulesByName.entries
        .where((MapEntry<String, List<ModuleRegistration>> entry) => entry.value.length > 1)
        .toList();

    if (duplicates.isNotEmpty) {
      final String errorMessage = 'Duplicate module names detected:\n${duplicates.map((MapEntry<String, List<ModuleRegistration>> entry) {
        final String modulesInfo = entry.value.map((ModuleRegistration reg) =>
            '${reg.module.runtimeType} from "${reg.source}"').join(', ');
        return '- "${entry.key}" is used by: $modulesInfo';
      }).join('\n')}';

      throw Exception(errorMessage);
    }
  }

  /// Validates the dependency graph for circular dependencies.
  ///
  /// This method builds a dependency graph and checks for cycles using
  /// depth-first search. If a cycle is detected, an exception is thrown
  /// with details about the cycle.
  void _validateDependencyGraph() {
    // Build a dependency graph
    final Map<String, Set<String>> dependencyGraph = <String, Set<String>>{};
    final Map<Type, String> modulesByType = <Type, String>{};

    // Map types to providing modules
    for (final ModuleRegistration registration in _moduleRegistrations.values) {
      final List<Type> dependencies = registration.module.dependencies ?? <Type>[];
      for (final Type type in dependencies) {
        modulesByType[type] = registration.module.name;
      }
    }

    // Build the dependency graph
    for (final ModuleRegistration registration in _moduleRegistrations.values) {
      final String moduleName = registration.module.name;
      final Set<String> moduleDependencies = <String>{};
      final List<Type> dependencies = registration.module.dependencies ?? <Type>[];

      for (final Type typeDependency in dependencies) {
        final String? provider = modulesByType[typeDependency];
        if (provider != null && provider != moduleName) {
          moduleDependencies.add(provider);
        }
      }

      dependencyGraph[moduleName] = moduleDependencies;
    }

    // Check for circular dependencies
    final Set<String> visited = <String>{};
    final Set<String> recursionStack = <String>{};

    for (final String moduleName in dependencyGraph.keys) {
      if (_detectCycle(moduleName, dependencyGraph, visited, recursionStack)) {
        final List<String> cycle = recursionStack.toList();
        final String cycleStr = <String>[...cycle, cycle.first].join(' → ');

        throw Exception('Circular dependency detected in module graph. '
            'Dependency cycle: $cycleStr');
      }
    }
  }

  /// Detects cycles in the dependency graph using depth-first search.
  ///
  /// Returns true if a cycle is detected, false otherwise.
  bool _detectCycle(
    String moduleName,
    Map<String, Set<String>> graph,
    Set<String> visited,
    Set<String> recursionStack
  ) {
    // If not visited, mark as visited and add to recursion stack
    if (!visited.contains(moduleName)) {
      visited.add(moduleName);
      recursionStack.add(moduleName);

      // Visit all dependencies
      for (final String dependency in graph[moduleName] ?? <String>{}) {
        // If dependency is in recursion stack, we found a cycle
        if (recursionStack.contains(dependency)) {
          recursionStack.add(dependency); // Add to show the complete cycle
          return true;
        }

        // If dependency not visited and leads to a cycle
        if (!visited.contains(dependency) &&
            _detectCycle(dependency, graph, visited, recursionStack)) {
          return true;
        }
      }
    }

    // Remove from recursion stack when backtracking
    recursionStack.remove(moduleName);
    return false;
  }
}
