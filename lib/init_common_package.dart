// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import 'base/module/common_package_modules/analytics_module.dart';
import 'base/module/common_package_modules/core_module.dart';
import 'base/module/common_package_modules/data_collection_module.dart';
import 'base/module/common_package_modules/device_info_module.dart';
import 'base/module/common_package_modules/ekyc_module.dart';
import 'base/module/common_package_modules/network_module.dart';
import 'base/module/common_package_modules/notification_module.dart';
import 'base/module/common_package_modules/ui_module.dart';
import 'base/module/common_package_modules/utility_module.dart';

import 'base/module/feature_module.dart';
import 'base/module/module_registry.dart';
import 'base/module/module_registry_builder.dart';
import 'util/utils.dart';

/// Global GetIt instance for dependency injection
final GetIt getIt = GetIt.instance;

// Internal reference to the module registry
// This is not intended to be accessed directly by host applications
@visibleForTesting
late ModuleRegistry moduleRegistry;

/// Initializes the common package with all features or selectively.
///
/// This method allows selective initialization of features based on the
/// application's needs. By default, it initializes all core features.
///
/// IMPORTANT: Host applications should ONLY use the methods provided in this file
/// to interact with the module system. Direct usage of ModuleRegistry is not
/// supported for host applications and may break in future versions.
///
/// Parameters:
/// - [locale]: Used for localization in HTTP requests.
/// - [features]: Specifies which features to initialize:
///   - If null: All modules will be initialized (default behavior)
///   - If empty list: No modules will be initialized
///   - If specific modules: Only those modules (and their dependencies) will be initialized
///
/// All modules are initialized immediately during this call. The initialization
/// process respects module dependencies, ensuring that if module A depends on
/// module B, module B will be initialized first.
///
/// Throws an [Exception] if module initialization fails or if no valid modules
/// are found in the provided features list.
///
/// Example:
/// ```dart
/// // Initialize all modules (most common usage)
/// await initCommonPackage();
///
/// // Initialize only specific modules
/// await initCommonPackage(
///   features: [
///     CommonPackageModuleNames.core,
///     CommonPackageModuleNames.network,
///     CommonPackageModuleNames.ui,
///   ],
/// );
///
/// // Initialize no modules (just register them)
/// await initCommonPackage(features: []);
/// // Later, initialize specific modules when needed
/// await initializeSpecificModules([CommonPackageModuleNames.network]);
///
/// // Initialize with locale for localized network requests
/// await initCommonPackage(
///   locale: Locale('en', 'US'),
///   features: [CommonPackageModuleNames.network],
/// );
/// ```
Future<void> initCommonPackage({
  Locale? locale,
  List<String>? features,
}) async {
  try {
    // Create a registry builder
    final ModuleRegistryBuilder builder = ModuleRegistry.builder(getIt);

    // Register all modules
    registerAllCommonModules(builder, locale: locale);

    // Build the registry (validation happens automatically)
    moduleRegistry = builder.build();

    // Initialize modules based on features parameter
    if (features == null) {
      // Initialize all modules
      'initCommonPackage'.commonLog('Initializing all modules');
      await moduleRegistry.initializeAllRegisteredModules();
    } else if (features.isEmpty) {
      // No modules to initialize
      'initCommonPackage'.commonLog('No modules to initialize');
      await moduleRegistry.initializeNoModules();
    } else {
      // Initialize specific modules
      'initCommonPackage'.commonLog('Initializing specific modules: $features');
      await moduleRegistry.initializeSpecificModules(features);
    }
  } catch (e) {
    // Log the error and rethrow
    'initCommonPackage'.commonLog('Error initializing common package: $e');
    rethrow;
  }
}

/// Registers all available modules with the module registry builder.
///
/// This method is exposed for testing purposes only and should not be called
/// directly by host applications. Host applications should use the
/// [initCommonPackage] method instead.
@visibleForTesting
void registerAllCommonModules(ModuleRegistryBuilder builder, {Locale? locale}) {
  const String source = 'common_package';

  // Register core module (always required)
  builder.register(CoreModule(), source: source);

  // Register feature modules
  builder.register(NetworkModule(locale: locale), source: source);
  builder.register(AnalyticsModule(), source: source);
  builder.register(DeviceInfoModule(), source: source);
  builder.register(DataCollectionModule(), source: source);
  builder.register(NotificationModule(), source: source);
  builder.register(UiModule(), source: source);
  builder.register(UtilityModule(), source: source);
  builder.register(EkycModule(), source: source);
}

/// Gets the list of all registered modules.
///
/// Returns an unmodifiable list of module names that have been registered
/// with the module registry.
///
/// This is useful for debugging or for dynamically determining which modules
/// are available in the application.
///
/// Example:
/// ```dart
/// final registeredModules = getRegisteredModules();
/// print('Registered modules: $registeredModules');
/// ```
///
/// Throws a [StateError] if called before [initCommonPackage].
List<String> getRegisteredModules() {
  _checkModuleRegistryInitialized();
  return List.unmodifiable(moduleRegistry.registeredModules);
}

/// Gets the list of all initialized modules.
///
/// Returns an unmodifiable list of module names that have been initialized.
/// This can be used to check which modules are currently active in the application.
///
/// Example:
/// ```dart
/// final initializedModules = getInitializedModules();
/// print('Initialized modules: $initializedModules');
/// ```
///
/// Throws a [StateError] if called before [initCommonPackage].
List<String> getInitializedModules() {
  _checkModuleRegistryInitialized();
  return List.unmodifiable(moduleRegistry.initializedModules);
}

/// Checks if a specific module is initialized.
///
/// Returns true if the module with the given [moduleName] is initialized,
/// false otherwise. This is useful for conditionally executing code based
/// on whether a module is available.
///
/// Example:
/// ```dart
/// if (isModuleInitialized(CommonPackageModuleNames.analytics)) {
///   // Use analytics features
/// } else {
///   // Analytics not available, use alternative approach
/// }
/// ```
///
/// Throws a [StateError] if called before [initCommonPackage].
bool isModuleInitialized(String moduleName) {
  _checkModuleRegistryInitialized();
  return moduleRegistry.isModuleInitialized(moduleName);
}

/// Checks if a specific module is registered.
///
/// Returns true if the module with the given [moduleName] is registered,
/// false otherwise. This can be used to check if a module is available
/// before attempting to initialize it.
///
/// Example:
/// ```dart
/// if (isModuleRegistered(CommonPackageModuleNames.ekyc)) {
///   await initializeSpecificModules([CommonPackageModuleNames.ekyc]);
/// }
/// ```
///
/// Throws a [StateError] if called before [initCommonPackage].
bool isModuleRegistered(String moduleName) {
  _checkModuleRegistryInitialized();
  return moduleRegistry.isModuleRegistered(moduleName);
}

/// Initializes all registered modules.
///
/// This method initializes all modules that have been registered with the registry.
/// It's useful when using lazy initialization and you later decide to initialize
/// all available modules.
///
/// Example:
/// ```dart
/// // First, register modules without initializing them
/// await initCommonPackage(lazyInitialization: true);
///
/// // Later, initialize all modules
/// await initializeAllModules();
/// ```
///
/// Returns a [Future] that completes when all modules have been initialized.
///
/// Throws a [StateError] if called before [initCommonPackage].
Future<void> initializeAllRegisteredModules() async {
  _checkModuleRegistryInitialized();
  await moduleRegistry.initializeAllRegisteredModules();
}

/// Initializes specific modules by name.
///
/// This method initializes only the specified modules. If a module depends on
/// other modules, those dependencies will be initialized first automatically.
///
/// This is particularly useful with lazy initialization to selectively initialize
/// modules only when they are needed.
///
/// Example:
/// ```dart
/// // First, register modules without initializing them
/// await initCommonPackage(lazyInitialization: true);
///
/// // Later, initialize only specific modules when needed
/// await initializeSpecificModules([
///   CommonPackageModuleNames.network,
///   CommonPackageModuleNames.analytics
/// ]);
/// ```
///
/// Returns a [Future] that completes when all specified modules have been initialized.
///
/// Throws a [StateError] if called before [initCommonPackage].
/// Throws an [ArgumentError] if the module names list is empty.
/// Throws an [Exception] if none of the specified modules are registered.
Future<void> initializeSpecificModules(List<String> moduleNames) async {
  _checkModuleRegistryInitialized();
  await moduleRegistry.initializeSpecificModules(moduleNames);
}

/// Initializes no modules (no-op).
///
/// This method does nothing and is provided for API completeness.
/// It's useful in conditional initialization scenarios where you might
/// want to explicitly indicate that no modules should be initialized.
///
/// Example:
/// ```dart
/// if (shouldInitializeModules) {
///   await initializeSpecificModules([CommonPackageModuleNames.network]);
/// } else {
///   await initializeNoModules(); // Explicit no-op for clarity
/// }
/// ```
///
/// Returns a completed [Future].
///
/// Throws a [StateError] if called before [initCommonPackage].
Future<void> initializeNoModules() async {
  _checkModuleRegistryInitialized();
  await moduleRegistry.initializeNoModules();
}

/// Registers a custom module with the module registry.
///
/// This method allows host applications to register their own custom modules
/// with the module registry. The module will be registered but not initialized.
/// To initialize the module, call [initializeSpecificModules] with the module's name.
///
/// The [source] parameter identifies where the module comes from (e.g., 'host_app')
/// to provide clearer error messages and ensure module name uniqueness across
/// different sources.
///
/// Example:
/// ```dart
/// // Create a custom module
/// class AuthModule implements FeatureModule {
///   @override
///   String get name => 'auth';
///
///   @override
///   List<Type> get dependencies => [AuthService];
///
///   @override
///   Future<void> register(GetIt getIt) async {
///     getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl());
///   }
/// }
///
/// // Register the custom module
/// registerCustomModule(AuthModule(), source: 'host_app');
///
/// // Initialize the custom module
/// await initializeSpecificModules(['auth']);
/// ```
///
/// Throws a [StateError] if called before [initCommonPackage].
/// Throws an [Exception] if a module with the same name is already registered
/// from a different source or with a different type.
void registerCustomModule(FeatureModule module, {required String source}) {
  _checkModuleRegistryInitialized();
  moduleRegistry.registerModule(module, source: source);
}

/// Registers and initializes a custom module with the module registry.
///
/// This method allows host applications to register and immediately initialize
/// their own custom modules with the module registry. This is a convenience method
/// that combines [registerCustomModule] and [initializeSpecificModules].
///
/// The [source] parameter identifies where the module comes from (e.g., 'host_app')
/// to provide clearer error messages and ensure module name uniqueness across
/// different sources.
///
/// Example:
/// ```dart
/// // Create a custom module
/// class AuthModule implements FeatureModule {
///   @override
///   String get name => 'auth';
///
///   @override
///   List<Type> get dependencies => [AuthService];
///
///   @override
///   Future<void> register(GetIt getIt) async {
///     getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl());
///   }
/// }
///
/// // Register and initialize the custom module
/// await registerAndInitializeCustomModule(AuthModule(), source: 'host_app');
/// ```
///
/// Throws a [StateError] if called before [initCommonPackage].
/// Throws an [Exception] if a module with the same name is already registered
/// from a different source or with a different type.
Future<void> registerAndInitializeCustomModule(FeatureModule module, {required String source}) async {
  _checkModuleRegistryInitialized();
  moduleRegistry.registerModule(module, source: source);
  await moduleRegistry.initializeSpecificModules([module.name]);
}

/// Checks if the module registry has been initialized.
///
/// This is an internal helper method used by the public API methods to ensure
/// that the module registry has been properly initialized before attempting
/// to use it.
///
/// Throws a [StateError] with a descriptive message if the module registry
/// has not been initialized, guiding the developer to call initCommonPackage() first.
@visibleForTesting
void checkModuleRegistryInitialized() {
  try {
    // This will throw if moduleRegistry is not initialized
    moduleRegistry.toString();
  } catch (e) {
    throw StateError(
      'Module registry not initialized. You must call initCommonPackage() before '
      'using any module-related functions. For example:\n'
      '```dart\n'
      'await initCommonPackage();\n'
      '// Now you can use module-related functions\n'
      '```\n\n'
      'IMPORTANT: Host applications should ONLY use the methods provided in this file '
      'to interact with the module system. Direct usage of ModuleRegistry is not '
      'supported for host applications and may break in future versions.',
    );
  }
}

// Alias to the public method for backward compatibility
void _checkModuleRegistryInitialized() {
  checkModuleRegistryInitialized();
}

