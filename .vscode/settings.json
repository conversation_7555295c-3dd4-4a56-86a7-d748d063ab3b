// VSCode workspace settings that are shared among all users of this project.
// This only affects subdirectories of this project.
{
    // VSCode formats files on save by default. Since Flutter source code is
    // hand-formatted, the default settings are changed to prevent inadvertent
    // reformatting of code.
    "[dart]": {
        "editor.formatOnSave": false,
        "editor.formatOnType": false,
        "editor.formatOnPaste": false,
    },

    "html.format.enable": false,
    "githubPullRequests.ignoredPullRequestBranches": [
        "master"
    ],
    "files.trimTrailingWhitespace": true
}
