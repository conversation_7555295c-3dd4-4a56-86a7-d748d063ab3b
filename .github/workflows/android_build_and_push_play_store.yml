name: Build Android

on:
  workflow_call:
    inputs:
      VAULT_KEY:
        required: true
        type: string
      ENVIRONMENT:
        required: true
        type: string
      BUILD_ENV:
        required: true
        type: string
      FLUTTER_VERSION:
        required: true
        type: string
      REPOSITORY:
        required: true
        type: string
      BUILD_NUMBER:
        required: true
        type: string
      BUILD_NUMBER_VAR_NAME:
        required: true
        type: string
      PACKAGE_NAME:
        required: true
        type: string
      PLAY_STORE_TRACK:
        required: true
        type: string
      PLAY_STORE_TRACK_STATUS:
        required: true
        type: string
      FIREBASE_APP_ID:
        required: true
        type: string
      JAVA_VERSION:
        required: false
        type: string
        default: "17"
    secrets:
      ORG_CI_TS_TOKEN:
        required: true
      SLACK_WEBHOOK_URL:
        required: true

jobs:
  build:
    runs-on: dedicated-runner
    timeout-minutes: 30
    environment: ${{ inputs.ENVIRONMENT }}
    strategy:
      fail-fast: false
      matrix:
        target: [ "appbundle" ]
        include:
          - os: dedicated-runner
            target: appbundle

    permissions:
      contents: read
      id-token: write
      actions: read
      pull-requests: write

    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      KEYSTORE_FOLDER: './android/app/keystore'
      KEYSTORE_FILE_NAME: 'evo_keystore.jks'
      GITHUB_TOKEN: ${{ secrets.ORG_CI_TS_TOKEN }}
      SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
      SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
      SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
      KEYSTORE: ${{ secrets.KEYSTORE }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.ORG_CI_TS_TOKEN }}

      - name: Marked the directory as safe so all users able to run it
        run: git config --global --add safe.directory '*'

      - name: Set outputs
        id: vars
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea
        with:
          github-token: ${{ secrets.ORG_CI_TS_TOKEN }}
          script: |
            const sha_short = context.sha.slice(0,7)
            const result = await github.rest.git.getCommit({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha,
            });
            const message = result.data.message.replace(/("|#|@|'|`|\(|\))/g, "\\$1") 
            const ticket = message.match(/[A-Z][A-Z0-9]+-[0-9]+/g)
            let jira = ""
            if (ticket && ticket.length > 0 ) {
              jira = `https://trustingsocial1.atlassian.net/browse/${ticket[0]}`
            } else {
              jira = "none"
            }
            core.setOutput('message', message);
            core.setOutput('jira', jira);
            core.setOutput('repo', context.repo.repo);
            core.setOutput('sha_short', sha_short);

      - name: Get git information
        id: git_info
        run: |
          MESSAGE=$(echo "${{ steps.vars.outputs.message }}")
          MESSAGE="${MESSAGE//'%'/'%25'}"
          MESSAGE="${MESSAGE//$'\n'/'%0A'}"
          MESSAGE="${MESSAGE//$'\r'/'%0D'}"
          echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "commit_message=${MESSAGE}" >> $GITHUB_OUTPUT

      - name: Set outputs build_name
        id: vars-build-name
        run: |
          echo "build_name=$(cat pubspec.yaml | grep ^version | sed -n 's/.*version: \([0-9.]*\).*/\1/p')" >> $GITHUB_OUTPUT

      - name: Set up JDK
        uses: actions/setup-java@v2
        with:
          distribution: "zulu"
          java-version: ${{ inputs.JAVA_VERSION }}

      - name: Setup Android SDK
        uses: android-actions/setup-android@v2

      - name: Decode Keystore for android
        id: decode_keystore
        if: ${{ always() }}
        uses: timheuer/base64-to-file@v1
        with:
          fileName: ${{ env.KEYSTORE_FILE_NAME }}
          fileDir: ${{ env.KEYSTORE_FOLDER }}
          encodedString: ${{ secrets.KEYSTORE }}

      - name: Set up Flutter
        uses: subosito/flutter-action@2783a3f08e1baf891508463f8c6653c258246225
        with:
          flutter-version: ${{ inputs.FLUTTER_VERSION }}
          channel: "stable"

      - name: Clean Cache
        run: flutter clean

      - name: Install dependencies
        run: flutter pub get
        timeout-minutes: 6

      - name: Update build number
        run: |
          versionCode=$(( ${{ inputs.BUILD_NUMBER }} + 1))
          echo "::set-output name=BUILD_NUMBER::$versionCode"
          sed -i "s/versionCode flutterVersionCode.toInteger()\+/versionCode $versionCode/" ./android/app/build.gradle
          cat ./android/app/build.gradle

      - name: Run unit test in background
        continue-on-error: true
        run: |
          sh ./.github/unit_test.sh
          sudo apt-get update
          sudo apt-get install lcov bc gawk -y

          # Run tests in background and save PID to environment variable
          flutter test --coverage &
          echo "TEST_PID=$!" >> $GITHUB_ENV
          echo "Running tests in background with PID: $!"

      - name: Remove mock file to avoid security risk
        if: ${{ always() }}
        id: remove_mock_file
        run: |
          sh ./.github/remove_mock_file.sh RELEASE

      - name: Flutter build
        run: |
          flutter build appbundle --release --build-name ${{ steps.vars-build-name.outputs.build_name }} --flavor ${{ inputs.BUILD_ENV }} -t lib/flavors/main_${{ inputs.BUILD_ENV }}.dart --obfuscate --split-debug-info=build/app/outputs/symbols/appbundle-${{ inputs.BUILD_ENV }}

      - name: Wait for unit test and calculate coverage
        id: test_coverage_info
        continue-on-error: true
        uses: tsocial/flutter-common-package/.github/actions/calculate-coverage@release/v4.x.x
        with:
          test_pid: ${{ env.TEST_PID }}

      - name: Coverage on changed file
        id: check_coverage_changed_file
        continue-on-error: true
        uses: tsocial/flutter-common-package/.github/actions/coverage-changed-file@release/v4.x.x

      - name: Get role name
        id: role
        run: echo "role_name=gha-$(echo ${{ github.event.repository.name }}-on-protected-branches | sed 's/_/-/g')" >> $GITHUB_OUTPUT

      - name: Retrieve secret from Vault
        id: vault
        uses: hashicorp/vault-action@v3
        with:
          method: jwt
          url: https://vault.tsengineering.io
          path: github-oidc
          role: ${{ steps.role.outputs.role_name }}
          secrets: |
            gcp/roleset/${{ inputs.VAULT_KEY }}/key private_key_data | SA_JSON_BASE64

      - name: Decode SA credentials
        id: auth
        run: |
          echo "${{ steps.vault.outputs.SA_JSON_BASE64 }}" | base64 -d > /tmp/sa.json
          echo "credentials_file_path=/tmp/sa.json" >> $GITHUB_OUTPUT

      - name: Install firebase CLI
        run: curl -sL https://firebase.tools | bash
      - name: Upload dart debug-symbols to Firebase Crashlytics
        run: firebase crashlytics:symbols:upload --app=${{inputs.FIREBASE_APP_ID}} build/app/outputs/symbols/appbundle-${{ inputs.BUILD_ENV }}
        env:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ steps.auth.outputs.credentials_file_path }}

      - name: Upload Play Store
        id: upload-play-store
        uses: r0adkll/upload-google-play@v1.1.3
        with:
          serviceAccountJson: ${{ steps.auth.outputs.credentials_file_path }}
          packageName: ${{ inputs.PACKAGE_NAME }}
          releaseFiles: build/app/outputs/bundle/${{ inputs.BUILD_ENV }}Release/app-${{ inputs.BUILD_ENV }}-release.aab
          track: ${{ inputs.PLAY_STORE_TRACK }}
          status: ${{ inputs.PLAY_STORE_TRACK_STATUS }}
          mappingFile: build/app/outputs/mapping/${{ inputs.BUILD_ENV }}Release/mapping.txt
          debugSymbols: build/app/intermediates/merged_native_libs/${{ inputs.BUILD_ENV }}Release/out/lib

      - name: Set final result
        id: final_result
        if: ${{ always() }}
        run: |
          final_result="failure"
          if [ "${{ steps.upload-play-store.outcome }}" == "success" ]; then
            final_result="success"
          fi
          echo "FINAL_RESULT=$final_result" >> $GITHUB_ENV

      - name: Increment and save build number
        if: ${{ env.FINAL_RESULT }}' === 'success'
        uses: action-pack/increment@v2
        with:
          name: ${{ inputs.BUILD_NUMBER_VAR_NAME }}
          token: ${{ secrets.ORG_CI_TS_TOKEN }}
          repository: ${{ inputs.REPOSITORY }}

      - name: Slack notify
        uses: 8398a7/action-slack@v3
        if: ${{ always() }}
        with:
          status: custom
          fields: workflow,job,commit,repo,ref,author,took,message
          custom_payload: |
            {
              attachments: [{
                color: '${{ env.FINAL_RESULT }}' === 'success' ? 'good' : 'danger',
                text: "*<https://github.com/tsocial/${{ github.event.repository.name }}|tsocial/${{ github.event.repository.name }}>*\n" +
                      "*<@${{ github.actor }}>* has built and pushed *`appbundle`* to *`${{ inputs.BUILD_ENV }} - Release`* with the commit hash *`${{ steps.vars.outputs.sha_short }}`* , status: *${{ env.FINAL_RESULT }}*!\n" +
                      "• <https://github.com/tsocial/${{ github.event.repository.name }}/commit/${{ steps.vars.outputs.sha_short }}|${{ steps.git_info.outputs.commit_message }}>\n" +
                      "• <https://github.com/tsocial/${{ github.event.repository.name }}/actions/runs/${{ github.run_id }}|Workflow>\n" +
                      "• ${{ format('Test coverage: Full: *`{0}%`*; Non-screen: *`{1}%`*; Non-widget: *`{2}%`*; Changed-file: *`{3}`*', steps.test_coverage_info.outputs.full_test_coverage, steps.test_coverage_info.outputs.excluded_screen_test_coverage, steps.test_coverage_info.outputs.logic_only_test_coverage, steps.check_coverage_changed_file.outputs.changed_file_coverage) }}",
              }]
            }

      - name: Delete current folder after build
        if: ${{ always() }}
        run: rm -rf *
