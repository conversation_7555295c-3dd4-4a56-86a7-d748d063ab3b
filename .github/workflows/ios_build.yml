name: Reusable iOS Build

on:
  workflow_call:
    inputs:
      APPSTORE_APP_ID:
        required: true
        type: string
      FLAVOR:
        required: true
        type: string
      ENVIRONMENT:
        required: true
        type: string
      FLUTTER_VERSION:
        required: true
        type: string
      RUNNER:
        required: false
        type: string
        default: 'self-hosted-macos-sonoma'
      XCODE_VERSION:
        required: false
        type: string
        default: '15.4'
    secrets:
      ORG_CI_TS_TOKEN:
        required: true
      SLACK_WEBHOOK_URL:
        required: true
      PROVISIONING_APP_BASE64_CONTENT:
        required: true
      PROVISIONING_ONESIGNAL_BASE64_CONTENT:
        required: true
      P12_BASE64_CONTENT:
        required: true
      KEY_CHAIN_PASS:
        required: true
      CER_PASS:
        required: true
      P8_CONTENT:
        required: true
      P8_KEY_ID:
        required: true
      P8_ISSUER_ID:
        required: true

jobs:
  build:
    name: Build ipa
    if: ${{ !cancelled() }} # stop immediately when user press 'cancel workflow', https://docs.github.com/en/actions/managing-workflow-runs-and-deployments/managing-workflow-runs/canceling-a-workflow
    runs-on: ${{ inputs.RUNNER }}
    timeout-minutes: 45
    environment: ${{ inputs.ENVIRONMENT }}
    outputs:
      sha_short: ${{ steps.git_info.outputs.sha_short }}
      commit_message: ${{ steps.git_info.outputs.commit_message }}
      quote_text: ${{ env.quote }}
    strategy:
      fail-fast: false

    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      GITHUB_TOKEN: ${{ secrets.ORG_CI_TS_TOKEN }}

    steps:
      - name: Print macOS system info
        run: |
          echo "=== System Information ==="
          sw_vers
          echo "=== CPU Information ==="
          sysctl -n machdep.cpu.brand_string
          echo "Number of cores: $(sysctl -n hw.ncpu)"
      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: ${{ inputs.XCODE_VERSION }}

      - name: Verify Xcode Version
        run: xcodebuild -version

      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0.2'
          bundler-cache: false
          working-directory: ./ios

      - name: Setup jq
        uses: dcarbone/install-jq-action@v2
        with:
          version: 1.7
          force: true

      - name: Set outputs
        id: vars
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7
        with:
          github-token: ${{ secrets.ORG_CI_TS_TOKEN }}
          script: |
            const sha_short = context.sha.slice(0,7)
            const result = await github.rest.git.getCommit({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha,
            });
            const message = result.data.message.replace(/("|#|@|'|`|\(|\))/g, "\\$1")
            const ticket = message.match(/[A-Z][A-Z0-9]+-[0-9]+/g)
            let jira = ""
            if (ticket && ticket.length > 0 ) {
              jira = `https://trustingsocial1.atlassian.net/browse/${ticket[0]}`
            } else {
              jira = "none"
            }
            core.setOutput('message', message);
            core.setOutput('jira', jira);
            core.setOutput('repo', context.repo.repo);
            core.setOutput('sha_short', sha_short);

      - name: Set outputs build_name
        id: vars-build-name
        run: |
          echo "build_name=$(cat pubspec.yaml | grep ^version | sed -n 's/.*version: \([0-9.]*\).*/\1/p')" >> $GITHUB_OUTPUT

      - name: Get git information
        id: git_info
        run: |
          MESSAGE=$(echo "${{ steps.vars.outputs.message }}")
          MESSAGE="${MESSAGE//'%'/'%25'}"
          MESSAGE="${MESSAGE//$'\n'/'%0A'}"
          MESSAGE="${MESSAGE//$'\r'/'%0D'}"

          echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "commit_message=${MESSAGE}" >> $GITHUB_OUTPUT

      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ inputs.FLUTTER_VERSION }}
          channel: "stable"

      - name: Clean Cache
        run: flutter clean

      - name: Setup git for private repos
        run: |
          rm -f ~/.gitconfig
          git config --global url.https://$<EMAIL>/.insteadOf https://github.com/

      - name: Install dependencies
        run: flutter pub get
        timeout-minutes: 6

      - name: Decrypt app provisioning file
        run: sh ./.github/decrypt_provisioning.sh
        env:
          PROVISIONING_BASE64_CONTENT: ${{ secrets.PROVISIONING_APP_BASE64_CONTENT }}
          PROVISIONING_FILE_NAME: "runner_app"

      - name: Decrypt OneSignal provisioning file
        run: sh ./.github/decrypt_provisioning.sh
        env:
          PROVISIONING_BASE64_CONTENT: ${{ secrets.PROVISIONING_ONESIGNAL_BASE64_CONTENT }}
          PROVISIONING_FILE_NAME: "onesignal_extension"

      - name: Decrypt p12 file
        run: sh ./.github/decrypt_p12.sh
        env:
          P12_BASE64_CONTENT: ${{ secrets.P12_BASE64_CONTENT }}

      - name: Build and upload to TestFlight using fastlane
        uses: maierj/fastlane-action@v2.3.0
        with:
          subdirectory: 'ios'
          lane: 'build_ipa_release'
          bundle-install-path: './vendor/bundle'
          options: '{"flavor":"${{ inputs.FLAVOR }}","keychainPass":"${{ secrets.KEY_CHAIN_PASS }}","cerPass":"${{ secrets.CER_PASS }}","p8Content":"${{ secrets.P8_CONTENT }}","p8KeyId":"${{ secrets.P8_KEY_ID }}","p8IssuerId":"${{ secrets.P8_ISSUER_ID }}"}'

      - name: Set up quote messages
        if: always()
        run: |
          if [ -n "${{ github.event.client_payload.service }}" ]; then
            echo 'message=`[${{env.TEST_ENV}}]` :rocket::rocket::rocket: *${{ github.event.client_payload.service }}* • <${{ github.event.client_payload.commit }}|commit>' >> $GITHUB_ENV
          else
            echo 'message=`[${{env.TEST_ENV}}]` :rocket::rocket::rocket:' >> $GITHUB_ENV
          fi

          # Read quotes into an array
          quotes=()
          while IFS= read -r line; do
            quotes+=("$line")
          done < .github/workflows/quotes.csv

          # Select a random quote
          index=$((RANDOM % ${#quotes[@]}))
          echo "quote=${quotes[$index]}" >> $GITHUB_ENV

      - name: Store workflow logs to Github Action
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: workflow-logs
          path: /Users/<USER>/Library/Logs/gym/Runner-${{ inputs.FLAVOR }}.log
          retention-days: 90

      - name: clean xcode archive
        if: ${{ always() }}
        run: find ~/Library/Developer/Xcode/Archives -depth 1 -name "*" -type d | xargs rm -rf

      - name: Delete current folder after build
        if: ${{ always() }}
        run: rm -rf *

  sleep_then_notify:
    name: Waiting for Apple review (Sleep)
    needs: build
    runs-on: self-hosted-cpu
    if: ${{ !cancelled() }} # only run if the workflow is not cancelled
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - name: Sleep for 15 mins
        if: ${{needs.build.result == 'success'}} # only sleep if the build job is success
        run: sleep 900s

      - name: Set Slack message
        id: slack_message
        run: |
          build_status="${{ needs.build.result }}"
          distribute_text="*<https://github.com/tsocial/${{ github.event.repository.name }}|tsocial/${{ github.event.repository.name }}>*: *${{ github.actor }}* has built and pushed *\`ipa\`* to *\`${{ inputs.ENVIRONMENT }}\`* with the commit hash *\`${{ needs.build.outputs.sha_short }}\`*, status: *${build_status}*!\n • <https://github.com/tsocial/${{ github.event.repository.name }}/commit/${{ needs.build.outputs.sha_short }}|${{ needs.build.outputs.commit_message }}>\n • <https://github.com/tsocial/${{ github.event.repository.name }}/actions/runs/${{ github.run_id }}|Workflow> • <https://appstoreconnect.apple.com/apps/${{ inputs.APPSTORE_APP_ID }}/testflight/ios|TestFlight ${{ inputs.FLAVOR }}>"
          coverage_text=" • Test coverage: Check Android build report"
          quote_text="*${{ needs.build.outputs.quote_text }}*"

          SLACK_MESSAGE="${quote_text}\n${distribute_text}\n${coverage_text}"
          echo "build_status: $build_status"
          echo "SLACK_MESSAGE: $SLACK_MESSAGE"
          echo "SLACK_MESSAGE=$SLACK_MESSAGE" >> $GITHUB_ENV

      - name: Slack notify
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,job,commit,repo,ref,author,took,message
          custom_payload: |
            {
              attachments: [{
                color: "${{ needs.build.result == 'success' && 'good' || 'danger' }}",
                text: "${{ env.SLACK_MESSAGE }}"
              }]
            }