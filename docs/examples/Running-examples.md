To run an example, switch to that example's directory, and use `flutter run`.
Make sure you have an emulator running, or a device connected over USB and
debugging enabled on that device.

 * `cd examples/hello_world`
 * `flutter run`

You can also specify a particular Dart file to run if you want to run an example
that doesn't have a `lib/main.dart` file using the `-t` command-line option. For
example, to run the `widgets/spinning_square.dart` example in the [examples/layers](https://github.com/flutter/flutter/tree/main/examples/layers)
directory on a connected Android device, from that directory you would run:
`flutter run -t widgets/spinning_square.dart`