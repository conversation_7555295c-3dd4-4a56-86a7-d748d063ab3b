import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/webview/builder/webview_builder.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late WebViewUiBuilder webViewUiBuilder;

  setUp(() {
    webViewUiBuilder = WebViewUiBuilder();
  });

  group('verify nextActionWidget() method', () {
    test(
        'verify nextActionWidget() when isShowNextActionWidget = false and nextActionWidget is null',
        () {
      final Widget widget = webViewUiBuilder.nextActionWidget();
      expect(widget, isA<SizedBox>());
    });

    test(
        'verify nextActionWidget() when isShowNextActionWidget = false and nextActionWidget is not null',
        () {
      const String nextActionText = 'Next Action';
      const Widget nextActionWidget = Text(nextActionText);

      final Widget widget = webViewUiBuilder.nextActionWidget(nextActionWidget: nextActionWidget);
      expect(widget, isA<SizedBox>());
    });

    test(
        'verify nextActionWidget() when isShowNextActionWidget = true and nextActionWidget is null',
        () {
      final Widget widget = webViewUiBuilder.nextActionWidget(isShowNextActionWidget: true);
      expect(widget, isA<SizedBox>());
    });

    test(
        'verify nextActionWidget() when isShowNextActionWidget = true and nextActionWidget is not null',
        () {
      const String nextActionText = 'Next Action';
      const Widget nextActionWidget = Text(nextActionText);

      final Widget widget = webViewUiBuilder.nextActionWidget(isShowNextActionWidget: true, nextActionWidget: nextActionWidget);
      expect(widget, nextActionWidget);
    });
  });

  group('verify errorWidget() method', () {
    test('verify errorWidget() when isShowErrorView = false and errorWidget is null', () {
      final Widget widget = webViewUiBuilder.errorWidget();
      expect(widget, isA<SizedBox>());
    });

    test('verify errorWidget() when isShowErrorView = false and errorWidget is not null', () {
      const String errorText = 'Error';
      const Widget errorWidget = Text(errorText);

      final Widget widget = webViewUiBuilder.errorWidget(errorWidget: errorWidget);
      expect(widget, isA<SizedBox>());
    });

    test('verify errorWidget() when isShowErrorView = true and errorWidget is null', () {
      final Widget widget = webViewUiBuilder.errorWidget(isShowErrorView: true);
      expect(widget, isA<SizedBox>());
    });

    test('verify errorWidget() when isShowErrorView = true and errorWidget is not null', () {
      const String errorText = 'Error';
      const Widget errorWidget = Text(errorText);

      final Widget widget =
          webViewUiBuilder.errorWidget(isShowErrorView: true, errorWidget: errorWidget);
      expect(widget, errorWidget);
    });
  });
}
