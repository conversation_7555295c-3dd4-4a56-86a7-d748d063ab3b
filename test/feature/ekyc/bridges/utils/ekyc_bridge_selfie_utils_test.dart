import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_image.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_image_direction.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/utils/ekyc_bridge_selfie_utils.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:trust_vision_plugin/result/selfie_image.dart';
import 'package:trust_vision_plugin/result/tv_image_class.dart'; // Adjust the import path as necessary

void main() {
  group('EkycBridgeSelfieImageUtils', () {
    test('convertTVSelfieImageToBridgeImage should handle flash mode', () {
      final EkycBridgeSelfieImageUtils utils = EkycBridgeSelfieImageUtils();
      final List<SelfieImage> images = <SelfieImage>[
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string1',
            encryptedImageHexString: 'encryptedHex1',
            imageId: 'imageId1',
            metadata: <String, dynamic>{'key1': 'value1'},
          ),
        ),
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string2',
            encryptedImageHexString: 'encryptedHex2',
            imageId: 'imageId2',
            metadata: <String, dynamic>{'key2': 'value2'},
          ),
        ),
      ];

      final List<EkycBridgeImage> result = utils.convertTVSelfieImageToBridgeImage(
        livenessMode: EkycBridgeLivenessMode.flash_16,
        images: images,
      );

      expect(result, hasLength(2));
      expect(result[0].direction, EkycBridgeImageDirection.closeFace);
      expect(result[1].direction, EkycBridgeImageDirection.farFace);
    });

    test('convertTVSelfieImageToBridgeImage should handle active mode', () {
      final EkycBridgeSelfieImageUtils utils = EkycBridgeSelfieImageUtils();
      final List<SelfieImage> images = <SelfieImage>[
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal1',
            metadata: <String, dynamic>{'key': 'value'},
          ),
          gestureImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'gesture1',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'right',
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal2',
            metadata: <String, dynamic>{'key': 'value'},
          ),
          gestureImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'gesture2',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'left',
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal3',
            metadata: <String, dynamic>{'key': 'value'},
          ),
          gestureImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'gesture3',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'up',
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal4',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'frontal',
      ];

      final List<EkycBridgeImage> result = utils.convertTVSelfieImageToBridgeImage(
        livenessMode: EkycBridgeLivenessMode.active,
        images: images,
      );

      expect(result.length, 7);

      expect(result[0].imageId, 'frontal1');
      expect(result[0].direction, EkycBridgeImageDirection.frontal);

      expect(result[1].imageId, 'gesture1');
      expect(result[1].direction, EkycBridgeImageDirection.right);

      expect(result[2].imageId, 'frontal2');
      expect(result[2].direction, EkycBridgeImageDirection.frontal);

      expect(result[3].imageId, 'gesture2');
      expect(result[3].direction, EkycBridgeImageDirection.left);

      expect(result[4].imageId, 'frontal3');
      expect(result[4].direction, EkycBridgeImageDirection.frontal);

      expect(result[5].imageId, 'gesture3');
      expect(result[5].direction, EkycBridgeImageDirection.up);

      expect(result[6].imageId, 'frontal4');
      expect(result[6].direction, EkycBridgeImageDirection.frontalMain);
    });

    test('convertTVSelfieImageToBridgeImage not handle other mode', () {
      final EkycBridgeSelfieImageUtils utils = EkycBridgeSelfieImageUtils();
      final List<SelfieImage> images = <SelfieImage>[
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string1',
            encryptedImageHexString: 'encryptedHex1',
            imageId: 'imageId1',
            metadata: <String, dynamic>{'key1': 'value1'},
          ),
        ),
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string2',
            encryptedImageHexString: 'encryptedHex2',
            imageId: 'imageId2',
            metadata: <String, dynamic>{'key2': 'value2'},
          ),
        ),
      ];

      final List<EkycBridgeImage> result = utils.convertTVSelfieImageToBridgeImage(
        livenessMode: EkycBridgeLivenessMode.passive,
        images: images,
      );

      expect(result, hasLength(0));
    });

    test('getDirectionOfSelfieImageByIndex should return correct direction', () {
      final EkycBridgeSelfieImageUtils utils = EkycBridgeSelfieImageUtils();

      expect(utils.getDirectionOfFlashSelfieImageByIndex(0), EkycBridgeImageDirection.closeFace);
      expect(utils.getDirectionOfFlashSelfieImageByIndex(1), EkycBridgeImageDirection.farFace);
      expect(utils.getDirectionOfFlashSelfieImageByIndex(2), isNull);
    });

    test('getGestureDirectionOfActiveSelfieImage should return correct direction', () {
      final EkycBridgeSelfieImageUtils utils = EkycBridgeSelfieImageUtils();

      expect(utils.getGestureDirectionOfActiveSelfieImage('left'), EkycBridgeImageDirection.left);
      expect(utils.getGestureDirectionOfActiveSelfieImage('right'), EkycBridgeImageDirection.right);
      expect(utils.getGestureDirectionOfActiveSelfieImage('up'), EkycBridgeImageDirection.up);
      expect(utils.getGestureDirectionOfActiveSelfieImage('down'), EkycBridgeImageDirection.down);
      expect(utils.getGestureDirectionOfActiveSelfieImage('unknown'), isNull);
    });

    test('createFlashSelfieTVImages should convert images correctly', () {
      final EkycBridgeSelfieImageUtils utils = EkycBridgeSelfieImageUtils();
      final List<SelfieImage> images = <SelfieImage>[
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string1',
            encryptedImageHexString: 'encryptedHex1',
            imageId: 'imageId1',
            metadata: <String, dynamic>{'key1': 'value1'},
          ),
        ),
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string2',
            encryptedImageHexString: 'encryptedHex2',
            imageId: 'imageId2',
            metadata: <String, dynamic>{'key2': 'value2'},
          ),
        ),
      ];

      final List<EkycBridgeImage> result = utils.createFlashSelfieTVImages(images);

      expect(result, hasLength(2));
      expect(result[0].direction, EkycBridgeImageDirection.closeFace);
      expect(result[1].direction, EkycBridgeImageDirection.farFace);
    });

    test('createActiveSelfieImages should convert images correctly', () {
      final EkycBridgeSelfieImageUtils utils = EkycBridgeSelfieImageUtils();
      final List<SelfieImage> images = <SelfieImage>[
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal1',
            metadata: <String, dynamic>{'key': 'value'},
          ),
          gestureImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'gesture1',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'right',
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal2',
            metadata: <String, dynamic>{'key': 'value'},
          ),
          gestureImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'gesture2',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'left',
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal3',
            metadata: <String, dynamic>{'key': 'value'},
          ),
          gestureImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'gesture3',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'up',
        SelfieImage(
          frontalImage: TVImageClass(
            rawImageBase64: 'base64string',
            encryptedImageHexString: 'encryptedHex',
            imageId: 'frontal4',
            metadata: <String, dynamic>{'key': 'value'},
          ),
        )..gestureType = 'frontal',
      ];

      final List<EkycBridgeImage> selfieImages = utils.createActiveSelfieImages(images);

      expect(selfieImages.length, 7);

      expect(selfieImages[0].imageId, 'frontal1');
      expect(selfieImages[0].direction, EkycBridgeImageDirection.frontal);

      expect(selfieImages[1].imageId, 'gesture1');
      expect(selfieImages[1].direction, EkycBridgeImageDirection.right);

      expect(selfieImages[2].imageId, 'frontal2');
      expect(selfieImages[2].direction, EkycBridgeImageDirection.frontal);

      expect(selfieImages[3].imageId, 'gesture2');
      expect(selfieImages[3].direction, EkycBridgeImageDirection.left);

      expect(selfieImages[4].imageId, 'frontal3');
      expect(selfieImages[4].direction, EkycBridgeImageDirection.frontal);

      expect(selfieImages[5].imageId, 'gesture3');
      expect(selfieImages[5].direction, EkycBridgeImageDirection.up);

      expect(selfieImages[6].imageId, 'frontal4');
      expect(selfieImages[6].direction, EkycBridgeImageDirection.frontalMain);
    });
  });
}
