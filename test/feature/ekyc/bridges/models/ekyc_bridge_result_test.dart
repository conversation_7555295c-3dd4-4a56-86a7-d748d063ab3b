import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_result.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EkycBridgeResult', () {
    test('succeed factory should create a successful result', () {
      final String data = 'Test Data';
      final EkycBridgeResult<String> result = EkycBridgeResult<String>.succeed(data: data);

      expect(result.isSuccess, isTrue);
      expect(result.data, data);
      expect(result.errorReason, isNull);
    });

    test('failed factory should create a failed result', () {
      final EkycBridgeErrorReason errorReason = EkycBridgeErrorReason.sessionExpired;
      final EkycBridgeResult<String> result = EkycBridgeResult<String>.failed(
        errorReason: errorReason,
      );

      expect(result.isSuccess, isFalse);
      expect(result.data, isNull);
      expect(result.errorReason, errorReason);
    });

    test('isSuccess should be true when errorReason is null', () {
      final EkycBridgeResult<String> result = EkycBridgeResult<String>.succeed(data: 'Test Data');

      expect(result.isSuccess, isTrue);
    });

    test('isSuccess should be false when errorReason is not null', () {
      final EkycBridgeErrorReason errorReason = EkycBridgeErrorReason.exceedLimit;
      final EkycBridgeResult<String> result = EkycBridgeResult<String>.failed(
        errorReason: errorReason,
      );

      expect(result.isSuccess, isFalse);
    });
  });
}
