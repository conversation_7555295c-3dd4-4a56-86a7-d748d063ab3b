import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:trust_vision_plugin/enums.dart';

class MockDevicePlatform extends Mock implements DevicePlatform {}

void main() {
  group('EkycBridgeLivenessMode', () {
    // Create a mock device platform
    final MockDevicePlatform mockDevicePlatform = MockDevicePlatform();

    // Mock the `getIt` locator to return the mock device platform
    setUp(() {
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);
      getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    });

    tearDown(() {
      getIt.unregister<DevicePlatform>();
    });

    test('should have correct values', () {
      expect(EkycBridgeLivenessMode.active.value, 'active');
      expect(EkycBridgeLivenessMode.passive.value, 'passive');
      expect(EkycBridgeLivenessMode.none.value, 'none');
      expect(EkycBridgeLivenessMode.flash.value, 'flash');
      expect(EkycBridgeLivenessMode.flash_edge.value, 'flash_edge');
      expect(EkycBridgeLivenessMode.flash_advanced.value, 'flash_advanced');
      expect(EkycBridgeLivenessMode.flash_8.value, 'flash_8');
      expect(EkycBridgeLivenessMode.flash_16.value, 'flash_16');
      expect(EkycBridgeLivenessMode.flash_32.value, 'flash_32');
    });

    test('getLivenessModeWrapperByPlatform returns correct value for Android', () {
      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
      expect(
        EkycBridgeLivenessMode.getFlashLivenessModeByPlatform(),
        EkycBridgeLivenessMode.flash_32,
      );
    });

    test('getLivenessModeWrapperByPlatform returns correct value for iOS', () {
      when(() => mockDevicePlatform.isIOS()).thenReturn(true);
      expect(
        EkycBridgeLivenessMode.getFlashLivenessModeByPlatform(),
        EkycBridgeLivenessMode.flash_16,
      );
    });

    for (final EkycBridgeLivenessMode modeWrapper in EkycBridgeLivenessMode.values) {
      test('toTvSdkLivenessMode returns correct value for ${modeWrapper.toString()}', () {
        final LivenessMode livenessMode = modeWrapper.toTVSDKLivenessMode();
        expect(livenessMode.toString().split('.').last, modeWrapper.toString().split('.').last);
      });
    }
  });
}
