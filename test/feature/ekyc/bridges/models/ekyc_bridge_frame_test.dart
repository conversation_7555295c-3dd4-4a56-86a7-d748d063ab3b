import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_frame.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EkycBridgeFrame', () {
    test('constructor should initialize all properties', () {
      final String base64 = 'base64string';
      final int index = 1;
      final String label = 'frameLabel';
      final String metadata = 'frameMetadata';

      final EkycBridgeFrame frame = EkycBridgeFrame(
        base64: base64,
        index: index,
        label: label,
        metadata: metadata,
      );

      expect(frame.base64, base64);
      expect(frame.index, index);
      expect(frame.label, label);
      expect(frame.metadata, metadata);
    });

    test('toJson should return a map with all properties', () {
      final String base64 = 'base64string';
      final int index = 1;
      final String label = 'frameLabel';
      final String metadata = 'frameMetadata';

      final EkycBridgeFrame frame = EkycBridgeFrame(
        base64: base64,
        index: index,
        label: label,
        metadata: metadata,
      );

      final Map<String, dynamic> json = frame.toJson();

      expect(json, isA<Map<String, dynamic>>());
      expect(json['base64'], base64);
      expect(json['index'], index);
      expect(json['label'], label);
      expect(json['metadata'], metadata);
    });

    test('toJson should handle null properties gracefully', () {
      final EkycBridgeFrame frame = EkycBridgeFrame(
        base64: null,
        index: null,
        label: null,
        metadata: null,
      );

      final Map<String, dynamic> json = frame.toJson();

      expect(json, isA<Map<String, dynamic>>());
      expect(json['base64'], isNull);
      expect(json['index'], isNull);
      expect(json['label'], isNull);
      expect(json['metadata'], isNull);
    });
  });
}
