import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_args.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FacialVerificationInitializeArgs', () {
    test('should correctly assign required sessionToken', () {
      // Arrange
      const String sessionToken = 'testSessionToken';

      // Act
      final FacialVerificationInitializeArgs args =
          FacialVerificationInitializeArgs(sessionToken: sessionToken);

      // Assert
      expect(args.sessionToken, equals(sessionToken));
      expect(args.languageCode, isNull); // languageCode should be null if not provided
    });

    test('should correctly assign optional languageCode', () {
      // Arrange
      const String sessionToken = 'testSessionToken';
      const String languageCode = 'en';

      // Act
      final FacialVerificationInitializeArgs args = FacialVerificationInitializeArgs(
        sessionToken: sessionToken,
        languageCode: languageCode,
      );

      // Assert
      expect(args.sessionToken, equals(sessionToken));
      expect(args.languageCode, equals(languageCode));
    });
  });
}
