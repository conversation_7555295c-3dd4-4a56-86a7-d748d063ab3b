import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_result.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FacialVerificationInitializeResult', () {
    test('FacialVerificationInitializeSuccessResult should have isSuccess set to true', () {
      // Act
      final FacialVerificationInitializeSuccessResult result =
          FacialVerificationInitializeSuccessResult();

      // Assert
      expect(result.isSuccess, isTrue);
    });

    test('FacialVerificationInitializeErrorResult should have isSuccess set to false', () {
      // Act
      final FacialVerificationInitializeErrorResult result =
          FacialVerificationInitializeErrorResult();

      // Assert
      expect(result.isSuccess, isFalse); // Corrected to false for error case
    });

    test('FacialVerificationInitializeErrorResult should correctly assign error properties', () {
      // Arrange
      final BaseEntity apiErrorResponse = BaseEntity();
      final EkycBridgeErrorReason bridgeErrorReason = EkycBridgeErrorReason.exceedLimit;

      // Act
      final FacialVerificationInitializeErrorResult result =
          FacialVerificationInitializeErrorResult(
        apiErrorResponse: apiErrorResponse,
        bridgeErrorReason: bridgeErrorReason,
      );

      // Assert
      expect(result.apiErrorResponse, equals(apiErrorResponse));
      expect(result.bridgeErrorReason, equals(bridgeErrorReason));
    });
  });
}
