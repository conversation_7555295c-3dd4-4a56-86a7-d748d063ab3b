import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_result.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FacialVerificationStartCapturingResult', () {
    test('FacialVerificationStartCapturingErrorNotInitialized should have isSuccess set to false',
        () {
      // Arrange
      final EkycBridgeLivenessMode livenessMode = EkycBridgeLivenessMode.flash_16;

      // Act
      final FacialVerificationStartCapturingErrorNotInitialized result =
          FacialVerificationStartCapturingErrorNotInitialized(
        livenessMode: livenessMode,
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.livenessMode, equals(livenessMode));
    });

    test('FacialVerificationStartCapturingSuccessResult should have isSuccess set to true', () {
      // Arrange
      final EkycBridgeLivenessMode livenessMode = EkycBridgeLivenessMode.flash_16;
      final List<String> videoIds = <String>['video1', 'video2'];
      final List<String> imageIds = <String>['image1', 'image2'];

      // Act
      final FacialVerificationStartCapturingSuccessResult result =
          FacialVerificationStartCapturingSuccessResult(
        livenessMode: livenessMode,
        videoIds: videoIds,
        imageIds: imageIds,
      );

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.livenessMode, equals(livenessMode));
      expect(result.videoIds, equals(videoIds));
      expect(result.imageIds, equals(imageIds));
    });

    test('FacialVerificationStartCapturingErrorResult should have isSuccess set to false', () {
      // Arrange
      final EkycBridgeLivenessMode livenessMode = EkycBridgeLivenessMode.flash_16;
      final BaseEntity apiErrorResponse = BaseEntity();
      final EkycBridgeErrorReason bridgeErrorReason = EkycBridgeErrorReason.exceedLimit;

      // Act
      final FacialVerificationStartCapturingErrorResult result =
          FacialVerificationStartCapturingErrorResult(
        livenessMode: livenessMode,
        apiErrorResponse: apiErrorResponse,
        bridgeErrorReason: bridgeErrorReason,
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.livenessMode, equals(livenessMode));
      expect(result.apiErrorResponse, equals(apiErrorResponse));
      expect(result.bridgeErrorReason, equals(bridgeErrorReason));
    });
  });
}
