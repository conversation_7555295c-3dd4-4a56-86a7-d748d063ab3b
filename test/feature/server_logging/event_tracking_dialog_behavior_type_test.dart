import 'package:flutter_common_package/feature/server_logging/event_tracking_dialog_behavior_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DialogBehaviorType:', () {
    test('type should return the proper string value', () {
      expect(EventTrackingDialogBehaviorType.show.eventId, '999');
      expect(EventTrackingDialogBehaviorType.dismissByOtherUserAction.eventId, '998');
      expect(EventTrackingDialogBehaviorType.clickClose.eventId, '997');
      expect(EventTrackingDialogBehaviorType.clickPositive.eventId, '996');
      expect(EventTrackingDialogBehaviorType.clickNegative.eventId, '995');
    });
  });
}
