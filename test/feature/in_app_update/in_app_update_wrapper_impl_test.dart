import 'dart:async';

import 'package:flutter_common_package/feature/in_app_update/in_app_update_platform.dart';
import 'package:flutter_common_package/feature/in_app_update/in_app_update_wrapper_impl.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:mocktail/mocktail.dart';

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockInAppUpdatePlatform extends Mock implements InAppUpdatePlatform {}

void main() {
  late DevicePlatform mockDevicePlatform;
  final InAppUpdatePlatform mockInAppUpdatePlatform = MockInAppUpdatePlatform();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(AppUpdateInfo);

    getIt.registerLazySingleton<DevicePlatform>(() => MockDevicePlatform());
    mockDevicePlatform = getIt.get<DevicePlatform>();
  });

  group('InAppUpdateWrapperImpl', () {
    late InAppUpdateWrapperImpl inAppUpdateWrapper;

    setUp(() {
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);
      inAppUpdateWrapper = InAppUpdateWrapperImpl(
        inAppUpdatePlatform: mockInAppUpdatePlatform,
      );
    });

    group('isUpdateAvailable', () {
      test('on non-Android platform, returns false', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(true);
        final bool result = await inAppUpdateWrapper.isUpdateAvailable();
        expect(result, false);
      });

      test('on Android platform, when checkForUpdate Available', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);

        when(() => mockInAppUpdatePlatform.checkForUpdate())
            .thenAnswer((_) => Future<AppUpdateInfo>.value(AppUpdateInfo(
                  updateAvailability: UpdateAvailability.updateAvailable,
                  immediateUpdateAllowed: false,
                  immediateAllowedPreconditions: <int>[],
                  flexibleUpdateAllowed: false,
                  flexibleAllowedPreconditions: <int>[],
                  availableVersionCode: null,
                  installStatus: InstallStatus.pending,
                  packageName: '',
                  clientVersionStalenessDays: null,
                  updatePriority: 1,
                )));

        final bool result = await inAppUpdateWrapper.isUpdateAvailable();
        expect(result, true);
      });

      test('on Android platform, when checkForUpdate return not Available', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);
        when(() => mockInAppUpdatePlatform.checkForUpdate()).thenAnswer((_) async => AppUpdateInfo(
              updateAvailability: UpdateAvailability.updateNotAvailable,
              immediateUpdateAllowed: false,
              immediateAllowedPreconditions: <int>[],
              flexibleUpdateAllowed: false,
              flexibleAllowedPreconditions: <int>[],
              availableVersionCode: null,
              installStatus: InstallStatus.pending,
              packageName: '',
              clientVersionStalenessDays: null,
              updatePriority: 1,
            ));

        final bool result = await inAppUpdateWrapper.isUpdateAvailable();
        expect(result, false);
      });

      test('on Android platform, checkForUpdate throws exception', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);
        when(() => mockInAppUpdatePlatform.checkForUpdate()).thenThrow(Exception('error'));

        final bool result = await inAppUpdateWrapper.isUpdateAvailable();
        expect(result, false);
      });
    });

    group('startImmediateUpdate', () {
      test('on non-Android platform, returns inAppUpdateFailed', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(true);

        final AppUpdateResult result = await inAppUpdateWrapper.startImmediateUpdate();
        expect(result, AppUpdateResult.inAppUpdateFailed);
      });

      test('on Android platform, returns correct app update status', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);
        when(() => mockInAppUpdatePlatform.performImmediateUpdate())
            .thenAnswer((_) async => AppUpdateResult.success);

        final AppUpdateResult result = await inAppUpdateWrapper.startImmediateUpdate();
        expect(result, AppUpdateResult.success);
      });

      test('on Android platform, returns false if update throw exception', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);
        when(() => mockInAppUpdatePlatform.performImmediateUpdate()).thenThrow(Exception('error'));

        final AppUpdateResult result = await inAppUpdateWrapper.startImmediateUpdate();
        expect(result, AppUpdateResult.inAppUpdateFailed);
      });
    });

    group('startFlexibleUpdate', () {
      test('on non-Android platform, returns inAppUpdateFailed', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(true);
        final AppUpdateResult result = await inAppUpdateWrapper.startFlexibleUpdate();
        expect(result, AppUpdateResult.inAppUpdateFailed);
      });

      test('on Android platform, returns correct app update status', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);
        when(() => mockInAppUpdatePlatform.startFlexibleUpdate())
            .thenAnswer((_) async => AppUpdateResult.success);

        final AppUpdateResult result = await inAppUpdateWrapper.startFlexibleUpdate();
        expect(result, AppUpdateResult.success);
      });

      test('on Android platform, returns false if update throw exception', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);
        when(() => mockInAppUpdatePlatform.startFlexibleUpdate()).thenThrow(Exception('error'));

        final AppUpdateResult result = await inAppUpdateWrapper.startFlexibleUpdate();
        expect(result, AppUpdateResult.inAppUpdateFailed);
      });
    });

    group('installStateStream', () {
      test('on non-Android platform, returns empty stream', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(true);
        final Stream<InstallStatus> stream = inAppUpdateWrapper.installStateStream();
        expect(stream, emitsDone);
      });

      test('on Android platform, returns a Stream<InstallStatus> ', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);

        when(() => mockInAppUpdatePlatform.installUpdateListener)
            .thenAnswer((_) => Stream<InstallStatus>.empty());

        final Stream<InstallStatus> stream = inAppUpdateWrapper.installStateStream();
        expect(stream, isA<Stream<InstallStatus>>());
      });
    });

    group('completeFlexibleUpdate', () {
      test('on non-Android platform, does nothing (no error)', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(true);
        await inAppUpdateWrapper.completeFlexibleUpdate();
      });

      test('on Android platform, completes without error', () async {
        when(() => mockDevicePlatform.isIOS()).thenReturn(false);
        when(() => mockInAppUpdatePlatform.completeFlexibleUpdate())
            .thenAnswer((_) => Future<void>.value());

        await inAppUpdateWrapper.completeFlexibleUpdate();
      });
    });
  });
}
