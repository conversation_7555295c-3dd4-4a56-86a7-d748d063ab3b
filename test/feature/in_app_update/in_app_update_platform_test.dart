import 'package:flutter/services.dart';
import 'package:flutter_common_package/feature/in_app_update/in_app_update_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:in_app_update/in_app_update.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late DefaultInAppUpdatePlatform inAppUpdatePlatform;

  setUp(() {
    inAppUpdatePlatform = DefaultInAppUpdatePlatform();

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('de.ffuf.in_app_update/methods'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'checkForUpdate':
            return <String, dynamic>{
              'updateAvailability': 2, // UpdateAvailability.updateAvailable
              'immediateAllowed': true,
              'immediateAllowedPreconditions': <int>[],
              'flexibleAllowed': true,
              'flexibleAllowedPreconditions': <int>[],
              'availableVersionCode': 100,
              'installStatus': 0, // InstallStatus.unknown
              'packageName': 'com.example.app',
              'clientVersionStalenessDays': 5,
              'updatePriority': 3,
            };
          case 'performImmediateUpdate':
          case 'startFlexibleUpdate':
          case 'completeFlexibleUpdate':
            return null; // Success case returns null
          default:
            return null;
        }
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('de.ffuf.in_app_update/methods'),
      null,
    );
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockStreamHandler(
      const EventChannel('de.ffuf.in_app_update/stateEvents'),
      null,
    );
  });

  group('DefaultInAppUpdatePlatform', () {
    test('checkForUpdate returns AppUpdateInfo', () async {
      final AppUpdateInfo result = await inAppUpdatePlatform.checkForUpdate();

      expect(result, isA<AppUpdateInfo>());
      expect(result.updateAvailability, UpdateAvailability.updateAvailable);
      expect(result.immediateUpdateAllowed, isTrue);
      expect(result.flexibleUpdateAllowed, isTrue);
      expect(result.availableVersionCode, 100);
      expect(result.packageName, 'com.example.app');
    });

    test('performImmediateUpdate returns success', () async {
      final AppUpdateResult result = await inAppUpdatePlatform.performImmediateUpdate();

      expect(result, AppUpdateResult.success);
    });

    test('startFlexibleUpdate returns success', () async {
      final AppUpdateResult result = await inAppUpdatePlatform.startFlexibleUpdate();

      expect(result, AppUpdateResult.success);
    });

    test('completeFlexibleUpdate completes successfully', () async {
      await expectLater(inAppUpdatePlatform.completeFlexibleUpdate(), completes);
    });

    test('handles platform exception in performImmediateUpdate', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('de.ffuf.in_app_update/methods'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'performImmediateUpdate') {
            throw PlatformException(code: 'USER_DENIED_UPDATE');
          }
          return null;
        },
      );

      final AppUpdateResult result = await inAppUpdatePlatform.performImmediateUpdate();
      expect(result, AppUpdateResult.userDeniedUpdate);
    });

    test('handles platform exception in startFlexibleUpdate', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('de.ffuf.in_app_update/methods'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'startFlexibleUpdate') {
            throw PlatformException(code: 'IN_APP_UPDATE_FAILED');
          }
          return null;
        },
      );

      final AppUpdateResult result = await inAppUpdatePlatform.startFlexibleUpdate();
      expect(result, AppUpdateResult.inAppUpdateFailed);
    });
  });
}
