import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/common_localization/common_localization_configs.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CommonLocalizationConfigs', () {
    final List<Locale> supportedLocales = <Locale>[
      const Locale('en', 'US'),
      const Locale('vi', 'VN'),
    ];
    const String fakePath = 'fake_path';
    const Locale fallbackLocale = Locale('en', 'US');
    const Locale startLocale = Locale('en', 'US');

    test('should initialize with provided parameters and default values', () {
      final CommonLocalizationConfigs commonLocalizationConfigs = CommonLocalizationConfigs(
        supportedLocales: supportedLocales,
        path: fakePath,
        fallbackLocale: fallbackLocale,
        startLocale: startLocale,
      );

      expect(commonLocalizationConfigs.supportedLocales, supportedLocales);
      expect(commonLocalizationConfigs.path, fakePath);
      expect(commonLocalizationConfigs.fallbackLocale, fallbackLocale);
      expect(commonLocalizationConfigs.startLocale, startLocale);
    });

    test(
        'should initialize with provided supportedLocales and fakePath, using default values for fallbackLocale and startLocale',
        () {
      final CommonLocalizationConfigs commonLocalizationConfigs = CommonLocalizationConfigs(
        supportedLocales: supportedLocales,
        path: fakePath,
      );

      expect(commonLocalizationConfigs.supportedLocales, supportedLocales);
      expect(commonLocalizationConfigs.path, fakePath);
      expect(commonLocalizationConfigs.fallbackLocale, const Locale('vi', 'VN'));
      expect(commonLocalizationConfigs.startLocale, const Locale('vi', 'VN'));
    });
  });
}
