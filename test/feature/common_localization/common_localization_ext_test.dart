import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('LocalizationBuildContextExt', () {
    testWidgets('Test input value', (WidgetTester tester) async {
      const String expectedCountryCode = 'VN';
      const String expectedLanguageCode = 'vi';
      final CommonLocalizationConfigs commonLocalizationConfigs = CommonLocalizationConfigs(
        supportedLocales: <Locale>[const Locale('vi', 'VN')],
        path: 'fake_path',
      );

      SharedPreferences.setMockInitialValues(<String, Object>{});
      await CommonLocalization.ensureInitialized();
      await tester.pumpWidget(CommonLocalization(
        configs: commonLocalizationConfigs,
        child: Builder(builder: (BuildContext context) {
          expect(context.localizationDelegates, isA<List<LocalizationsDelegate<dynamic>>>());
          expect(context.supportedLocales, commonLocalizationConfigs.supportedLocales);
          expect(
              context.locale,
              isA<Locale>()
                  .having(
                    (Locale p0) => p0.countryCode,
                    'verify countryCode',
                    expectedCountryCode,
                  )
                  .having(
                    (Locale p0) => p0.languageCode,
                    'verify languageCode',
                    expectedLanguageCode,
                  ));
          return const SizedBox.shrink();
        }),
      ));
    });
  });
}
