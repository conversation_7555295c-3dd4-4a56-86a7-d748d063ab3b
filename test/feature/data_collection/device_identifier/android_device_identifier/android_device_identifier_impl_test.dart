import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/android_device_identifier/android_device_identifier_impl.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  const String flutterAndroidIdChannel = 'android_id';
  const String getAndroidIDName = 'getId';
  const MethodChannel channel = MethodChannel(flutterAndroidIdChannel);

  late LoggingRepo mockLoggingRepo;
  late AndroidDeviceIdentifierImpl androidDeviceIdentifierImpl;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    androidDeviceIdentifierImpl = AndroidDeviceIdentifierImpl();
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      null,
    );
  });

  test('Test AndroidDeviceIdentifierImpl constant values', () {
    expect(
      AndroidDeviceIdentifierImpl.androidDeviceIdentifierErrorType,
      'android_device_identifier',
    );
  });

  group('Test function getAndroidId', () {
    test('getAndroidId should return the Android ID when it is retrieved successfully', () async {
      const String expectedId = 'android_id';
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == getAndroidIDName) {
            return expectedId;
          }
          return null;
        },
      );

      final String? result = await androidDeviceIdentifierImpl.getAndroidId();

      expect(result, expectedId);
    });

    test('getAndroidId should log an error event and return null when an exception is thrown',
        () async {
      when(() => mockLoggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) async => Future<void>.value());

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == getAndroidIDName) {
            throw PlatformException(code: 'error', message: 'error_message');
          }
          return null;
        },
      );

      final String? result = await androidDeviceIdentifierImpl.getAndroidId();

      expect(
        verify(() => mockLoggingRepo.logErrorEvent(
              errorType: captureAny(named: 'errorType'),
              args: captureAny(named: 'args'),
            )).captured,
        <dynamic>[
          'android_device_identifier',
          <String, dynamic>{
            'action': 'get_android_id',
            'description': 'error_message',
          }
        ],
      );
      expect(result, isNull);
    });

    test('getAndroidId should log an error event and return null when an exception is thrown',
        () async {
      when(() => mockLoggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) async => Future<void>.value());

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == getAndroidIDName) {
            throw Exception('error_message');
          }
          return null;
        },
      );

      final String? result = await androidDeviceIdentifierImpl.getAndroidId();

      expect(
        verify(() => mockLoggingRepo.logErrorEvent(
              errorType: captureAny(named: 'errorType'),
              args: captureAny(named: 'args'),
            )).captured,
        <dynamic>[
          'android_device_identifier',
          <String, dynamic>{
            'action': 'get_android_id',
            'description': 'Exception: error_message',
          }
        ],
      );
      expect(result, isNull);
    });
  });
}
