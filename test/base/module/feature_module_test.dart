// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

import 'mocks/mock_modules.dart';

// Concrete implementation of FeatureModule for testing
class TestFeatureModule extends FeatureModule {
  final String moduleName;
  final List<Type> moduleDependencies;
  final Future<void> Function(GetIt getIt)? registerCallback;

  TestFeatureModule({
    required this.moduleName,
    this.moduleDependencies = const <Type>[],
    this.registerCallback,
  });

  @override
  String get name => moduleName;

  @override
  List<Type> get dependencies => moduleDependencies;

  @override
  Future<void> register(GetIt getIt) async {
    if (registerCallback != null) {
      await registerCallback!(getIt);
    }
  }
}

class MockGetIt extends Mock implements GetIt {}

void main() {
  late GetIt getIt;
  late MockFeatureModule mockModule;
  late MockGetIt mockGetIt;

  setUpAll(() {
    registerFallbackValue(GetIt.instance);
  });

  setUp(() {
    getIt = GetIt.instance;
    getIt.reset();
    mockModule = MockFeatureModule();
    mockGetIt = MockGetIt();
  });

  group('FeatureModule with Mock', () {
    test('should implement name property correctly', () {
      // Arrange
      when(() => mockModule.name).thenReturn('test_module');

      // Assert
      expect(mockModule.name, 'test_module');
    });

    test('should implement dependencies property correctly', () {
      // Arrange
      when(() => mockModule.dependencies).thenReturn(<Type>[String, int, double]);

      // Assert
      expect(mockModule.dependencies, <Type>[String, int, double]);
    });

    test('should implement register method correctly', () async {
      // Arrange
      bool registerCalled = false;
      when(() => mockModule.register(any())).thenAnswer((_) async {
        registerCalled = true;
      });

      // Act
      await mockModule.register(getIt);

      // Assert
      expect(registerCalled, isTrue);
      verify(() => mockModule.register(getIt)).called(1);
    });

    test('should register dependencies correctly', () async {
      // Arrange
      when(() => mockModule.register(any())).thenAnswer((Invocation invocation) async {
        final GetIt getIt = invocation.positionalArguments[0] as GetIt;
        getIt.registerSingleton<String>('test string');
        getIt.registerSingleton<int>(42);
      });

      // Act
      await mockModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<String>(), isTrue);
      expect(getIt.isRegistered<int>(), isTrue);
      expect(getIt<String>(), 'test string');
      expect(getIt<int>(), 42);
      verify(() => mockModule.register(getIt)).called(1);
    });
  });

  group('FeatureModule with Concrete Implementation', () {
    test('should return correct name', () {
      // Arrange
      final TestFeatureModule module = TestFeatureModule(moduleName: 'testModule');

      // Act & Assert
      expect(module.name, equals('testModule'));
    });

    test('should return correct dependencies', () {
      // Arrange
      final TestFeatureModule module = TestFeatureModule(
        moduleName: 'testModule',
        moduleDependencies: <Type>[String, int, double],
      );

      // Act & Assert
      expect(module.dependencies, equals(<Type>[String, int, double]));
    });

    test('should return empty dependencies list when none are specified', () {
      // Arrange
      final TestFeatureModule module = TestFeatureModule(moduleName: 'testModule');

      // Act & Assert
      expect(module.dependencies, isEmpty);
    });

    test('should call register callback when register is called', () async {
      // Arrange
      bool callbackCalled = false;
      final TestFeatureModule module = TestFeatureModule(
        moduleName: 'testModule',
        registerCallback: (GetIt getIt) async {
          callbackCalled = true;
        },
      );

      // Act
      await module.register(mockGetIt);

      // Assert
      expect(callbackCalled, isTrue);
    });

    test('should not throw when register is called without a callback', () async {
      // Arrange
      final TestFeatureModule module = TestFeatureModule(moduleName: 'testModule');

      // Act & Assert
      expect(() async => await module.register(mockGetIt), returnsNormally);
    });

    test('should pass GetIt instance to register callback', () async {
      // Arrange
      late GetIt passedGetIt;
      final TestFeatureModule module = TestFeatureModule(
        moduleName: 'testModule',
        registerCallback: (GetIt getIt) async {
          passedGetIt = getIt;
        },
      );

      // Act
      await module.register(mockGetIt);

      // Assert
      expect(passedGetIt, equals(mockGetIt));
    });
  });
}
