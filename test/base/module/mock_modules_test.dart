// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/module/common_package_module_names.dart';
import 'package:flutter_common_package/base/module/module_registry.dart';
import 'package:flutter_common_package/base/module/module_registry_internal.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

import 'mocks/mock_modules.dart';

void main() {
  late GetIt getIt;
  late ModuleRegistry moduleRegistry;
  late MockAnalyticsModule mockAnalyticsModule;
  late MockNetworkModule mockNetworkModule;

  setUpAll(() {
    registerFallbackValue(GetIt.instance);
  });

  setUp(() {
    getIt = GetIt.instance;
    getIt.reset();
    moduleRegistry = ModuleRegistryInternal.getModuleRegistry(getIt);

    mockAnalyticsModule = MockAnalyticsModule();
    mockNetworkModule = MockNetworkModule();
  });

  group('MockAnalyticsModule', () {
    test('should have correct name and dependencies', () {
      // Arrange
      when(() => mockAnalyticsModule.name).thenReturn(CommonPackageModuleNames.analytics);
      when(() => mockAnalyticsModule.dependencies).thenReturn([
        LoggingRepo,
        EventTrackingUtils,
        FirebaseAnalyticsWrapper,
      ]);

      // Assert
      expect(mockAnalyticsModule.name, CommonPackageModuleNames.analytics);
      expect(mockAnalyticsModule.dependencies, contains(LoggingRepo));
      expect(mockAnalyticsModule.dependencies, contains(EventTrackingUtils));
      expect(mockAnalyticsModule.dependencies, contains(FirebaseAnalyticsWrapper));
    });

    test('should register mock dependencies when setupWithMockDependencies is called', () async {
      // Arrange
      mockAnalyticsModule.setupWithMockDependencies();

      // Act
      await mockAnalyticsModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<EventTrackingUtils>(), isTrue);
      expect(getIt.isRegistered<FirebaseAnalyticsWrapper>(), isTrue);

      // Verify the mocks are working
      final eventTrackingUtils = getIt.get<EventTrackingUtils>();
      await eventTrackingUtils.sendUserActionEvent(eventId: 'test_event', metaData: {'key': 'value'});

      final firebaseAnalytics = getIt.get<FirebaseAnalyticsWrapper>();
      await firebaseAnalytics.logEvent(name: 'test_event', parameters: {'key': 'value'});

      // Verify we can cast to the mock types
      final mockEventTrackingUtils = eventTrackingUtils as MockEventTrackingUtils;
      final mockFirebaseAnalytics = firebaseAnalytics as MockFirebaseAnalyticsWrapper;

      expect(mockEventTrackingUtils.events.length, 1);
      expect(mockEventTrackingUtils.events[0]['eventName'], 'test_event');
      expect(mockEventTrackingUtils.events[0]['parameters'], {'key': 'value'});

      expect(mockFirebaseAnalytics.events.length, 1);
      expect(mockFirebaseAnalytics.events[0]['name'], 'test_event');
      expect(mockFirebaseAnalytics.events[0]['parameters'], {'key': 'value'});
    });
  });

  group('MockNetworkModule', () {
    test('should have correct name and dependencies', () {
      // Arrange
      when(() => mockNetworkModule.name).thenReturn(CommonPackageModuleNames.network);
      when(() => mockNetworkModule.dependencies).thenReturn([
        NetworkManager,
      ]);

      // Assert
      expect(mockNetworkModule.name, CommonPackageModuleNames.network);
      expect(mockNetworkModule.dependencies, contains(NetworkManager));
    });

    test('should register mock dependencies when setupWithMockDependencies is called', () async {
      // Arrange
      mockNetworkModule.setupWithMockDependencies();

      // Act
      await mockNetworkModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<NetworkManager>(), isTrue);

      // Verify the mocks are working
      final networkManager = getIt.get<NetworkManager>();
      expect(networkManager, isA<MockNetworkManager>());

      // Verify we can use the mock
      expect(networkManager.hasInternet, isTrue);
      verify(() => networkManager.hasInternet).called(1);
    });
  });

  group('Module Registry with Mocks', () {
    test('should register modules correctly', () async {
      // Arrange
      when(() => mockAnalyticsModule.name).thenReturn(CommonPackageModuleNames.analytics);
      when(() => mockNetworkModule.name).thenReturn(CommonPackageModuleNames.network);
      mockAnalyticsModule.setupWithMockDependencies();
      mockNetworkModule.setupWithMockDependencies();

      final builder = ModuleRegistryInternal.createBuilder(getIt);
      builder.register(mockAnalyticsModule, source: 'test');
      builder.register(mockNetworkModule, source: 'test');
      moduleRegistry = builder.build();

      // Assert
      expect(moduleRegistry.registeredModules.contains(CommonPackageModuleNames.analytics), isTrue);
      expect(moduleRegistry.registeredModules.contains(CommonPackageModuleNames.network), isTrue);
    });

    test('should call register method when initializing modules', () async {
      // Arrange
      when(() => mockAnalyticsModule.name).thenReturn(CommonPackageModuleNames.analytics);
      mockAnalyticsModule.setupWithMockDependencies();

      final builder = ModuleRegistryInternal.createBuilder(getIt);
      builder.register(mockAnalyticsModule, source: 'test');
      moduleRegistry = builder.build();

      // Act
      await moduleRegistry.initializeSpecificModules([CommonPackageModuleNames.analytics]);

      // Assert
      verify(() => mockAnalyticsModule.register(any())).called(1);
    });
  });
}
