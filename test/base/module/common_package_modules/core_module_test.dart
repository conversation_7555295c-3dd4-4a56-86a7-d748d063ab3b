// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:async';

import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/base/module/event_bus.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/util/local_storage_helper.dart';
import 'package:flutter_common_package/util/secure_storage_helper.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/shared_preferences_impl.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator_impl.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

// Mock classes
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}
class MockUuid extends Mock implements Uuid {}
class MockPackageInfo extends Mock implements PackageInfo {}
class MockGetIt extends Mock implements GetIt {}
class MockEventBus extends Mock implements EventBus {}
class MockCommonLocalStorageHelper extends Mock implements CommonLocalStorageHelper {}
class MockCommonSharedPreferencesHelper extends Mock implements CommonSharedPreferencesHelper {}
class MockUUIDGenerator extends Mock implements UUIDGenerator {}

// Mock the platform channel for PackageInfo
void mockPackageInfoPlatformChannel() {
  const MethodChannel channel = MethodChannel('dev.fluttercommunity.plus/package_info');
  TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
    channel,
    (MethodCall methodCall) async {
      if (methodCall.method == 'getAll') {
        return <String, String>{
          'appName': 'Test App',
          'packageName': 'com.test.app',
          'version': '1.0.0',
          'buildNumber': '1',
          'buildSignature': '',
        };
      }
      return null;
    },
  );
}

void main() {
  // Initialize the binding
  TestWidgetsFlutterBinding.ensureInitialized();

  group('CoreModule with direct GetIt instance', () {
    late GetIt getIt;
    late CoreModule coreModule;
    late MockPackageInfo mockPackageInfo;
    late MockFlutterSecureStorage mockFlutterSecureStorage;
    late MockUuid mockUuid;

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      coreModule = CoreModule();

      // Set up SharedPreferences mock
      SharedPreferences.setMockInitialValues(<String, Object>{});

      // Mock the platform channel for PackageInfo
      mockPackageInfoPlatformChannel();

      // Create mocks
      mockPackageInfo = MockPackageInfo();
      mockFlutterSecureStorage = MockFlutterSecureStorage();
      mockUuid = MockUuid();

      // Configure mocks
      when(() => mockUuid.v4()).thenReturn('mock-uuid-v4');
      when(() => mockPackageInfo.appName).thenReturn('Test App');
      when(() => mockPackageInfo.packageName).thenReturn('com.test.app');
      when(() => mockPackageInfo.version).thenReturn('1.0.0');
      when(() => mockPackageInfo.buildNumber).thenReturn('1');
      when(() => mockPackageInfo.buildSignature).thenReturn('');

      // Pre-register mocks
      getIt.registerSingleton<PackageInfo>(mockPackageInfo);
      getIt.registerSingleton<FlutterSecureStorage>(mockFlutterSecureStorage);
      getIt.registerSingleton<Uuid>(mockUuid);
    });

    test('should have correct name', () {
      // Assert
      expect(coreModule.name, equals(CommonPackageModuleNames.core));
    });

    test('should have correct dependencies', () {
      // Assert
      expect(coreModule.dependencies, contains(PackageInfo));
      expect(coreModule.dependencies, contains(FlutterSecureStorage));
      expect(coreModule.dependencies, contains(CommonLocalStorageHelper));
      expect(coreModule.dependencies, contains(CommonSharedPreferencesHelper));
      expect(coreModule.dependencies, contains(UUIDGenerator));
      expect(coreModule.dependencies, contains(EventBus));
      expect(coreModule.dependencies.length, equals(6));
    });

    test('should register all dependencies', () async {
      // Act
      await coreModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<PackageInfo>(), isTrue);
      expect(getIt.isRegistered<FlutterSecureStorage>(), isTrue);
      expect(getIt.isRegistered<CommonLocalStorageHelper>(), isTrue);
      expect(getIt.isRegistered<CommonSharedPreferencesHelper>(), isTrue);
      expect(getIt.isRegistered<UUIDGenerator>(), isTrue);
      expect(getIt.isRegistered<EventBus>(), isTrue);

      // Verify the implementations
      expect(getIt.get<CommonLocalStorageHelper>(), isA<CommonSecureStorageHelperImpl>());
      expect(getIt.get<CommonSharedPreferencesHelper>(), isA<CommonSharedPreferencesHelperImpl>());
      expect(getIt.get<UUIDGenerator>(), isA<UUIDGeneratorImpl>());
      expect(getIt.get<EventBus>(), isA<EventBus>());
    });

    test('should register CommonLocalStorageHelper with FlutterSecureStorage', () async {
      // Act
      await coreModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<CommonLocalStorageHelper>(), isTrue);
      final CommonLocalStorageHelper storageHelper = getIt.get<CommonLocalStorageHelper>();
      expect(storageHelper, isA<CommonSecureStorageHelperImpl>());
    });

    test('should register CommonSharedPreferencesHelper', () async {
      // Act
      await coreModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<CommonSharedPreferencesHelper>(), isTrue);
      final CommonSharedPreferencesHelper prefsHelper = getIt.get<CommonSharedPreferencesHelper>();
      expect(prefsHelper, isA<CommonSharedPreferencesHelperImpl>());
    });

    test('should register UUIDGenerator with Uuid', () async {
      // Act
      await coreModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<UUIDGenerator>(), isTrue);
      final UUIDGenerator uuidGenerator = getIt.get<UUIDGenerator>();
      expect(uuidGenerator, isA<UUIDGeneratorImpl>());
    });

    test('should register EventBus as singleton', () async {
      // Act
      await coreModule.register(getIt);

      // Assert
      expect(getIt.isRegistered<EventBus>(), isTrue);
      final EventBus eventBus1 = getIt.get<EventBus>();
      final EventBus eventBus2 = getIt.get<EventBus>();
      expect(identical(eventBus1, eventBus2), isTrue);
    });
  });

  group('CoreModule with mocked GetIt', () {
    late CoreModule coreModule;
    late MockGetIt mockGetIt;

    setUp(() {
      coreModule = CoreModule();
      mockGetIt = MockGetIt();

      // Configure the mock GetIt
      when(() => mockGetIt.isRegistered<PackageInfo>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<FlutterSecureStorage>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<CommonLocalStorageHelper>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<CommonSharedPreferencesHelper>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<UUIDGenerator>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<EventBus>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<Uuid>()).thenReturn(false);

      when(() => mockGetIt.registerLazySingletonAsync<PackageInfo>(any())).thenReturn(null);
      when(() => mockGetIt.getAsync<PackageInfo>()).thenAnswer((_) async => MockPackageInfo());
      when(() => mockGetIt.registerLazySingleton<FlutterSecureStorage>(any())).thenReturn(null);
      when(() => mockGetIt.registerLazySingleton<CommonLocalStorageHelper>(any())).thenReturn(null);
      when(() => mockGetIt.registerLazySingleton<CommonSharedPreferencesHelper>(any())).thenReturn(null);
      when(() => mockGetIt.registerLazySingleton<UUIDGenerator>(any())).thenReturn(null);
      when(() => mockGetIt.registerLazySingleton<EventBus>(any())).thenReturn(null);
      when(() => mockGetIt.get<FlutterSecureStorage>()).thenReturn(MockFlutterSecureStorage());
    });

    test('should register all dependencies with mocked GetIt', () async {
      // Act
      await coreModule.register(mockGetIt);

      // Assert
      verify(() => mockGetIt.registerLazySingletonAsync<PackageInfo>(any())).called(1);
      verify(() => mockGetIt.getAsync<PackageInfo>()).called(1);
      verify(() => mockGetIt.registerLazySingleton<FlutterSecureStorage>(any())).called(1);
      verify(() => mockGetIt.registerLazySingleton<CommonLocalStorageHelper>(any())).called(1);
      verify(() => mockGetIt.registerLazySingleton<CommonSharedPreferencesHelper>(any())).called(1);
      verify(() => mockGetIt.registerLazySingleton<UUIDGenerator>(any())).called(1);
      verify(() => mockGetIt.registerLazySingleton<EventBus>(any())).called(1);
    });

    test('should register CommonLocalStorageHelper with correct implementation', () async {
      // Arrange
      final List<Function> capturedFactories = <Function>[];
      when(() => mockGetIt.registerLazySingleton<CommonLocalStorageHelper>(captureAny()))
          .thenAnswer((Invocation invocation) {
        capturedFactories.add(invocation.positionalArguments[0] as Function);
        return;
      });

      // Act
      await coreModule.register(mockGetIt);

      // Assert
      expect(capturedFactories.length, equals(1));

      // Call the factory function to verify it creates the correct implementation
      final Function factory = capturedFactories[0];
      final result = factory();
      expect(result, isA<CommonSecureStorageHelperImpl>());
    });

    test('should register CommonSharedPreferencesHelper with correct implementation', () async {
      // Arrange
      final List<Function> capturedFactories = <Function>[];
      when(() => mockGetIt.registerLazySingleton<CommonSharedPreferencesHelper>(captureAny()))
          .thenAnswer((Invocation invocation) {
        capturedFactories.add(invocation.positionalArguments[0] as Function);
        return;
      });

      // Act
      await coreModule.register(mockGetIt);

      // Assert
      expect(capturedFactories.length, equals(1));

      // Call the factory function to verify it creates the correct implementation
      final Function factory = capturedFactories[0];
      final result = factory();
      expect(result, isA<CommonSharedPreferencesHelperImpl>());
    });

    test('should not register dependencies when they are already registered', () async {
      // Arrange - configure the mock GetIt to return true for all isRegistered calls
      when(() => mockGetIt.isRegistered<PackageInfo>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<FlutterSecureStorage>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<CommonLocalStorageHelper>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<CommonSharedPreferencesHelper>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<UUIDGenerator>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<EventBus>()).thenReturn(true);

      // Act
      await coreModule.register(mockGetIt);

      // Assert - verify that no registration methods were called
      verifyNever(() => mockGetIt.registerLazySingletonAsync<PackageInfo>(any()));
      verifyNever(() => mockGetIt.registerLazySingleton<FlutterSecureStorage>(any()));
      verifyNever(() => mockGetIt.registerLazySingleton<CommonLocalStorageHelper>(any()));
      verifyNever(() => mockGetIt.registerLazySingleton<CommonSharedPreferencesHelper>(any()));
      verifyNever(() => mockGetIt.registerLazySingleton<UUIDGenerator>(any()));
      verifyNever(() => mockGetIt.registerLazySingleton<EventBus>(any()));
    });
  });

  group('CoreModule with PackageInfo async registration', () {
    late GetIt getIt;
    late CoreModule coreModule;

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      coreModule = CoreModule(); // Use the real CoreModule here to test async registration

      // Mock the platform channel for PackageInfo
      mockPackageInfoPlatformChannel();
    });

    test('should register PackageInfo asynchronously', () async {
      // Arrange
      // Make sure PackageInfo is not registered
      if (getIt.isRegistered<PackageInfo>()) {
        getIt.unregister<PackageInfo>();
      }

      // Act
      final Completer<void> completer = Completer<void>();

      // Use a timeout to avoid hanging if the test fails
      Timer(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          completer.completeError('Timeout waiting for PackageInfo registration');
        }
      });

      // Start the registration process
      unawaited(coreModule.register(getIt).then((_) {
        if (!completer.isCompleted) {
          completer.complete();
        }
      }));

      // Wait for the registration to complete or timeout
      await completer.future;

      // Assert
      expect(getIt.isRegistered<PackageInfo>(), isTrue);
    });
  });
}
