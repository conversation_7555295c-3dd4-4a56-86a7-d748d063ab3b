// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/module/common_package_modules/notification_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/onesignal/listener_handler.dart';
import 'package:flutter_common_package/feature/remote_config/remote_config_settings.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/util/clear_all_notifications_wrapper.dart';
import 'package:flutter_common_package/util/extensible_enum.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

class MockGetIt extends Mock implements GetIt {}
class MockLoggingRepo extends Mock implements LoggingRepo {}
class MockOneSignalListenerHandler extends Mock implements OneSignalListenerHandler {}
class MockCommonFlavorValues extends Mock implements CommonFlavorValues {}
class MockFlavorConfig extends Mock implements FlavorConfig {}
class MockOneSignal extends Mock implements OneSignal {}
class MockOSPermissionStateChanges extends Mock implements OSPermissionStateChanges {}
class MockOSPermissionState extends Mock implements OSPermissionState {}
class MockOSSubscriptionStateChanges extends Mock implements OSSubscriptionStateChanges {}
class MockOSSubscriptionState extends Mock implements OSSubscriptionState {}

class FakeOSPermissionStateChanges extends Fake implements OSPermissionStateChanges {}
class FakeOSSubscriptionStateChanges extends Fake implements OSSubscriptionStateChanges {}

// We can't directly mock enums, so we'll avoid using 'any()' for them
class FakeEventType extends Fake implements EventType {}

void main() {
  late NotificationModule notificationModule;
  late MockGetIt mockGetIt;
  late MockLoggingRepo mockLoggingRepo;
  late MockOneSignalListenerHandler mockOneSignalListenerHandler;
  late MockCommonFlavorValues mockFlavorValues;
  late MockOneSignal mockOneSignal;

  setUpAll(() {
    // Register fallback values
    registerFallbackValue(EventType.userAction);
    registerFallbackValue(FakeOSPermissionStateChanges());
    registerFallbackValue(FakeOSSubscriptionStateChanges());
  });

  setUp(() {
    notificationModule = NotificationModule();
    mockGetIt = MockGetIt();
    mockLoggingRepo = MockLoggingRepo();
    mockOneSignalListenerHandler = MockOneSignalListenerHandler();
    mockFlavorValues = MockCommonFlavorValues();
    mockOneSignal = MockOneSignal();

    // Initialize FlavorConfig with mock values
    // Use a unique flavor name for each test to avoid conflicts
    FlavorConfig(flavor: 'notification_test_${DateTime.now().millisecondsSinceEpoch}', values: mockFlavorValues);

    // Mock the flavor values
    when(() => mockFlavorValues.oneSignalAppId).thenReturn(null);

    // Setup GetIt mocks
    when(() => mockGetIt.get<LoggingRepo>()).thenReturn(mockLoggingRepo);
    when(() => mockGetIt.registerLazySingleton<ClearAllNotificationsWrapper>(any())).thenAnswer((_) {});
    when(() => mockGetIt.isRegistered<OneSignalListenerHandler>()).thenReturn(true);
    when(() => mockGetIt.get<OneSignalListenerHandler>()).thenReturn(mockOneSignalListenerHandler);

    // Setup OneSignal mock - avoid using any() for enum parameters
    OneSignal.shared = mockOneSignal;
    when(() => mockOneSignal.setLogLevel(OSLogLevel.verbose, OSLogLevel.none)).thenAnswer((_) async {});
    when(() => mockOneSignal.setLocationShared(false)).thenAnswer((_) async {});
    when(() => mockOneSignal.setAppId(captureAny())).thenAnswer((_) async {});
    when(() => mockOneSignal.setLaunchURLsInApp(false)).thenAnswer((_) async {});
    when(() => mockOneSignal.disablePush(false)).thenAnswer((_) async {});
    when(() => mockOneSignal.setPermissionObserver(captureAny())).thenAnswer((_) {});
    when(() => mockOneSignal.setSubscriptionObserver(captureAny())).thenAnswer((_) {});

    // Setup LoggingRepo mock
    when(() => mockLoggingRepo.logEvent(eventType: captureAny(named: 'eventType'), data: captureAny(named: 'data'))).thenAnswer((_) async {});
  });

  tearDown(() {
    reset(mockGetIt);
    reset(mockLoggingRepo);
    reset(mockOneSignalListenerHandler);
    reset(mockFlavorValues);
    reset(mockOneSignal);
  });

  tearDownAll(() {
    // No need to clean up FlavorConfig as we're using unique flavor names
  });

  group('NotificationModule', () {
    test('should have the correct name', () {
      expect(notificationModule.name, equals(CommonPackageModuleNames.notification));
    });

    test('should have the correct dependencies', () {
      expect(notificationModule.dependencies, contains(ClearAllNotificationsWrapper));
      expect(notificationModule.dependencies.length, equals(1));
    });

    test('should register ClearAllNotificationsWrapper', () async {
      // Execute
      await notificationModule.register(mockGetIt);

      // Verify
      verify(() => mockGetIt.registerLazySingleton<ClearAllNotificationsWrapper>(any())).called(1);
    });

    test('should not initialize OneSignal when app ID is null', () async {
      // Setup
      when(() => mockFlavorValues.oneSignalAppId).thenReturn(null);

      // Execute
      await notificationModule.register(mockGetIt);

      // Verify
      verifyNever(() => mockOneSignal.setAppId(any()));
    });

    test('should initialize OneSignal when app ID is provided', () async {
      // Setup
      when(() => mockFlavorValues.oneSignalAppId).thenReturn('test-onesignal-app-id');

      // Execute
      await notificationModule.register(mockGetIt);

      // Verify
      verify(() => mockOneSignal.setLogLevel(OSLogLevel.verbose, OSLogLevel.none)).called(1);
      verify(() => mockOneSignal.setLocationShared(false)).called(1);
      verify(() => mockOneSignal.setAppId('test-onesignal-app-id')).called(1);
      verify(() => mockOneSignal.setLaunchURLsInApp(false)).called(1);
      verify(() => mockOneSignal.disablePush(false)).called(1);
      verify(() => mockOneSignal.setPermissionObserver(any())).called(1);
      verify(() => mockOneSignal.setSubscriptionObserver(any())).called(1);
    });

    test('should set up permission observer correctly', () async {
      // Setup
      when(() => mockFlavorValues.oneSignalAppId).thenReturn('test-onesignal-app-id');
      when(() => mockGetIt.isRegistered<OneSignalListenerHandler>()).thenReturn(true);

      // Capture the permission observer callback
      Function? permissionObserverCallback;
      when(() => mockOneSignal.setPermissionObserver(captureAny()))
          .thenAnswer((invocation) {
        permissionObserverCallback = invocation.positionalArguments[0] as Function;
      });

      // Execute
      await notificationModule.register(mockGetIt);

      // Verify callback was captured
      expect(permissionObserverCallback, isNotNull);

      // Create mock permission state changes
      final mockPermissionChanges = MockOSPermissionStateChanges();
      final mockPermissionStateTo = MockOSPermissionState();

      // Setup mock permission state
      when(() => mockPermissionChanges.to).thenReturn(mockPermissionStateTo);
      when(() => mockPermissionChanges.jsonRepresentation()).thenReturn('{}');
      when(() => mockPermissionStateTo.status).thenReturn(OSNotificationPermission.denied);

      // Call the callback
      permissionObserverCallback!(mockPermissionChanges);

      // Verify logging repo was called when permission is denied
      verify(() => mockLoggingRepo.logEvent(eventType: EventType.deniedNotification)).called(1);

      // Verify OneSignalListenerHandler was called
      verify(() => mockOneSignalListenerHandler.onPermissionObserver(mockPermissionChanges)).called(1);
    });

    test('should not call OneSignalListenerHandler when it is not registered for permission observer', () async {
      // Setup
      when(() => mockFlavorValues.oneSignalAppId).thenReturn('test-onesignal-app-id');
      when(() => mockGetIt.isRegistered<OneSignalListenerHandler>()).thenReturn(false);

      // Capture the permission observer callback
      Function? permissionObserverCallback;
      when(() => mockOneSignal.setPermissionObserver(captureAny()))
          .thenAnswer((invocation) {
        permissionObserverCallback = invocation.positionalArguments[0] as Function;
      });

      // Execute
      await notificationModule.register(mockGetIt);

      // Verify callback was captured
      expect(permissionObserverCallback, isNotNull);

      // Create mock permission state changes
      final mockPermissionChanges = MockOSPermissionStateChanges();
      final mockPermissionStateTo = MockOSPermissionState();

      // Setup mock permission state
      when(() => mockPermissionChanges.to).thenReturn(mockPermissionStateTo);
      when(() => mockPermissionChanges.jsonRepresentation()).thenReturn('{}');
      when(() => mockPermissionStateTo.status).thenReturn(OSNotificationPermission.denied);

      // Call the callback
      permissionObserverCallback!(mockPermissionChanges);

      // Verify logging repo was called when permission is denied
      verify(() => mockLoggingRepo.logEvent(eventType: EventType.deniedNotification)).called(1);

      // Verify OneSignalListenerHandler was NOT called
      verifyNever(() => mockOneSignalListenerHandler.onPermissionObserver(any()));
    });

    test('should set up subscription observer correctly', () async {
      // Setup
      when(() => mockFlavorValues.oneSignalAppId).thenReturn('test-onesignal-app-id');
      when(() => mockGetIt.isRegistered<OneSignalListenerHandler>()).thenReturn(true);

      // Capture the subscription observer callback
      Function? subscriptionObserverCallback;
      when(() => mockOneSignal.setSubscriptionObserver(captureAny()))
          .thenAnswer((invocation) {
        subscriptionObserverCallback = invocation.positionalArguments[0] as Function;
      });

      // Execute
      await notificationModule.register(mockGetIt);

      // Verify callback was captured
      expect(subscriptionObserverCallback, isNotNull);

      // Create mock subscription state changes
      final mockSubscriptionChanges = MockOSSubscriptionStateChanges();
      final mockSubscriptionStateTo = MockOSSubscriptionState();

      // Setup mock subscription state
      when(() => mockSubscriptionChanges.to).thenReturn(mockSubscriptionStateTo);
      when(() => mockSubscriptionChanges.jsonRepresentation()).thenReturn('{}');
      when(() => mockSubscriptionStateTo.pushToken).thenReturn('test-push-token');

      // Call the callback
      await subscriptionObserverCallback!(mockSubscriptionChanges);

      // Verify logging repo was called with token
      verify(() => mockLoggingRepo.logEvent(
            eventType: EventType.notificationToken,
            data: <String, dynamic>{'token': 'test-push-token'},
          )).called(1);

      // Verify OneSignalListenerHandler was called
      verify(() => mockOneSignalListenerHandler.onSubscriptionObserver(mockSubscriptionChanges)).called(1);
    });

    test('should not call OneSignalListenerHandler when it is not registered for subscription observer', () async {
      // Setup
      when(() => mockFlavorValues.oneSignalAppId).thenReturn('test-onesignal-app-id');
      when(() => mockGetIt.isRegistered<OneSignalListenerHandler>()).thenReturn(false);

      // Capture the subscription observer callback
      Function? subscriptionObserverCallback;
      when(() => mockOneSignal.setSubscriptionObserver(captureAny()))
          .thenAnswer((invocation) {
        subscriptionObserverCallback = invocation.positionalArguments[0] as Function;
      });

      // Execute
      await notificationModule.register(mockGetIt);

      // Verify callback was captured
      expect(subscriptionObserverCallback, isNotNull);

      // Create mock subscription state changes
      final mockSubscriptionChanges = MockOSSubscriptionStateChanges();
      final mockSubscriptionStateTo = MockOSSubscriptionState();

      // Setup mock subscription state
      when(() => mockSubscriptionChanges.to).thenReturn(mockSubscriptionStateTo);
      when(() => mockSubscriptionChanges.jsonRepresentation()).thenReturn('{}');
      when(() => mockSubscriptionStateTo.pushToken).thenReturn('test-push-token');

      // Call the callback
      await subscriptionObserverCallback!(mockSubscriptionChanges);

      // Verify logging repo was called with token
      verify(() => mockLoggingRepo.logEvent(
            eventType: EventType.notificationToken,
            data: <String, dynamic>{'token': 'test-push-token'},
          )).called(1);

      // Verify OneSignalListenerHandler was NOT called
      verifyNever(() => mockOneSignalListenerHandler.onSubscriptionObserver(any()));
    });
  });
}
