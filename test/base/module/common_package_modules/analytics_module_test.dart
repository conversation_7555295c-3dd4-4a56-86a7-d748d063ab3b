// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:datadog_flutter_plugin/src/logs/ddlogs.dart';
import 'package:flutter_common_package/feature/server_logging/datadog_sdk_wrapper.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/module/common_package_modules/analytics_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/device_identifier.dart';
import 'package:flutter_common_package/feature/server_logging/analytics_service_impl.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockGetIt extends Mock implements GetIt {}

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

class MockDeviceInfoPluginWrapper extends Mock
    implements DeviceInfoPluginWrapper {}

class MockUUIDGenerator extends Mock implements UUIDGenerator {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockDeviceIdentifier extends Mock implements DeviceIdentifier {}

class MockFlavorConfig extends Mock implements FlavorConfig {}

class MockCommonDataDogConfig extends Mock implements CommonDataDogConfig {
  @override
  DatadogConfiguration? get datadogConfiguration => MockDatadogConfiguration();

  @override
  Map<String, String> get logCustomTags => {'env': 'test', 'app': 'test-app'};
}

class MockCommonFlavorValues extends Mock implements CommonFlavorValues {}

class MockDatadogConfiguration extends Mock implements DatadogConfiguration {}

class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {}

class MockFirebaseAnalyticsObserver extends Mock
    implements FirebaseAnalyticsObserver {}

class MockFirebaseCrashlytics extends Mock implements FirebaseCrashlytics {}

class MockFirebaseAnalyticsWrapper extends Mock
    implements FirebaseAnalyticsWrapper {}

class MockDatadogSdk extends Mock implements DatadogSdk {}

class MockDatadogLogging extends Mock implements DatadogLogging {}

class MockDatadogLogger extends Mock implements DatadogLogger {}

// Fake implementation of DatadogLoggerConfiguration for testing
class FakeDatadogLoggerConfiguration extends Fake
    implements DatadogLoggerConfiguration {}

// Fake implementation of DatadogConfiguration for testing
class FakeDatadogConfiguration extends Fake implements DatadogConfiguration {}

// Fake implementation of DatadogSdk for testing
class FakeDatadogSdk extends Fake implements DatadogSdk {}

// Fake implementation of FirebaseAnalytics for testing
class FakeFirebaseAnalytics extends Fake implements FirebaseAnalytics {}

// Fake implementation of FirebaseCrashlytics for testing
class FakeFirebaseCrashlytics extends Fake implements FirebaseCrashlytics {}

// Fake implementation of FlutterErrorDetails for testing
class FakeFlutterErrorDetails extends Fake implements FlutterErrorDetails {
  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Fake FlutterErrorDetails';
  }
}

/// Main test function for AnalyticsModule
void main() {
  // Declare all mock objects needed for testing
  late AnalyticsModule analyticsModule;
  late MockGetIt mockGetIt;
  late MockCommonHttpClient mockCommonHttpClient;
  late MockDeviceInfoPluginWrapper mockDeviceInfoPluginWrapper;
  late MockUUIDGenerator mockUUIDGenerator;
  late MockDeviceIdentifier mockDeviceIdentifier;
  late MockCommonFlavorValues mockFlavorValues;
  late MockFirebaseAnalytics mockFirebaseAnalytics;
  late MockFirebaseCrashlytics mockFirebaseCrashlytics;
  late MockFirebaseAnalyticsWrapper mockFirebaseAnalyticsWrapper;
  late MockDatadogSdk mockDatadogSdk;
  late MockDatadogLogging mockDatadogLogs;
  late MockDatadogLogger mockDatadogLogger;

  /// Register all fallback values for Mocktail
  setUpAll(() {
    // Register fallback values for all types used in mocks
    registerFallbackValue(EventType.userAction);
    registerFallbackValue(FakeDatadogLoggerConfiguration());
    registerFallbackValue(FakeDatadogConfiguration());
    registerFallbackValue(FakeDatadogSdk());
    registerFallbackValue(TrackingConsent.granted);
    registerFallbackValue(FakeFirebaseAnalytics());
    registerFallbackValue(FakeFirebaseCrashlytics());
    registerFallbackValue(FakeFlutterErrorDetails());
  });

  /// Set up test environment before each test
  setUp(() {
    // Initialize all mock objects
    analyticsModule = AnalyticsModule();
    mockGetIt = MockGetIt();
    mockCommonHttpClient = MockCommonHttpClient();
    mockDeviceInfoPluginWrapper = MockDeviceInfoPluginWrapper();
    mockUUIDGenerator = MockUUIDGenerator();
    mockDeviceIdentifier = MockDeviceIdentifier();
    mockFlavorValues = MockCommonFlavorValues();
    mockFirebaseAnalytics = MockFirebaseAnalytics();
    mockFirebaseCrashlytics = MockFirebaseCrashlytics();
    mockFirebaseAnalyticsWrapper = MockFirebaseAnalyticsWrapper();
    mockDatadogSdk = MockDatadogSdk();
    mockDatadogLogs = MockDatadogLogging();
    mockDatadogLogger = MockDatadogLogger();

    // Reset the DatadogSdkWrapper to avoid affecting other tests
    try {
      DatadogSdkWrapper.resetToOriginalInstance();
    } catch (e) {
      // This is expected in the test environment
    }

    // Initialize FlavorConfig with mock values
    FlavorConfig(flavor: 'test', values: mockFlavorValues);

    // Mock the flavor values
    when(() => mockFlavorValues.oneSignalAppId).thenReturn(null);
    when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
    when(() => mockFlavorValues.commonDataDogConfig).thenReturn(null);
    when(() => mockFlavorValues.baseUrl).thenReturn('https://test.example.com');

    // Setup GetIt mocks for common dependencies
    when(() => mockGetIt.get<CommonHttpClient>())
        .thenReturn(mockCommonHttpClient);
    when(() => mockGetIt.get<DeviceInfoPluginWrapper>())
        .thenReturn(mockDeviceInfoPluginWrapper);
    when(() => mockGetIt.get<UUIDGenerator>()).thenReturn(mockUUIDGenerator);

    // Setup GetIt registration mocks
    when(() => mockGetIt.registerLazySingleton<LoggingRepo>(any()))
        .thenAnswer((_) {});
    when(() => mockGetIt.registerLazySingleton<EventTrackingUtils>(any()))
        .thenAnswer((_) {});
    when(() => mockGetIt.registerLazySingleton<CommonNavigatorObserver>(any()))
        .thenAnswer((_) {});

    // Setup isRegistered mocks for core dependencies
    when(() => mockGetIt.isRegistered<LoggingRepo>()).thenReturn(false);
    when(() => mockGetIt.isRegistered<EventTrackingUtils>()).thenReturn(false);
    when(() => mockGetIt.isRegistered<CommonNavigatorObserver>()).thenReturn(false);

    // Setup DeviceIdentifier mocks
    when(() => mockGetIt.isRegistered<DeviceIdentifier>()).thenReturn(true);
    when(() => mockGetIt.get<DeviceIdentifier>())
        .thenReturn(mockDeviceIdentifier);
    when(() => mockDeviceIdentifier.getDeviceId())
        .thenAnswer((_) async => 'test-device-id');

    // Setup Firebase Analytics mocks
    when(() => mockGetIt.isRegistered<FirebaseAnalytics>()).thenReturn(false);
    when(() => mockGetIt.get<FirebaseAnalytics>())
        .thenReturn(mockFirebaseAnalytics);
    when(() => mockGetIt.registerSingleton<FirebaseAnalytics>(any()))
        .thenReturn(mockFirebaseAnalytics);
    when(() => mockGetIt.registerLazySingleton<FirebaseAnalyticsWrapper>(any()))
        .thenAnswer((_) {});

    // Setup FirebaseCrashlytics mocks
    when(() => mockGetIt.isRegistered<FirebaseCrashlytics>()).thenReturn(false);
    when(() => mockGetIt.get<FirebaseCrashlytics>())
        .thenReturn(mockFirebaseCrashlytics);
    when(() => mockGetIt.registerSingleton<FirebaseCrashlytics>(any()))
        .thenReturn(mockFirebaseCrashlytics);

    // Setup Crashlytics methods
    when(() => mockFirebaseCrashlytics.recordFlutterError(any()))
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockFirebaseCrashlytics.recordError(
            any(),
            any(),
            fatal: any(named: 'fatal')
        )).thenAnswer((_) async => Future<void>.value());
    when(() => mockFirebaseCrashlytics.setCustomKey(any(), any()))
        .thenAnswer((_) async => Future<void>.value());

    // Setup DatadogSdk mock behavior
    when(() => mockDatadogSdk.logs).thenReturn(mockDatadogLogs);
    when(() => mockDatadogLogs.createLogger(any()))
        .thenReturn(mockDatadogLogger);
    when(() => mockDatadogSdk.initialize(any(), any()))
        .thenAnswer((_) async {});
    when(() => mockDatadogSdk.setUserInfo(extraInfo: any(named: 'extraInfo')))
        .thenAnswer((_) {});

    // Set the mock DatadogSdk in the wrapper for testing
    DatadogSdkWrapper.instanceForTesting = mockDatadogSdk;

    // Setup DatadogSdk registration mocks
    when(() => mockGetIt.registerSingleton<DatadogSdk>(any()))
        .thenReturn(mockDatadogSdk);
    when(() => mockGetIt.isRegistered<DatadogSdk>()).thenReturn(false);
    when(() => mockGetIt.get<DatadogSdk>()).thenReturn(mockDatadogSdk);

    // Setup DatadogLogger registration mocks
    when(() => mockGetIt.registerLazySingleton<DatadogLogger>(any()))
        .thenAnswer((_) {});

    // Setup AnalyticsServiceImpl registration mocks
    when(() => mockGetIt.registerLazySingleton<AnalyticsServiceImpl>(any()))
        .thenAnswer((_) {});
    when(() => mockGetIt.isRegistered<AnalyticsServiceImpl>())
        .thenReturn(false);

    // Setup FirebaseAnalyticsWrapper registration mocks
    when(() => mockGetIt.isRegistered<FirebaseAnalyticsWrapper>())
        .thenReturn(false);

    // Register fallback values for DatadogConfiguration
    registerFallbackValue(DatadogConfiguration(
      clientToken: 'test-token',
      env: 'test',
      site: DatadogSite.us1,
    ));

    // Register fallback values for DatadogLoggerConfiguration
    registerFallbackValue(DatadogLoggerConfiguration());

    // Setup additional mocks for DatadogLogger
    when(() => mockDatadogLogger.debug(any())).thenAnswer((_) {});
    when(() => mockDatadogLogger.info(any())).thenAnswer((_) {});
    when(() => mockDatadogLogger.warn(any())).thenAnswer((_) {});
    when(() => mockDatadogLogger.error(any())).thenAnswer((_) {});
    when(() => mockDatadogLogger.addTag(any(), any())).thenAnswer((_) {});
  });

  tearDown(() {
    // Reset the DatadogSdkWrapper to avoid affecting other tests
    try {
      DatadogSdkWrapper.resetToOriginalInstance();
    } catch (e) {
      // This is expected in the test environment
    }
  });

  /// Tests for the AnalyticsModule class
  group('AnalyticsModule', () {
    /// Tests for basic module properties
    group('Basic properties', () {
      test('should have the correct name', () {
        // Verify the module name matches the expected value
        expect(analyticsModule.name, equals(CommonPackageModuleNames.analytics));
      });

      test('should have the correct dependencies', () {
        // Verify all expected dependencies are present
        expect(analyticsModule.dependencies, contains(LoggingRepo));
        expect(analyticsModule.dependencies, contains(EventTrackingUtils));
        expect(analyticsModule.dependencies, contains(FirebaseAnalyticsWrapper));
        expect(analyticsModule.dependencies, contains(CommonNavigatorObserver));
        expect(analyticsModule.dependencies, contains(DatadogLogger));
        expect(analyticsModule.dependencies, contains(AnalyticsServiceImpl));

        // Verify the total number of dependencies
        expect(analyticsModule.dependencies.length, equals(6));
      });
    });

    /// Tests for main register method
    group('Main register method', () {
      test('should call appropriate methods based on configuration', () async {
        // Arrange: Configure flavor with both Firebase and Datadog enabled
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify core dependencies are registered
        verify(() => mockGetIt.registerLazySingleton<LoggingRepo>(any())).called(1);
        verify(() => mockGetIt.registerLazySingleton<EventTrackingUtils>(any())).called(1);
        verify(() => mockGetIt.registerLazySingleton<CommonNavigatorObserver>(any())).called(1);

        // Verify Firebase and Datadog dependencies are registered
        verify(() => mockGetIt.registerLazySingleton<FirebaseAnalyticsWrapper>(any())).called(1);
        verify(() => mockDatadogSdk.initialize(any(), any())).called(1);
      });
    });

    /// Tests for core dependency registration
    group('Core dependency registration', () {
      test('should register core dependencies when no analytics services are enabled', () async {
        // Arrange: Configure flavor to disable Firebase and Datadog
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        when(() => mockFlavorValues.commonDataDogConfig).thenReturn(null);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify core dependencies are registered
        verify(() => mockGetIt.registerLazySingleton<LoggingRepo>(any())).called(1);
        verify(() => mockGetIt.registerLazySingleton<EventTrackingUtils>(any())).called(1);
        verify(() => mockGetIt.registerLazySingleton<CommonNavigatorObserver>(any())).called(1);
      });

      test('should create LoggingRepoImpl with CommonHttpClient', () async {
        // Arrange: Configure flavor and capture the factory function
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        when(() => mockFlavorValues.commonDataDogConfig?.datadogConfiguration).thenReturn(null);

        LoggingRepo? capturedRepo;
        when(() => mockGetIt.registerLazySingleton<LoggingRepo>(any()))
            .thenAnswer((invocation) {
          final factoryFunc = invocation.positionalArguments[0] as Function();
          capturedRepo = factoryFunc();
        });

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify the correct implementation is created
        expect(capturedRepo, isA<LoggingRepoImpl>());
      });
    });

    /// Tests for Firebase initialization
    group('Firebase initialization', () {
      test('should initialize Firebase when enabled in flavor config', () async {
        // Arrange: Configure flavor to enable Firebase
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig?.datadogConfiguration).thenReturn(null);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify Firebase wrapper is registered
        verify(() => mockGetIt.registerLazySingleton<FirebaseAnalyticsWrapper>(any()))
            .called(1);
      });

      test('should handle exceptions when initializing Firebase Analytics', () async {
        // Arrange: Configure flavor and make Firebase initialization throw an exception
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig?.datadogConfiguration).thenReturn(null);
        when(() => mockGetIt.isRegistered<FirebaseAnalytics>()).thenReturn(false);
        when(() => mockGetIt.registerSingleton<FirebaseAnalytics>(any()))
            .thenThrow(Exception('Firebase initialization error'));

        // Act: Register the module - should not throw exception
        await analyticsModule.register(mockGetIt);

        // Assert: Verify that we attempted to register Firebase
        verify(() => mockGetIt.isRegistered<FirebaseAnalytics>()).called(1);
      });

      test('should handle exceptions when initializing FirebaseCrashlytics', () async {
        // Arrange: Configure flavor and make Crashlytics initialization throw an exception
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig?.datadogConfiguration).thenReturn(null);
        when(() => mockGetIt.isRegistered<FirebaseCrashlytics>()).thenReturn(false);
        when(() => mockGetIt.registerSingleton<FirebaseCrashlytics>(any()))
            .thenThrow(Exception('Crashlytics initialization error'));

        // Act: Register the module - should not throw exception
        await analyticsModule.register(mockGetIt);

        // Assert: Verify that we attempted to register Crashlytics
        verify(() => mockGetIt.isRegistered<FirebaseCrashlytics>()).called(greaterThan(0));
      });

      test('should set device ID in Crashlytics when available', () async {
        // Arrange: Configure flavor to enable Firebase
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig?.datadogConfiguration).thenReturn(null);
        when(() => mockGetIt.isRegistered<FirebaseCrashlytics>()).thenReturn(true);
        when(() => mockGetIt.get<FirebaseCrashlytics>()).thenReturn(mockFirebaseCrashlytics);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify device ID is set in Crashlytics
        verify(() => mockFirebaseCrashlytics.setCustomKey('deviceId', 'test-device-id')).called(1);
      });

      test('should handle exceptions when setting device ID in Crashlytics', () async {
        // Arrange: Configure flavor to enable Firebase
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig?.datadogConfiguration).thenReturn(null);
        when(() => mockGetIt.isRegistered<FirebaseCrashlytics>()).thenReturn(true);
        when(() => mockGetIt.get<FirebaseCrashlytics>()).thenReturn(mockFirebaseCrashlytics);
        when(() => mockDeviceIdentifier.getDeviceId())
            .thenThrow(Exception('Failed to get device ID'));

        // Act: Register the module - should not throw exception
        await analyticsModule.register(mockGetIt);

        // Assert: Verify we attempted to get the device ID but didn't crash
        verify(() => mockGetIt.isRegistered<DeviceIdentifier>()).called(greaterThan(0));
      });
    });

    /// Tests for Datadog initialization
    group('Datadog initialization', () {
      test('should initialize Datadog when configuration is provided', () async {
        // Arrange: Configure flavor with Datadog configuration
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify Datadog is properly initialized
        verify(() => mockGetIt.registerSingleton<DatadogSdk>(any())).called(1);
        verify(() => mockDatadogSdk.initialize(
            any(), TrackingConsent.granted)).called(1);
        verify(() => mockDatadogSdk
            .setUserInfo(extraInfo: {'deviceId': 'test-device-id'})).called(1);
        verify(() => mockDatadogLogs.createLogger(any())).called(1);
        verify(() => mockGetIt.registerLazySingleton<DatadogLogger>(any()))
            .called(1);
        verify(() => mockGetIt.registerLazySingleton<AnalyticsServiceImpl>(any()))
            .called(1);
      });

      test('should handle gracefully when DatadogLogs is null', () async {
        // Arrange: Configure flavor with Datadog and make logs return null
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);
        when(() => mockDatadogSdk.logs).thenReturn(null);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify Datadog is initialized but logger is not registered
        verify(() => mockDatadogSdk.initialize(
            any(), TrackingConsent.granted)).called(1);
        verify(() => mockDatadogSdk
            .setUserInfo(extraInfo: {'deviceId': 'test-device-id'})).called(1);

        // Verify that DatadogLogger was not registered since logs is null
        verifyNever(() => mockGetIt.registerLazySingleton<DatadogLogger>(any()));
      });

      test('should handle exceptions when initializing Datadog', () async {
        // Arrange: Configure flavor with Datadog and make initialization throw an exception
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);
        when(() => mockDatadogSdk.initialize(any(), any()))
            .thenThrow(Exception('Datadog initialization error'));

        // Act: Register the module - should not throw exception
        await analyticsModule.register(mockGetIt);

        // Assert: Verify we attempted to initialize Datadog but didn't crash
        verify(() => mockDatadogSdk.initialize(any(), any())).called(1);
      });

      test('should handle exceptions when setting device ID in Datadog', () async {
        // Arrange: Configure flavor with Datadog
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);
        when(() => mockDeviceIdentifier.getDeviceId())
            .thenThrow(Exception('Failed to get device ID'));

        // Act: Register the module - should not throw exception
        await analyticsModule.register(mockGetIt);

        // Assert: Verify we attempted to get the device ID but didn't crash
        verify(() => mockGetIt.isRegistered<DeviceIdentifier>()).called(greaterThan(0));
      });

      test('should add custom tags to Datadog logger when available', () async {
        // Arrange: Configure flavor with Datadog and custom tags
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);

        // Custom tags are already set in the MockCommonDataDogConfig class

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify custom tags are added to the logger
        verify(() => mockDatadogLogger.addTag('env', 'test')).called(1);
        verify(() => mockDatadogLogger.addTag('app', 'test-app')).called(1);
      });
    });

    /// Tests for multiple analytics services
    group('Multiple analytics services', () {
      test('should initialize both Firebase and Datadog when both are enabled', () async {
        // Arrange: Configure flavor to enable both Firebase and Datadog
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify both services are initialized
        // Verify Firebase initialization
        verify(() => mockGetIt.registerLazySingleton<FirebaseAnalyticsWrapper>(any()))
            .called(1);

        // Verify Datadog initialization
        verify(() => mockDatadogSdk.initialize(any(), any())).called(1);
        verify(() => mockDatadogSdk.setUserInfo(extraInfo: any(named: 'extraInfo')))
            .called(1);
        verify(() => mockDatadogLogs.createLogger(any())).called(1);
        verify(() => mockGetIt.registerLazySingleton<DatadogLogger>(any()))
            .called(1);
      });

      test('should not register AnalyticsServiceImpl twice when both services are enabled', () async {
        // Arrange: Configure flavor and make AnalyticsServiceImpl already registered
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);
        when(() => mockGetIt.isRegistered<AnalyticsServiceImpl>())
            .thenReturn(true);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify AnalyticsServiceImpl is not registered again
        verifyNever(() => mockGetIt.registerLazySingleton<AnalyticsServiceImpl>(any()));
      });

      test('should use FirebaseAnalyticsWrapper when available', () async {
        // Arrange: Configure flavor and make FirebaseAnalyticsWrapper available
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);
        when(() => mockGetIt.isRegistered<FirebaseAnalyticsWrapper>())
            .thenReturn(true);
        when(() => mockGetIt.get<FirebaseAnalyticsWrapper>())
            .thenReturn(mockFirebaseAnalyticsWrapper);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify the module completes without errors
        // This test is primarily to ensure the code doesn't crash when FirebaseAnalyticsWrapper is available
        // We don't need to verify specific calls, just that the test completes successfully
        expect(true, isTrue);
      });
    });

    /// Tests for error handling and edge cases
    group('Error handling and edge cases', () {
      test('should handle PlatformDispatcher.onError callback setup', () async {
        // Arrange: Configure flavor to enable Firebase
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig).thenReturn(null);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify that Crashlytics was registered
        // This test is a bit tricky since we can't directly test the callback
        // But we can verify that the code path is executed
        verify(() => mockGetIt.isRegistered<FirebaseCrashlytics>()).called(greaterThan(0));
      });

      test('should not re-register FirebaseAnalytics when already registered', () async {
        // Arrange: Configure flavor and make FirebaseAnalytics already registered
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig).thenReturn(null);
        when(() => mockGetIt.isRegistered<FirebaseAnalytics>()).thenReturn(true);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify we check if it's registered but don't register it again
        verify(() => mockGetIt.isRegistered<FirebaseAnalytics>()).called(1);
        verifyNever(() => mockGetIt.registerSingleton<FirebaseAnalytics>(any()));
      });

      test('should handle missing DeviceIdentifier gracefully', () async {
        // Arrange: Configure flavor and make DeviceIdentifier not registered
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(true);
        when(() => mockFlavorValues.commonDataDogConfig).thenReturn(null);
        when(() => mockGetIt.isRegistered<DeviceIdentifier>()).thenReturn(false);
        when(() => mockGetIt.isRegistered<FirebaseCrashlytics>()).thenReturn(true);
        when(() => mockGetIt.get<FirebaseCrashlytics>()).thenReturn(mockFirebaseCrashlytics);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify we check if it's registered but don't try to get it
        verify(() => mockGetIt.isRegistered<DeviceIdentifier>()).called(greaterThan(0));
        verifyNever(() => mockGetIt.get<DeviceIdentifier>());
      });

      test('should handle missing FirebaseAnalyticsWrapper gracefully', () async {
        // Arrange: Configure flavor and make FirebaseAnalyticsWrapper not registered
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);
        when(() => mockGetIt.isRegistered<FirebaseAnalyticsWrapper>())
            .thenReturn(false);
        when(() => mockGetIt.isRegistered<AnalyticsServiceImpl>()).thenReturn(false);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify the module completes without errors
        // This test is primarily to ensure the code doesn't crash when FirebaseAnalyticsWrapper is not registered
        // We don't need to verify specific calls, just that the test completes successfully
        expect(true, isTrue);
      });

      test('should not register DatadogSdk twice', () async {
        // Arrange: Configure flavor with Datadog and make DatadogSdk already registered
        when(() => mockFlavorValues.initializeFirebaseSdk).thenReturn(false);
        final mockCommonDataDogConfig = MockCommonDataDogConfig();
        when(() => mockFlavorValues.commonDataDogConfig)
            .thenReturn(mockCommonDataDogConfig);
        when(() => mockGetIt.isRegistered<DatadogSdk>()).thenReturn(true);

        // Act: Register the module
        await analyticsModule.register(mockGetIt);

        // Assert: Verify we check if it's registered but don't register it again
        verify(() => mockGetIt.isRegistered<DatadogSdk>()).called(greaterThan(0));
        verifyNever(() => mockGetIt.registerSingleton<DatadogSdk>(any()));
      });
    });
  });
}
