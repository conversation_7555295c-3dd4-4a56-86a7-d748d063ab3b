// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/module/common_package_modules/network_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_factory.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_request_option_mapper.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/data_collection/firebase_performance/fp_network_request_interceptor.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:package_info_plus/package_info_plus.dart';

class MockDeviceInfoPluginWrapper extends Mock
    implements DeviceInfoPluginWrapper {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockPackageInfo extends Mock implements PackageInfo {}

class MockConnectivity extends Mock implements Connectivity {}

class MockNetworkManager extends Mock implements NetworkManager {}

class MockDioRequestOptionMapper extends Mock
    implements DioRequestOptionMapper {}

class MockDataCollector extends Mock implements DataCollector {}

class MockDioFirebasePerformanceInterceptor extends Mock
    implements DioFirebasePerformanceInterceptor {}

class FakeGetIt extends Fake implements GetIt {}

class MockGetIt extends Mock implements GetIt {}

class MockDioFactory extends Mock implements DioFactory {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    registerFallbackValue(FakeGetIt());
    registerFallbackValue(const Duration(seconds: 10));
  });

  late GetIt getIt;
  late MockDioFactory mockDioFactory;
  late MockDeviceInfoPluginWrapper mockDeviceInfoPluginWrapper;
  late MockDevicePlatform mockDevicePlatform;
  late MockPackageInfo mockPackageInfo;
  late MockConnectivity mockConnectivity;
  late MockNetworkManager mockNetworkManager;
  late MockDataCollector mockDataCollector;
  late MockDioFirebasePerformanceInterceptor mockDioFirebasePerformanceInterceptor;
  late MockDioRequestOptionMapper mockDioRequestOptionMapper;
  late NetworkModule networkModule;
  late Dio testDio;

  // Stream controller for connectivity changes
  final StreamController<List<ConnectivityResult>> connectivityStreamController =
      StreamController<List<ConnectivityResult>>.broadcast();

  setUp(() {
    getIt = GetIt.instance;
    getIt.reset();
    registerFallbackValue(const Duration(seconds: 10));

    // Initialize flavor config
    FlavorConfig(
      flavor: 'test',
      values: CommonFlavorValues(
        baseUrl: 'https://test.example.com',
        initializeFirebaseSdk: false,
        oneSignalAppId: null,
      ),
    );

    // Set up mocks
    mockDioFactory = MockDioFactory();
    mockDeviceInfoPluginWrapper = MockDeviceInfoPluginWrapper();
    mockDevicePlatform = MockDevicePlatform();
    mockPackageInfo = MockPackageInfo();
    mockConnectivity = MockConnectivity();
    mockNetworkManager = MockNetworkManager();
    mockDataCollector = MockDataCollector();
    mockDioFirebasePerformanceInterceptor =
        MockDioFirebasePerformanceInterceptor();
    mockDioRequestOptionMapper = MockDioRequestOptionMapper();

    // Create a pre-configured Dio instance for testing
    testDio = Dio();
    testDio.options.baseUrl = 'https://test.example.com';
    testDio.options.headers = {
      HeaderKey.language: 'en',
      HeaderKey.platform: 'test',
      HeaderKey.appVersion: '1.0.0',
      HeaderKey.appBuildNumber: '100',
      HeaderKey.deviceModel: 'Test Device',
      HeaderKey.osVersion: '1.0.0',
      HeaderKey.osBuildNumber: '123',
      HeaderKey.deviceId: 'test-device-id',
      HeaderKey.timeZoneOffset: '+07:00',
      HeaderKey.timeZone: 'Asia/Bangkok',
    };
    testDio.options.connectTimeout = const Duration(seconds: 10);
    testDio.options.receiveTimeout = const Duration(seconds: 10);
    testDio.options.sendTimeout = const Duration(seconds: 10);

    // Configure the mock factory to return our test Dio
    when(() => mockDioFactory.createDio(
          any(),
          locale: any(named: 'locale'),
          connectTimeout: any(named: 'connectTimeout'),
          receiveTimeout: any(named: 'receiveTimeout'),
          sendTimeout: any(named: 'sendTimeout'),
          baseUrl: any(named: 'baseUrl'),
          interceptors: any(named: 'interceptors'),
        )).thenAnswer((_) async => testDio);

    // Create NetworkModule instance with mock factory
    networkModule = NetworkModule(
      locale: const Locale('en'),
      dioFactory: mockDioFactory,
    );

    // Configure mocks
    when(() => mockDeviceInfoPluginWrapper.getDeviceModel())
        .thenReturn('Test Device');
    when(() => mockDeviceInfoPluginWrapper.getPlatformName())
        .thenReturn('test-platform');
    when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenReturn('1.0.0');
    when(() => mockDeviceInfoPluginWrapper.getAndroidBuildNumber())
        .thenReturn('123');

    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);

    // Set up PackageInfo mock
    when(() => mockPackageInfo.version).thenReturn('1.0.0');
    when(() => mockPackageInfo.buildNumber).thenReturn('100');

    when(() => mockNetworkManager.initialise()).thenAnswer((_) async {});

    // Set up DataCollector mock
    when(() => mockDataCollector.getDeviceId())
        .thenAnswer((_) async => 'test-device-id');

    // Set up FlavorConfig
    FlavorConfig(
      flavor: 'test',
      values: CommonFlavorValues(
        baseUrl: 'https://test.example.com',
        initializeFirebaseSdk: false,
        // Set to false to avoid Firebase initialization
        oneSignalAppId: null,
      ),
    );

    // Register dependencies
    getIt.registerSingleton<DeviceInfoPluginWrapper>(
        mockDeviceInfoPluginWrapper);
    getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    getIt.registerSingleton<PackageInfo>(mockPackageInfo);
    getIt.registerSingleton<Connectivity>(mockConnectivity);
    getIt.registerSingleton<NetworkManager>(mockNetworkManager);

    // Set up connectivity stream and methods
    when(() => mockConnectivity.onConnectivityChanged)
        .thenAnswer((_) => connectivityStreamController.stream);
    when(() => mockConnectivity.checkConnectivity())
        .thenAnswer((_) async => [ConnectivityResult.wifi]);

    // Create module
    networkModule = NetworkModule(locale: const Locale('en'));
  });

  tearDown(() {
    getIt.reset();
  });

  group('NetworkModule', () {
    test('should have correct name and dependencies', () {
      // Assert name
      expect(networkModule.name, 'common_network');
      expect(networkModule.name, equals(CommonPackageModuleNames.network));

      // Assert dependencies
      expect(networkModule.dependencies, contains(Connectivity));
      expect(networkModule.dependencies, contains(NetworkManager));
      expect(networkModule.dependencies, contains(Dio));
      expect(networkModule.dependencies, contains(CommonHttpClient));
    });

    test('should register all dependencies', () async {
      // Act - Only verify registration, not initialization
      // This avoids platform-specific calls
      final Dio dio = Dio();
      dio.options.baseUrl = 'https://test.example.com';
      dio.options.headers = <String, String>{
        'language': 'en',
        'platform': 'test',
        'app_version': '1.0.0',
        'app_build_number': '100',
        'device_model': 'Test Device',
        'os_version': '1.0.0',
        'os_build_number': '123',
      };
      getIt.registerSingleton<Dio>(dio);
      getIt.registerSingleton<CommonHttpClient>(DioClientImpl(getIt<Dio>()));

      // Assert
      expect(getIt.isRegistered<Dio>(), isTrue);
      expect(getIt.isRegistered<CommonHttpClient>(), isTrue);

      // Verify Dio configuration
      final Dio dioInstance = getIt<Dio>();
      expect(dioInstance.options.baseUrl, 'https://test.example.com');
      expect(dioInstance.options.headers['language'], 'en');
      expect(dioInstance.options.headers['platform'], 'test');
      expect(dioInstance.options.headers['app_version'], '1.0.0');
      expect(dioInstance.options.headers['app_build_number'], '100');
      expect(dioInstance.options.headers['device_model'], 'Test Device');
      expect(dioInstance.options.headers['os_version'], '1.0.0');
      expect(dioInstance.options.headers['os_build_number'], '123');
    });

    test('should not register dependencies that are already registered',
        () async {
      // Arrange
      getIt.reset();

      // Mock the isRegistered method to return true for all dependencies
      final mockGetIt = MockGetIt();
      when(() => mockGetIt.isRegistered<Connectivity>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<NetworkManager>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<Dio>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<CommonHttpClient>()).thenReturn(true);

      // Act
      await networkModule.register(mockGetIt);

      // Assert - verify that nothing was registered
      verifyNever(() => mockGetIt.registerLazySingleton<Connectivity>(any()));
      verifyNever(() => mockGetIt.registerLazySingleton<NetworkManager>(any()));
      verifyNever(() => mockGetIt.registerLazySingleton<Dio>(any()));
      verifyNever(
          () => mockGetIt.registerLazySingleton<CommonHttpClient>(any()));
    });

    test('should handle Connectivity initialization failure', () async {
      // Arrange
      getIt.reset();

      // Create a custom GetIt that throws when registering Connectivity
      final mockGetIt = MockGetIt();
      when(() => mockGetIt.isRegistered<Connectivity>()).thenReturn(false);
      when(() => mockGetIt.registerLazySingleton<Connectivity>(any()))
          .thenThrow(Exception('Failed to initialize Connectivity'));

      // Create module
      final module = NetworkModule(dioFactory: mockDioFactory);

      // Act & Assert
      try {
        await module.register(mockGetIt);
        fail('Expected an exception to be thrown');
      } catch (e) {
        expect(e, isA<Exception>());
        expect(e.toString(), contains('Failed to initialize Connectivity'));
      }
    });

    test('should handle NetworkManager initialization failure', () async {
      // Create a custom NetworkModule that throws during NetworkManager initialization
      final customNetworkModule = NetworkModule(dioFactory: mockDioFactory);

      // Create a custom GetIt that throws when registering NetworkManager
      final mockGetIt = MockGetIt();
      when(() => mockGetIt.isRegistered<Connectivity>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<NetworkManager>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<Dio>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<CommonHttpClient>()).thenReturn(true);

      // Make registerLazySingleton throw an exception when registering NetworkManager
      when(() => mockGetIt.registerLazySingleton<NetworkManager>(any()))
          .thenThrow(Exception('Failed to initialize NetworkManager'));

      // Act & Assert
      try {
        await customNetworkModule.register(mockGetIt);
        fail('Expected an exception to be thrown');
      } catch (e) {
        expect(e, isA<Exception>());
        expect(e.toString(), contains('Failed to initialize NetworkManager'));
      }
    });

    test('should propagate specific errors from NetworkManager initialization', () async {
      // Create a custom NetworkModule
      final customNetworkModule = NetworkModule(dioFactory: mockDioFactory);

      // Create a custom GetIt that throws a specific error when registering NetworkManager
      final mockGetIt = MockGetIt();
      when(() => mockGetIt.isRegistered<Connectivity>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<NetworkManager>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<Dio>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<CommonHttpClient>()).thenReturn(true);

      // Make registerLazySingleton throw a specific error
      final socketException = SocketException('Failed to connect to network');
      when(() => mockGetIt.registerLazySingleton<NetworkManager>(any()))
          .thenThrow(socketException);

      // Act & Assert
      try {
        await customNetworkModule.register(mockGetIt);
        fail('Expected a SocketException to be thrown');
      } catch (e) {
        expect(e, isA<SocketException>());
        expect((e as SocketException).message, 'Failed to connect to network');
      }
    });

    test('should create Dio with correct configuration', () async {
      // Arrange - reset GetIt to ensure clean state
      getIt.reset();

      // Use the mock DioFactory instead of real dependencies
      final module =
          NetworkModule(dioFactory: mockDioFactory, locale: const Locale('en'));
      await module.register(getIt);

      // Assert
      expect(getIt.isRegistered<Dio>(), isTrue);

      final dio = getIt<Dio>();
      expect(dio.options.headers[HeaderKey.appVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.appBuildNumber], '100');
      expect(dio.options.headers[HeaderKey.deviceModel], 'Test Device');
      expect(dio.options.headers[HeaderKey.platform], 'test');
      expect(dio.options.headers[HeaderKey.osVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.language], 'en');
      expect(dio.options.baseUrl, 'https://test.example.com');
      expect(dio.options.connectTimeout, const Duration(seconds: 10));
      expect(dio.options.receiveTimeout, const Duration(seconds: 10));
      expect(dio.options.sendTimeout, const Duration(seconds: 10));
    });

    test('should set Android-specific headers when platform is Android',
        () async {
      // Arrange
      getIt.reset();
      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);

      // Use the mock DioFactory instead of real dependencies
      final module = NetworkModule(dioFactory: mockDioFactory);
      await module.register(getIt);

      // Assert
      final dio = getIt<Dio>();
      expect(dio.options.headers[HeaderKey.osBuildNumber], '123');
    });

    test('should not set Android-specific headers when platform is iOS',
        () async {
      // Arrange
      getIt.reset();
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
      when(() => mockDevicePlatform.isIOS()).thenReturn(true);

      // Configure mock to return a Dio without Android-specific headers
      final dioWithoutAndroidHeaders = Dio();
      dioWithoutAndroidHeaders.options.headers = {
        HeaderKey.appVersion: '1.0.0',
        HeaderKey.appBuildNumber: '100',
        HeaderKey.deviceModel: 'Test Device',
        HeaderKey.platform: 'test-platform',
        HeaderKey.osVersion: '1.0.0',
        // No osBuildNumber header
      };

      when(() => mockDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: any(named: 'interceptors'),
          )).thenAnswer((_) async => dioWithoutAndroidHeaders);

      // Use the mock DioFactory
      final module = NetworkModule(dioFactory: mockDioFactory);
      await module.register(getIt);

      // Assert
      final dio = getIt<Dio>();
      expect(dio.options.headers[HeaderKey.osBuildNumber], isNull);
    });

    test('should set device ID when DataCollector is registered', () async {
      // Arrange
      getIt.reset();
      getIt.registerSingleton<DataCollector>(mockDataCollector);

      // Use the mock DioFactory instead of real dependencies
      final module = NetworkModule(dioFactory: mockDioFactory);
      await module.register(getIt);

      // Assert
      final dio = getIt<Dio>();
      expect(dio.options.headers[HeaderKey.deviceId], 'test-device-id');
    });

    test('should not set device ID when DataCollector is not registered',
        () async {
      // Arrange
      getIt.reset();

      // Configure mock to return a Dio without deviceId header
      final dioWithoutDeviceId = Dio();
      dioWithoutDeviceId.options.headers = {
        HeaderKey.appVersion: '1.0.0',
        HeaderKey.appBuildNumber: '100',
        HeaderKey.deviceModel: 'Test Device',
        HeaderKey.platform: 'test',
        HeaderKey.osVersion: '1.0.0',
        // No deviceId header
      };

      when(() => mockDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: any(named: 'interceptors'),
          )).thenAnswer((_) async => dioWithoutDeviceId);

      // Use the mock DioFactory
      final module = NetworkModule(dioFactory: mockDioFactory);
      await module.register(getIt);

      // Assert
      final dio = getIt<Dio>();
      expect(dio.options.headers[HeaderKey.deviceId], isNull);
    });

    test('should create NetworkModule without locale', () async {
      // Arrange
      getIt.reset();

      // Configure mock to return Dio without language header
      final dioWithoutLocale = Dio();
      dioWithoutLocale.options.headers = {};

      when(() => mockDioFactory.createDio(
            any(),
            locale: null,
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: any(named: 'interceptors'),
          )).thenAnswer((_) async => dioWithoutLocale);

      // Act - create module without locale
      final module = NetworkModule(dioFactory: mockDioFactory);
      await module.register(getIt);

      // Assert
      verify(() => mockDioFactory.createDio(
            getIt,
            locale: null,
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: any(named: 'interceptors'),
          )).called(1);
    });

    test('should handle custom interceptors', () async {
      // Arrange
      getIt.reset();
      final mockInterceptor = MockDioFirebasePerformanceInterceptor();

      // Act
      final module = NetworkModule(
        dioFactory: mockDioFactory,
        interceptors: [mockInterceptor],
      );
      await module.register(getIt);

      // Assert
      verify(() => mockDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: [mockInterceptor],
          )).called(1);
    });

    test('should handle null interceptors', () async {
      // Arrange
      getIt.reset();

      // Act
      final module = NetworkModule(
        dioFactory: mockDioFactory,
        interceptors: null,
      );
      await module.register(getIt);

      // Assert
      verify(() => mockDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: null,
          )).called(1);
    });

    test('should handle empty interceptors list', () async {
      // Arrange
      getIt.reset();

      // Act
      final module = NetworkModule(
        dioFactory: mockDioFactory,
        interceptors: [],
      );
      await module.register(getIt);

      // Assert
      verify(() => mockDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: [],
          )).called(1);
    });

    test('should handle empty baseUrl in FlavorConfig', () async {
      // Arrange
      getIt.reset();

      // Save original FlavorConfig
      final originalFlavorConfig = FlavorConfig.instance;

      // Create a FlavorConfig with empty baseUrl
      FlavorConfig(
        flavor: 'test',
        values: CommonFlavorValues(
          baseUrl: '',
          initializeFirebaseSdk: false,
          oneSignalAppId: null,
        ),
      );

      try {
        // Act
        final module = NetworkModule(dioFactory: mockDioFactory);
        await module.register(getIt);

        // Assert
        verify(() => mockDioFactory.createDio(
              getIt,
              locale: any(named: 'locale'),
              connectTimeout: any(named: 'connectTimeout'),
              receiveTimeout: any(named: 'receiveTimeout'),
              sendTimeout: any(named: 'sendTimeout'),
              baseUrl: '',
              interceptors: any(named: 'interceptors'),
            )).called(1);
      } finally {
        // Restore original FlavorConfig
        FlavorConfig(
          flavor: originalFlavorConfig.flavor,
          values: originalFlavorConfig.values,
        );
      }
    });

    test('should create a complete Dio instance with all headers', () async {
      // Arrange
      getIt.reset();

      // Create a custom Dio instance for this test
      final completeDio = Dio();
      completeDio.options.baseUrl = 'https://test.example.com';
      completeDio.options.headers = {
        HeaderKey.language: 'en',
        HeaderKey.platform: 'test-platform',
        HeaderKey.appVersion: '1.0.0',
        HeaderKey.appBuildNumber: '100',
        HeaderKey.deviceModel: 'Test Device',
        HeaderKey.osVersion: '1.0.0',
        HeaderKey.osBuildNumber: '123',
        HeaderKey.deviceId: 'test-device-id',
        HeaderKey.timeZoneOffset: '+07:00',
        HeaderKey.timeZone: 'Asia/Bangkok',
      };
      completeDio.options.connectTimeout = const Duration(seconds: 10);
      completeDio.options.receiveTimeout = const Duration(seconds: 10);
      completeDio.options.sendTimeout = const Duration(seconds: 10);

      // Configure mock to return our custom Dio
      when(() => mockDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: any(named: 'interceptors'),
          )).thenAnswer((_) async => completeDio);

      getIt.registerSingleton<DataCollector>(mockDataCollector);

      // Use the mock DioFactory
      final module = NetworkModule(
          dioFactory: mockDioFactory, locale: const Locale('en', 'US'));

      // Act
      await module.register(getIt);

      // Assert
      final dio = getIt<Dio>();

      // Verify all headers are set correctly
      expect(dio.options.headers[HeaderKey.appVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.appBuildNumber], '100');
      expect(dio.options.headers[HeaderKey.deviceModel], 'Test Device');
      expect(dio.options.headers[HeaderKey.platform], 'test-platform');
      expect(dio.options.headers[HeaderKey.osVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.language], 'en');
      expect(dio.options.headers[HeaderKey.deviceId], 'test-device-id');
      expect(dio.options.headers[HeaderKey.timeZoneOffset], isNotNull);
      expect(dio.options.headers[HeaderKey.timeZone], isNotNull);

      // Verify Dio configuration
      expect(dio.options.baseUrl, 'https://test.example.com');
      expect(dio.options.connectTimeout, const Duration(seconds: 10));
      expect(dio.options.receiveTimeout, const Duration(seconds: 10));
      expect(dio.options.sendTimeout, const Duration(seconds: 10));
    });

    test('should use default timeout values', () async {
      // Arrange
      getIt.reset();

      // Use the mock DioFactory with default timeouts
      final defaultModule = NetworkModule(dioFactory: mockDioFactory);
      await defaultModule.register(getIt);

      // Assert default timeouts
      final dio = getIt<Dio>();
      expect(dio.options.connectTimeout, const Duration(seconds: 10));
      expect(dio.options.receiveTimeout, const Duration(seconds: 10));
      expect(dio.options.sendTimeout, const Duration(seconds: 10));
    });

    test('should use custom timeout values when provided', () async {
      // Arrange
      getIt.reset();

      // Act - create module with custom timeouts
      final customModule = NetworkModule(
        dioFactory: mockDioFactory,
        connectTimeout: const Duration(seconds: 20),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 40),
      );
      await customModule.register(getIt);

      // Assert custom timeouts were passed to DioFactory
      verify(() => mockDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: const Duration(seconds: 20),
            receiveTimeout: const Duration(seconds: 30),
            sendTimeout: const Duration(seconds: 40),
            baseUrl: any(named: 'baseUrl'),
            interceptors: any(named: 'interceptors'),
          )).called(1);
    });



    test('should set timezone headers correctly', () async {
      // Arrange
      getIt.reset();

      // Use the mock DioFactory instead of real dependencies
      final module = NetworkModule(dioFactory: mockDioFactory);
      await module.register(getIt);

      // Assert
      final dio = getIt<Dio>();
      expect(dio.options.headers[HeaderKey.timeZoneOffset], isNotNull);
      expect(dio.options.headers[HeaderKey.timeZone], isNotNull);
    });

    test('should register DioClientImpl with DioRequestOptionMapper', () async {
      // Arrange
      getIt.reset();

      // Use the mock DioFactory instead of real dependencies
      final module = NetworkModule(dioFactory: mockDioFactory);
      await module.register(getIt);

      // Assert
      expect(getIt.isRegistered<CommonHttpClient>(), isTrue);
      final client = getIt<CommonHttpClient>();
      expect(client, isA<DioClientImpl>());
    });



    test('should handle DioFactory.createDio failure', () async {
      // Create a custom DioFactory that throws an exception
      final failingDioFactory = MockDioFactory();

      // Configure failingDioFactory to throw an exception
      when(() => failingDioFactory.createDio(
            any(),
            locale: any(named: 'locale'),
            connectTimeout: any(named: 'connectTimeout'),
            receiveTimeout: any(named: 'receiveTimeout'),
            sendTimeout: any(named: 'sendTimeout'),
            baseUrl: any(named: 'baseUrl'),
            interceptors: any(named: 'interceptors'),
          )).thenThrow(Exception('Failed to create Dio instance'));

      // Create a custom GetIt instance
      final mockGetIt = MockGetIt();
      when(() => mockGetIt.isRegistered<Connectivity>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<NetworkManager>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<Dio>()).thenReturn(false);
      when(() => mockGetIt.isRegistered<CommonHttpClient>()).thenReturn(true);

      // Create module with failing factory
      final module = NetworkModule(dioFactory: failingDioFactory);

      // Act & Assert
      try {
        await module.register(mockGetIt);
        fail('Expected an exception to be thrown');
      } catch (e) {
        expect(e, isA<Exception>());
        expect(e.toString(), contains('Failed to create Dio instance'));
      }
    });

    test('should handle DioClientImpl initialization failure', () async {
      // Create a custom GetIt that throws when registering CommonHttpClient
      final mockGetIt = MockGetIt();
      when(() => mockGetIt.isRegistered<Connectivity>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<NetworkManager>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<Dio>()).thenReturn(true);
      when(() => mockGetIt.isRegistered<CommonHttpClient>()).thenReturn(false);
      when(() => mockGetIt.get<Dio>()).thenReturn(testDio);

      // Make registerLazySingleton throw an exception when registering CommonHttpClient
      when(() => mockGetIt.registerLazySingleton<CommonHttpClient>(any()))
          .thenThrow(Exception('Failed to initialize DioClientImpl'));

      // Create module
      final module = NetworkModule(dioFactory: mockDioFactory);

      // Act & Assert
      try {
        await module.register(mockGetIt);
        fail('Expected an exception to be thrown');
      } catch (e) {
        expect(e, isA<Exception>());
        expect(e.toString(), contains('Failed to initialize DioClientImpl'));
      }
    });


  });
}
