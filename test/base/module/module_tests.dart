import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/init_common_package.dart';

import 'common_package_modules/analytics_module_test.dart' as analytics_module_test;
import 'common_package_modules/data_collection_module_test.dart' as data_collection_module_test;
import 'common_package_modules/device_info_module_test.dart' as device_info_module_test;
import 'common_package_modules/notification_module_test.dart' as notification_module_test;
import 'common_package_modules/utility_module_test.dart' as utility_module_test;

void main() {
  group('Module Tests', () {
    setUp(() {
      // Reset GetIt before each test group
      getIt.reset();
    });

    tearDown(() {
      // Clean up GetIt after each test group
      getIt.reset();
    });

    group('DeviceInfoModule Tests', () {
      device_info_module_test.main();
    });

    group('AnalyticsModule Tests', () {
      analytics_module_test.main();
    });

    group('DataCollectionModule Tests', () {
      data_collection_module_test.main();
    });

    group('UtilityModule Tests', () {
      utility_module_test.main();
    });

    group('NotificationModule Tests', () {
      notification_module_test.main();
    });
  });
}
