// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:async';

import 'package:flutter_common_package/base/module/event_bus.dart';
import 'package:flutter_test/flutter_test.dart';

// Test events
class TestEvent {
  final String? message;
  TestEvent(this.message);
}

class AnotherTestEvent {
  final int value;
  AnotherTestEvent(this.value);
}

void main() {
  late EventBus eventBus;

  setUp(() {
    eventBus = EventBus();
  });

  group('EventBus', () {
    test('should deliver events to registered listeners', () async {
      // Arrange
      String? receivedMessage;
      final Completer<void> completer = Completer<void>();

      eventBus.on<TestEvent>().listen((TestEvent event) {
        receivedMessage = event.message;
        completer.complete();
      });

      // Act
      eventBus.fire(TestEvent('Hello'));

      // Wait for the event to be processed
      await completer.future;

      // Assert
      expect(receivedMessage, equals('Hello'));
    });

    test('should not deliver events to listeners of different types', () async {
      // Arrange
      bool testEventReceived = false;
      bool anotherTestEventReceived = false;
      final Completer<void> completer = Completer<void>();

      eventBus.on<TestEvent>().listen((_) {
        testEventReceived = true;
        completer.complete();
      });

      eventBus.on<AnotherTestEvent>().listen((_) {
        anotherTestEventReceived = true;
      });

      // Act
      eventBus.fire(TestEvent('Hello'));

      // Wait for the event to be processed
      await completer.future;

      // Assert
      expect(testEventReceived, isTrue);
      expect(anotherTestEventReceived, isFalse);
    });

    test('should deliver events to multiple listeners of the same type', () async {
      // Arrange
      int listenerCallCount = 0;
      final Completer<void> completer = Completer<void>();
      int expectedCalls = 2;

      eventBus.on<TestEvent>().listen((_) {
        listenerCallCount++;
        if (listenerCallCount == expectedCalls) {
          completer.complete();
        }
      });

      eventBus.on<TestEvent>().listen((_) {
        listenerCallCount++;
        if (listenerCallCount == expectedCalls) {
          completer.complete();
        }
      });

      // Act
      eventBus.fire(TestEvent('Hello'));

      // Wait for both listeners to be called
      await completer.future;

      // Assert
      expect(listenerCallCount, equals(2));
    });

    test('should not deliver events after subscription is canceled', () async {
      // Arrange
      bool eventReceived = false;
      final StreamSubscription<TestEvent> subscription = eventBus.on<TestEvent>().listen((_) {
        eventReceived = true;
      });

      // Act
      await subscription.cancel();
      eventBus.fire(TestEvent('Hello'));

      // Give some time for any potential event processing
      await Future.delayed(Duration(milliseconds: 50));

      // Assert
      expect(eventReceived, isFalse);
    });

    test('should handle multiple event types correctly', () async {
      // Arrange
      String? testEventMessage;
      int? anotherTestEventValue;
      final Completer<void> completer1 = Completer<void>();
      final Completer<void> completer2 = Completer<void>();

      eventBus.on<TestEvent>().listen((TestEvent event) {
        testEventMessage = event.message;
        completer1.complete();
      });

      eventBus.on<AnotherTestEvent>().listen((AnotherTestEvent event) {
        anotherTestEventValue = event.value;
        completer2.complete();
      });

      // Act
      eventBus.fire(TestEvent('Hello'));
      eventBus.fire(AnotherTestEvent(42));

      // Wait for both events to be processed
      await Future.wait(<Future<void>>[completer1.future, completer2.future]);

      // Assert
      expect(testEventMessage, equals('Hello'));
      expect(anotherTestEventValue, equals(42));
    });

    test('should not deliver events after all subscriptions are canceled', () async {
      // Arrange
      bool eventReceived = false;
      final StreamSubscription<TestEvent> subscription = eventBus.on<TestEvent>().listen((_) {
        eventReceived = true;
      });

      // Act - cancel the subscription
      await subscription.cancel();
      eventBus.fire(TestEvent('Hello'));

      // Assert
      expect(eventReceived, isFalse);
    });

    test('should handle multiple event types independently', () async {
      // Arrange
      bool testEventReceived = false;
      bool anotherTestEventReceived = false;

      // Use a completer to ensure the event is processed
      final Completer<void> completer = Completer<void>();

      eventBus.on<TestEvent>().listen((TestEvent event) {
        testEventReceived = true;
        completer.complete();
      });

      eventBus.on<AnotherTestEvent>().listen((_) {
        anotherTestEventReceived = true;
      });

      // Act - fire only one event type
      eventBus.fire(TestEvent('Hello'));

      // Wait for the event to be processed
      await completer.future;

      // Assert - only the matching listener should be called
      expect(testEventReceived, isTrue);
      expect(anotherTestEventReceived, isFalse);
    });
    test('should fire events without error when there are no listeners', () async {
      // Act & Assert - This should not throw an error
      expect(() => eventBus.fire(TestEvent('Hello')), returnsNormally);
      expect(() => eventBus.fire(AnotherTestEvent(42)), returnsNormally);
    });

    test('should dispose all stream controllers', () async {
      // Arrange
      bool testEventReceived = false;
      bool anotherTestEventReceived = false;

      eventBus.on<TestEvent>().listen((_) {
        testEventReceived = true;
      });

      eventBus.on<AnotherTestEvent>().listen((_) {
        anotherTestEventReceived = true;
      });

      // Act
      eventBus.dispose();

      // These should not be delivered after dispose
      eventBus.fire(TestEvent('Hello'));
      eventBus.fire(AnotherTestEvent(42));

      // Give some time for any potential event processing
      await Future.delayed(Duration(milliseconds: 50));

      // Assert
      expect(testEventReceived, isFalse);
      expect(anotherTestEventReceived, isFalse);
    });

    test('should create a new stream controller when on<T>() is called after dispose', () async {
      // Arrange
      String? receivedMessage;
      final completer = Completer<void>();

      // First dispose the event bus
      eventBus.dispose();

      // Then register a new listener
      eventBus.on<TestEvent>().listen((event) {
        receivedMessage = event.message;
        completer.complete();
      });

      // Act
      eventBus.fire(TestEvent('Hello after dispose'));

      // Wait for the event to be processed
      await completer.future;

      // Assert
      expect(receivedMessage, equals('Hello after dispose'));
    });

    test('should handle multiple event types with different payloads', () async {
      // Arrange
      String? receivedMessage;
      int? receivedValue;
      final completer1 = Completer<void>();
      final completer2 = Completer<void>();

      eventBus.on<TestEvent>().listen((event) {
        receivedMessage = event.message;
        completer1.complete();
      });

      eventBus.on<AnotherTestEvent>().listen((event) {
        receivedValue = event.value;
        completer2.complete();
      });

      // Act
      eventBus.fire(TestEvent('Complex payload'));
      eventBus.fire(AnotherTestEvent(123));

      // Wait for both events to be processed
      await Future.wait([completer1.future, completer2.future]);

      // Assert
      expect(receivedMessage, equals('Complex payload'));
      expect(receivedValue, equals(123));
    });

    test('should handle edge cases with null values in events', () async {
      // Arrange
      final completer = Completer<void>();
      String? receivedMessage;

      eventBus.on<TestEvent>().listen((event) {
        receivedMessage = event.message;
        completer.complete();
      });

      // Act - fire an event with null message
      eventBus.fire(TestEvent(null));

      // Wait for the event to be processed
      await completer.future;

      // Assert
      expect(receivedMessage, isNull);
    });

    test('should handle rapid firing of multiple events', () async {
      // Arrange
      final receivedMessages = <String>[];
      final completer = Completer<void>();
      int expectedEvents = 5;

      eventBus.on<TestEvent>().listen((event) {
        receivedMessages.add(event.message!);
        if (receivedMessages.length == expectedEvents) {
          completer.complete();
        }
      });

      // Act - fire multiple events in rapid succession
      for (int i = 0; i < expectedEvents; i++) {
        eventBus.fire(TestEvent('Event $i'));
      }

      // Wait for all events to be processed
      await completer.future;

      // Assert
      expect(receivedMessages.length, equals(expectedEvents));
      for (int i = 0; i < expectedEvents; i++) {
        expect(receivedMessages[i], equals('Event $i'));
      }
    });

    test('should allow multiple dispose calls without errors', () async {
      // Act & Assert
      eventBus.dispose();
      expect(() => eventBus.dispose(), returnsNormally);
      expect(() => eventBus.dispose(), returnsNormally);
    });
  });
}
