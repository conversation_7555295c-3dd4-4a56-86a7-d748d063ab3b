// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

/// This file contains performance tests for the modular initialization system.
///
/// These tests measure and compare the performance of different module initialization
/// approaches, providing quantitative evidence of the benefits of modular initialization.
/// The tests can be used as benchmarks for performance comparisons during development.
///
/// Key metrics measured:
/// - Time to build registry with minimal features vs. all features
/// - Performance improvement when using selective initialization
///
/// Note: Performance results will vary based on the environment. These tests are designed
/// to provide relative comparisons rather than absolute performance measurements.

import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_registry.dart';
import 'package:flutter_common_package/base/module/module_registry_builder.dart';
import 'package:flutter_common_package/base/module/module_registry_internal.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:package_info_plus/package_info_plus.dart';

void main() {
  // Initialize Flutter binding
  TestWidgetsFlutterBinding.ensureInitialized();

  // Mock PackageInfo.fromPlatform
  PackageInfo.setMockInitialValues(
    appName: 'Test App',
    packageName: 'com.test.app',
    version: '1.0.0',
    buildNumber: '1',
    buildSignature: '',
  );

  // Mock the connectivity channel
  const MethodChannel channel =
      MethodChannel('dev.fluttercommunity.plus/connectivity');
  TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
      .setMockMethodCallHandler(
    channel,
    (MethodCall methodCall) async {
      if (methodCall.method == 'check') {
        return <String>['wifi'];
      }
      return null;
    },
  );

  late GetIt getIt;

  setUp(() {
    getIt = GetIt.instance;
    getIt.reset();

    // Initialize flavor configuration for testing with a unique name
    FlavorConfig(
      flavor: 'performance_test_${DateTime.now().millisecondsSinceEpoch}',
      values: CommonFlavorValues(
        baseUrl: 'https://test.example.com',
        initializeFirebaseSdk: false,
        oneSignalAppId: null,
      ),
    );
  });

  tearDown(() {
    getIt.reset();
  });

  tearDownAll(() {
    // No need to clean up FlavorConfig as we're using unique flavor names
  });

  group('Performance Comparison', () {
    /// This test compares the initialization time between minimal and full feature sets.
    ///
    /// It measures:
    /// 1. Time to build a registry with only the core module
    /// 2. Time to build a registry with 10 modules
    /// 3. The performance ratio between these two scenarios
    ///
    /// Expected results in a real environment:
    /// - Minimal initialization should be significantly faster
    /// - The performance ratio should be proportional to the number of modules
    ///
    /// This test can be used as a benchmark during development to ensure
    /// that modular initialization continues to provide performance benefits.
    test(
        'modular initialization with minimal features should be faster than traditional initialization',
        () async {
      // Create a stopwatch to measure performance
      final Stopwatch stopwatch = Stopwatch();

      // Measure time to create and build a registry with minimal features
      stopwatch.start();
      final ModuleRegistryBuilder builder = ModuleRegistry.builder(getIt);
      // Register only core module
      builder.register(_createMinimalModule('core'), source: 'test');
      final ModuleRegistry registry = builder.build();
      stopwatch.stop();

      final int minimalTime = stopwatch.elapsedMilliseconds;
      'Performance Test'.commonLog(
          'Time to build registry with minimal features: $minimalTime ms');

      // Reset stopwatch and GetIt
      stopwatch.reset();
      getIt.reset();

      // Measure time to create and build a registry with all features
      stopwatch.start();
      final ModuleRegistryBuilder fullBuilder = ModuleRegistry.builder(getIt);
      // Register multiple modules
      for (int i = 0; i < 10; i++) {
        fullBuilder.register(_createMinimalModule('module_$i'), source: 'test');
      }
      final ModuleRegistry fullRegistry = fullBuilder.build();
      stopwatch.stop();

      final int fullTime = stopwatch.elapsedMilliseconds;
      'Performance Test'
          .commonLog('Time to build registry with all features: $fullTime ms');

      // In a real environment, we would expect minimalTime to be less than fullTime
      // But in the test environment, the difference might be negligible
      'Performance Test'
          .commonLog('Performance ratio: ${fullTime / minimalTime}x');

      // We don't make assertions about the actual times, as they will vary by environment
      // Instead, we just verify that the code runs without errors
      expect(registry, isNotNull);
      expect(fullRegistry, isNotNull);
    });

    /// This test measures the performance improvement when using selective initialization.
    ///
    /// It compares:
    /// 1. Time to build a registry with 10 modules (simulating a full app)
    /// 2. Time to build a registry with only 3 modules (simulating selective initialization)
    /// 3. Calculates the percentage improvement in startup time
    ///
    /// Expected results in a real environment:
    /// - Selective initialization should be significantly faster
    /// - The performance improvement should be roughly proportional to the reduction in modules
    ///
    /// This test demonstrates the real-world benefit of the modular architecture:
    /// apps can load faster by initializing only what they need.
    test(
        'modular initialization with selective features should improve startup time',
        () async {
      // Create a stopwatch to measure performance
      final Stopwatch stopwatch = Stopwatch();

      // Measure time to initialize a registry with all modules
      final List<FeatureModule> allModules = <FeatureModule>[];
      for (int i = 0; i < 10; i++) {
        allModules.add(_createMinimalModule('all_$i'));
      }

      stopwatch.start();
      final ModuleRegistryBuilder allBuilder = ModuleRegistry.builder(getIt);
      for (final FeatureModule module in allModules) {
        allBuilder.register(module, source: 'test');
      }
      final ModuleRegistry allRegistry = allBuilder.build();
      stopwatch.stop();

      final int allTime = stopwatch.elapsedMilliseconds;
      'Performance Test'
          .commonLog('Time to build registry with all modules: $allTime ms');

      // Reset stopwatch and GetIt
      stopwatch.reset();
      getIt.reset();

      // Measure time to initialize a registry with selective modules
      final List<FeatureModule> selectiveModules = <FeatureModule>[];
      for (int i = 0; i < 3; i++) {
        selectiveModules.add(_createMinimalModule('selective_$i'));
      }

      stopwatch.start();
      final ModuleRegistryBuilder selectiveBuilder =
          ModuleRegistry.builder(getIt);
      for (final FeatureModule module in selectiveModules) {
        selectiveBuilder.register(module, source: 'test');
      }
      final ModuleRegistry selectiveRegistry = selectiveBuilder.build();
      stopwatch.stop();

      final int selectiveTime = stopwatch.elapsedMilliseconds;
      'Performance Test'.commonLog(
          'Time to build registry with selective modules: $selectiveTime ms');
      'Performance Test'.commonLog(
          'Performance improvement: ${(allTime - selectiveTime) / allTime * 100}%');

      // We don't make assertions about the actual times, as they will vary by environment
      expect(allRegistry, isNotNull);
      expect(selectiveRegistry, isNotNull);
    });
  });
}

/// Creates a minimal module for performance testing.
///
/// This is a helper function that creates a test module with the given name.
/// The module has minimal dependencies and a no-op registration function
/// to isolate the performance of the module registration system itself.
FeatureModule _createMinimalModule(String name) {
  return _TestModule(name: name);
}

/// A minimal module implementation for performance testing.
///
/// This class implements the FeatureModule interface with minimal overhead
/// to provide a consistent baseline for performance measurements.
///
/// In a real application, modules would have more complex dependencies
/// and registration logic, which would further amplify the performance
/// differences measured in these tests.
class _TestModule implements FeatureModule {
  final String _name;

  _TestModule({required String name}) : _name = name;

  @override
  String get name => _name;

  @override
  List<Type> get dependencies => <Type>[String];

  @override
  Future<void> register(GetIt getIt) async {
    // No-op for testing
    // In a real module, this would register dependencies with GetIt
  }
}
