import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/alert_manager.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/util/visible_top_page_state_helper.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:visibility_detector/visibility_detector.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockNetworkManager extends Mock implements NetworkManager {}

class MockCommonNavigatorObserver extends Mock implements CommonNavigatorObserver {}

class MockAlertManager extends Mock implements AlertManager {}

class MockBuildContext extends Mock implements BuildContext {}

class MockModalRoute<T> extends Mock implements ModalRoute<T> {}

class MockStreamSubscription<T> extends Mock implements StreamSubscription<T> {}

class MockStream<T> extends Mock implements Stream<T> {}

class MockWidgetsBinding extends Mock implements WidgetsBinding {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockVisibleTopPageStateHelper extends Mock implements VisibleTopPageStateHelper {}

class TestPageBase extends PageBase {
  const TestPageBase(
    this.routeSettings, {
    super.key,
    this.pageStateBase,
  });

  final String fakeEventTrackingScreenId = 'fake_event_tracking_screen_id';

  final TestPageBaseState? pageStateBase;

  @override
  // ignore: no_logic_in_create_state
  TestPageBaseState createState() => pageStateBase ?? TestPageBaseState();

  @override
  EventTrackingScreenId get eventTrackingScreenId =>
      EventTrackingScreenId(fakeEventTrackingScreenId);

  @override
  final RouteSettings routeSettings;
}

class TestPageBaseState extends PageStateBase<TestPageBase> {
  bool hasCallRegisterRouteObserverIfNeed = false;
  bool hasCallUnregisterRouteObserverIfNeed = false;
  bool hasCallOnResumed = false;
  bool hasCallOnPaused = false;
  bool hasCallOnPageResumed = false;
  bool hasCallOnPagePaused = false;
  bool hasCallOnInactive = false;
  bool hasCallOnPageInactive = false;
  bool hasCallOnDetached = false;
  bool hasCallOnHidden = false;
  bool hasCallOnPageHidden = false;
  bool hasCallIsTopVisible = false;
  bool hasCallIsPageVisible = false;
  bool hasCallIsPageAtTopOfNavigationStack = false;
  bool hasCallIsCurrent = false;

  @override
  Widget getContentWidget(BuildContext context) {
    return Container();
  }

  @override
  void registerRouteObserverIfNeed() {
    hasCallRegisterRouteObserverIfNeed = true;
  }

  @override
  void unregisterRouteObserverIfNeed() {
    hasCallUnregisterRouteObserverIfNeed = true;
  }

  @override
  void onResumed() {
    hasCallOnResumed = true;
    super.onResumed();
  }

  @override
  void onPaused() {
    hasCallOnPaused = true;
    super.onPaused();
  }

  @override
  void onPageResumed() {
    hasCallOnPageResumed = true;
    super.onPageResumed();
  }

  @override
  void onPagePaused() {
    hasCallOnPagePaused = true;
    super.onPagePaused();
  }

  @override
  void onInactive() {
    hasCallOnInactive = true;
    super.onInactive();
  }

  @override
  void onPageInactive() {
    hasCallOnPageInactive = true;
    super.onPageInactive();
  }

  @override
  void onDetached() {
    hasCallOnDetached = true;
    super.onDetached();
  }

  @override
  void onHidden() {
    hasCallOnHidden = true;
    super.onHidden();
  }

  @override
  void onPageHidden() {
    hasCallOnPageHidden = true;
    super.onPageHidden();
  }

  @override
  bool get isCurrent {
    hasCallIsCurrent = true;
    return true;
  }
}

void main() {
  late NetworkManager mockNetworkManager;
  late AlertManager mockAlertManager;
  late CommonUtilFunction mockCommonUtilFunction;
  late TestPageBaseState pageStateBase;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(ToastDuration.LENGTH_SHORT);

    getIt.registerSingleton<CommonImageProvider>(MockCommonImageProvider());

    getIt.registerSingleton<LoggingRepo>(MockLoggingRepo());

    getIt.registerSingleton<NetworkManager>(MockNetworkManager());
    mockNetworkManager = getIt.get<NetworkManager>();

    getIt.registerSingleton<CommonNavigatorObserver>(MockCommonNavigatorObserver());

    getIt.registerSingleton<CommonUtilFunction>(MockCommonUtilFunction());
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    getIt.registerSingleton<AlertManager>(MockAlertManager());
    mockAlertManager = getIt.get<AlertManager>();
  });

  setUp(() {
    pageStateBase = TestPageBaseState();

    pageStateBase.visibleTopPageStateHelper = MockVisibleTopPageStateHelper();

    when(() => pageStateBase.visibleTopPageStateHelper.isTopVisiblePage).thenReturn(true);
  });

  test('verify initState()', () {
    final PageStateBase<TestPageBase> pageStateBase = TestPageBaseState();

    when(() => mockNetworkManager.myStreamNetwork).thenAnswer(
      (_) => Stream<bool>.fromIterable(<bool>[true]),
    );

    pageStateBase.initState();

    expect(pageStateBase.hasListenNetwork(), true);

    verify(() => mockNetworkManager.myStreamNetwork).called(1);
  });

  test('verify didChangeDependencies()', () {
    final TestPageBaseState pageStateBase = TestPageBaseState();

    expect(pageStateBase.hasCallRegisterRouteObserverIfNeed, false);

    pageStateBase.didChangeDependencies();

    expect(pageStateBase.hasCallRegisterRouteObserverIfNeed, true);
  });

  group('verify didChangeAppLifecycleState()', () {
    setUp(() {
      when(() => mockAlertManager.setEnable(any())).thenAnswer((_) => Future<void>.value());
    });

    test('verify AppLifecycleState.resumed', () {
      pageStateBase.streamNetwork = MockStreamSubscription<bool>();
      when(() => pageStateBase.streamNetwork?.isPaused).thenReturn(true);
      when(() => pageStateBase.streamNetwork?.resume()).thenAnswer((_) async {
        return Future<void>.value();
      });
      pageStateBase.didChangeAppLifecycleState(AppLifecycleState.resumed);

      expect(pageStateBase.hasCallOnResumed, true);
      verify(() => mockAlertManager.setEnable(true)).called(1);
      verify(() => pageStateBase.streamNetwork?.resume()).called(1);
    });

    test('verify AppLifecycleState.inactive', () {
      pageStateBase.didChangeAppLifecycleState(AppLifecycleState.inactive);

      expect(pageStateBase.hasCallOnInactive, true);
    });

    test('verify AppLifecycleState.paused', () {
      pageStateBase.streamNetwork = MockStreamSubscription<bool>();
      when(() => pageStateBase.streamNetwork?.pause()).thenAnswer((_) async {
        return Future<void>.value();
      });
      pageStateBase.didChangeAppLifecycleState(AppLifecycleState.paused);

      expect(pageStateBase.hasCallOnPaused, true);
      verify(() => mockAlertManager.setEnable(false)).called(1);
      verify(() => pageStateBase.streamNetwork?.pause()).called(1);
    });

    test('verify AppLifecycleState.detached', () {
      pageStateBase.didChangeAppLifecycleState(AppLifecycleState.detached);

      expect(pageStateBase.hasCallOnDetached, true);
    });

    test('verify AppLifecycleState.hidden', () {
      pageStateBase.didChangeAppLifecycleState(AppLifecycleState.hidden);

      expect(pageStateBase.hasCallOnHidden, true);
    });
  });

  group('verify showToast()', () {
    setUp(() {
      when(() => mockAlertManager.show(
            any(),
            iosDurationInSec: any(named: 'iosDurationInSec'),
            androidDuration: any(named: 'androidDuration'),
          )).thenAnswer((_) => Future<bool?>.value());
    });

    test('should call show with message', () {
      const String fakeMessage = 'fake_message';

      // ignore: invalid_use_of_protected_member
      pageStateBase.showToast(fakeMessage);
      verify(() => mockAlertManager.show(
            fakeMessage,
            iosDurationInSec: any(named: 'iosDurationInSec'),
            androidDuration: any(named: 'androidDuration'),
          )).called(1);
    });

    test('should call show with message and android duration', () {
      const String fakeMessage = 'fake_message';

      // ignore: invalid_use_of_protected_member
      pageStateBase.showToast(
        fakeMessage,
        androidDuration: ToastDuration.LENGTH_LONG,
      );

      verify(() => mockAlertManager.show(
            fakeMessage,
            iosDurationInSec: any(named: 'iosDurationInSec'),
            androidDuration: ToastDuration.LENGTH_LONG,
          )).called(1);
    });

    test('should call show with message and ios duration in sec', () {
      const String fakeMessage = 'fake_message';
      const int fakeDurationInSec = 2;

      // ignore: invalid_use_of_protected_member
      pageStateBase.showToast(
        fakeMessage,
        iosDurationInSec: fakeDurationInSec,
      );

      verify(() => mockAlertManager.show(
            fakeMessage,
            iosDurationInSec: fakeDurationInSec,
            androidDuration: any(named: 'androidDuration'),
          )).called(1);
    });
  });

  group('verify handleApiCommonError()', () {
    const int statusCode = 404;
    const String fakeVerdict = 'fake_verdict';
    const String errorMessage = 'Not Found';

    setUp(() {
      when(() => mockAlertManager.show(
            any(),
            iosDurationInSec: any(named: 'iosDurationInSec'),
            androidDuration: any(named: 'androidDuration'),
          )).thenAnswer((_) => Future<bool?>.value());
    });

    test('should call showToast with message from getMessageByErrorCode', () {
      final ErrorUIModel mockErrorUIModel = ErrorUIModel(
        statusCode: statusCode,
        verdict: fakeVerdict,
        userMessage: errorMessage,
      );

      // ignore: invalid_use_of_protected_member
      pageStateBase.handleApiCommonError(
        mockErrorUIModel,
      );

      verify(() => mockAlertManager.show(
            CommonStrings.generic404ErrorMessage,
            iosDurationInSec: any(named: 'iosDurationInSec'),
            androidDuration: any(named: 'androidDuration'),
          )).called(1);
    });

    test('should call additionalFunction if provided', () {
      bool hasClickAdditionalFunction = false;
      void additionalFunction() {
        hasClickAdditionalFunction = true;
      }

      final ErrorUIModel mockErrorUIModel = ErrorUIModel(
        statusCode: statusCode,
        verdict: fakeVerdict,
        userMessage: errorMessage,
      );

      // ignore: invalid_use_of_protected_member
      pageStateBase.handleApiCommonError(mockErrorUIModel, additionalFunction: additionalFunction);

      verify(() => mockAlertManager.show(
            CommonStrings.generic404ErrorMessage,
            iosDurationInSec: any(named: 'iosDurationInSec'),
            androidDuration: any(named: 'androidDuration'),
          )).called(1);

      expect(hasClickAdditionalFunction, true);
    });
  });

  group('verify getMessageByErrorCode()', () {
    test('should return generic 404 error message when NOT_FOUND', () {
      final String result = pageStateBase.getMessageByErrorCode(CommonHttpClient.NOT_FOUND);
      expect(result, CommonStrings.generic404ErrorMessage);
    });

    test('should return generic 400 error message when BAD_REQUEST', () {
      final String result = pageStateBase.getMessageByErrorCode(CommonHttpClient.BAD_REQUEST);
      expect(result, CommonStrings.generic400ErrorMessage);
    });

    test(
        'should return generic 401 error message and call clearDataOnTokenInvalid when INVALID_TOKEN',
        () {
      when(() => mockCommonUtilFunction.clearDataOnTokenInvalid())
          .thenAnswer((_) => Future<void>.value());

      final String result = pageStateBase.getMessageByErrorCode(CommonHttpClient.INVALID_TOKEN);
      expect(result, CommonStrings.generic401ErrorMessage);
      verify(() => mockCommonUtilFunction.clearDataOnTokenInvalid()).called(1);
    });

    test('should return generic 429 error message when LIMIT_EXCEEDED', () {
      final String result = pageStateBase.getMessageByErrorCode(CommonHttpClient.LIMIT_EXCEEDED);
      expect(result, CommonStrings.generic429ErrorMessage);
    });

    test('should return generic 422 error message when INVALID_FORMAT', () {
      final String result = pageStateBase.getMessageByErrorCode(CommonHttpClient.INVALID_FORMAT);
      expect(result, CommonStrings.generic422ErrorMessage);
    });

    test('should return generic no internet error message when NO_INTERNET', () {
      final String result = pageStateBase.getMessageByErrorCode(CommonHttpClient.NO_INTERNET);
      expect(result, CommonStrings.genericNoInternetErrorMessage);
    });

    test('should return generic no internet error message when SOCKET_ERRORS', () {
      final String result = pageStateBase.getMessageByErrorCode(CommonHttpClient.SOCKET_ERRORS);
      expect(result, CommonStrings.genericNoInternetErrorMessage);
    });

    test('should return other generic error message for an undefined status code', () {
      final String result = pageStateBase.getMessageByErrorCode(999); // Undefined status code
      expect(result, CommonStrings.otherGenericErrorMessage);
    });
  });

  group('verify hasListenNetwork()', () {
    test('verify hasListenNetwork', () {
      expect(pageStateBase.hasListenNetwork(), true);
    });
  });

  group('verify VisibleTopPageStateHelper', () {
    late TestPageBaseState pageStateBase;

    setUp(() {
      pageStateBase = TestPageBaseState();
      pageStateBase.visibleTopPageStateHelper = MockVisibleTopPageStateHelper();

      when(() => pageStateBase.visibleTopPageStateHelper.handlePageWhenNavStackChanged(any()))
          .thenAnswer((_) => Future<void>.value());
    });

    test('verify didPopNext()', () {
      pageStateBase.didPopNext();

      verify(() => pageStateBase.visibleTopPageStateHelper
          .handlePageWhenNavStackChanged(pageStateBase.isCurrent)).called(1);
    });

    test('verify didPush()', () {
      pageStateBase.didPush();

      verify(() => pageStateBase.visibleTopPageStateHelper
          .handlePageWhenNavStackChanged(pageStateBase.isCurrent)).called(1);
    });

    test('verify didPop()', () {
      pageStateBase.didPop();

      verify(() => pageStateBase.visibleTopPageStateHelper
          .handlePageWhenNavStackChanged(pageStateBase.isCurrent)).called(1);
    });

    test('verify didPushNext()', () {
      pageStateBase.didPushNext();

      verify(() => pageStateBase.visibleTopPageStateHelper
          .handlePageWhenNavStackChanged(pageStateBase.isCurrent)).called(1);
    });
  });

  group('verify onResumed()', () {
    test('verify onResumed() with isTopVisible = true', () {
      pageStateBase.onResumed();

      expect(pageStateBase.hasCallOnPageResumed, true);
    });

    test('verify onResumed() with isTopVisible = false', () {
      when(() => pageStateBase.visibleTopPageStateHelper.isTopVisiblePage).thenReturn(false);

      pageStateBase.onResumed();

      expect(pageStateBase.hasCallOnPageResumed, false);
    });
  });

  group('verify onPaused()', () {
    test('verify onPaused() with isTopVisible = true', () {
      pageStateBase.onPaused();

      expect(pageStateBase.hasCallOnPagePaused, true);
    });

    test('verify onPaused() with isTopVisible = false', () {
      when(() => pageStateBase.visibleTopPageStateHelper.isTopVisiblePage).thenReturn(false);

      pageStateBase.onPaused();

      expect(pageStateBase.hasCallOnPagePaused, false);
    });
  });

  group('verify onInactive()', () {
    test('verify onInactive() with isTopVisible = true', () {
      pageStateBase.onInactive();

      expect(pageStateBase.hasCallOnPageInactive, true);
    });

    test('verify onInactive() with isTopVisible = false', () {
      when(() => pageStateBase.visibleTopPageStateHelper.isTopVisiblePage).thenReturn(false);

      pageStateBase.onInactive();

      expect(pageStateBase.hasCallOnPageInactive, false);
    });
  });

  group('verify onHidden()', () {
    test('verify onHidden() with isTopVisible = true', () {
      pageStateBase.onHidden();

      expect(pageStateBase.hasCallOnPageHidden, true);
    });

    test('verify onHidden() with isTopVisible = false', () {
      when(() => pageStateBase.visibleTopPageStateHelper.isTopVisiblePage).thenReturn(false);

      pageStateBase.onHidden();

      expect(pageStateBase.hasCallOnPageHidden, false);
    });
  });

  test('verify isTopVisible()', () {
    when(() => pageStateBase.visibleTopPageStateHelper.isTopVisiblePage).thenReturn(false);

    final bool result = pageStateBase.isTopVisible();

    expect(result, false);

    verify(() => pageStateBase.visibleTopPageStateHelper.isTopVisiblePage).called(1);
  });

  test('verify isPageVisible()', () {
    when(() => pageStateBase.visibleTopPageStateHelper.isPageVisible).thenReturn(true);

    final bool result = pageStateBase.isPageVisible();

    expect(result, true);

    verify(() => pageStateBase.visibleTopPageStateHelper.isPageVisible).called(1);
  });

  test('verify isPageAtTopOfNavigationStack()', () {
    when(() => pageStateBase.visibleTopPageStateHelper.isPageAtTopOfNavigationStack)
        .thenReturn(true);

    final bool result = pageStateBase.isPageAtTopOfNavigationStack();

    expect(result, true);

    verify(() => pageStateBase.visibleTopPageStateHelper.isPageAtTopOfNavigationStack).called(1);
  });

  testWidgets('verify PageBase', (WidgetTester tester) async {
    const String fakeRouteName = 'fake_route_name';
    when(() => mockNetworkManager.myStreamNetwork).thenAnswer(
      (_) => Stream<bool>.fromIterable(<bool>[true]),
    );

    pageStateBase.visibleTopPageStateHelper = MockVisibleTopPageStateHelper();

    when(() => pageStateBase.visibleTopPageStateHelper.onVisibilityChanged(
        visibleFraction: any(named: 'visibleFraction'))).thenAnswer((_) => Future<void>.value());

    final TestPageBase pageBase = TestPageBase(
      const RouteSettings(name: fakeRouteName),
      pageStateBase: pageStateBase,
    );

    await tester.runAsync(() async {
      await tester.pumpWidget(pageBase);

      // verify eventTrackingScreenId
      expect(pageBase.eventTrackingScreenId.name,
          EventTrackingScreenId(pageBase.fakeEventTrackingScreenId).name);

      // verify initState()
      expect(pageStateBase.hasListenNetwork(), true);

      verify(() => mockNetworkManager.myStreamNetwork).called(1);

      // verify didChangeDependencies()
      expect(pageStateBase.hasCallRegisterRouteObserverIfNeed, true);

      // verify build()
      final Finder visibilityDetectorFinder = find.byType(VisibilityDetector);
      expect(visibilityDetectorFinder, findsOneWidget);

      final Widget visibilityDetectorWidget = tester.widget(visibilityDetectorFinder);
      expect(visibilityDetectorWidget.key, const Key(fakeRouteName));
    });
  });
}
