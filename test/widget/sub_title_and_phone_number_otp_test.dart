import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/otp/sub_title_and_phone_number_otp.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

void main() {
  const String phoneNumber = 'phoneNumber';
  const String textOtpDesc = 'textOtpDesc';

  late CommonColors mockCommonColor;
  late CommonTextStyles mockCommonTextStyles;

  setUpAll(() {
    registerFallbackValue(Colors.black);
    registerFallbackValue(Colors.white);

    getItRegisterColor();
    getItRegisterTextStyle();

    mockCommonColor = getIt.get<CommonColors>();
    mockCommonTextStyles = getIt.get<CommonTextStyles>();

    when(() => mockCommonColor.textPassive).thenReturn(Colors.black);
    when(() => mockCommonTextStyles.bodyLarge(any())).thenReturn(
      const TextStyle(color: Colors.black),
    );
    when(() => mockCommonTextStyles.h300()).thenReturn(
      const TextStyle(color: Colors.white),
    );
  });

  group('test UI SubTitleAndPhoneNumberOtpWidget', () {
    testWidgets('verify SubTitleAndPhoneNumberOtpWidget with textOtpDesc = null',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SubTitleAndPhoneNumberOtpWidget(
              phoneNumber: phoneNumber,
              commonColors: mockCommonColor,
              commonTextStyles: mockCommonTextStyles,
            ),
          ),
        ),
      );

      final Finder richTextFinder = find.byType(RichText);
      expect(richTextFinder, findsOneWidget);

      final RichText richText = widgetTester.widget(richTextFinder);
      final TextSpan textSpan = richText.text as TextSpan;
      expect(textSpan.children?.length, 2);
      expect(textSpan.toPlainText(), '${CommonStrings.otpDesc} $phoneNumber');
      expect(textSpan.style, const TextStyle(color: Colors.black, height: 1.5));
      verify(() => mockCommonTextStyles.bodyLarge(Colors.black).copyWith(height: 1.5)).called(1);

      final TextSpan otpDescTextSpan = textSpan.children![0] as TextSpan;
      expect(otpDescTextSpan.text, '${CommonStrings.otpDesc} ');
      expect(otpDescTextSpan.style, isNull);

      final TextSpan phoneNumberTextSpan = textSpan.children![1] as TextSpan;
      expect(phoneNumberTextSpan.text, phoneNumber);
      expect(phoneNumberTextSpan.style, const TextStyle(color: Colors.white));
      verify(() => mockCommonTextStyles.h300()).called(1);
    });

    testWidgets('verify SubTitleAndPhoneNumberOtpWidget with textOtpDesc!=null',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SubTitleAndPhoneNumberOtpWidget(
              phoneNumber: phoneNumber,
              textOtpDesc: textOtpDesc,
              commonColors: mockCommonColor,
              commonTextStyles: mockCommonTextStyles,
            ),
          ),
        ),
      );

      final Finder richTextFinder = find.byType(RichText);
      expect(richTextFinder, findsOneWidget);

      final RichText richText = widgetTester.widget(richTextFinder);
      final TextSpan textSpan = richText.text as TextSpan;
      expect(textSpan.children?.length, 2);
      expect(textSpan.toPlainText(), '$textOtpDesc $phoneNumber');
      expect(textSpan.style, const TextStyle(color: Colors.black, height: 1.5));
      verify(() => mockCommonTextStyles.bodyLarge(Colors.black).copyWith(height: 1.5)).called(1);

      final TextSpan otpDescTextSpan = textSpan.children![0] as TextSpan;
      expect(otpDescTextSpan.text, '$textOtpDesc ');
      expect(otpDescTextSpan.style, isNull);

      final TextSpan phoneNumberTextSpan = textSpan.children![1] as TextSpan;
      expect(phoneNumberTextSpan.text, phoneNumber);
      expect(phoneNumberTextSpan.style, const TextStyle(color: Colors.white));
      verify(() => mockCommonTextStyles.h300()).called(1);
    });
  });
}
