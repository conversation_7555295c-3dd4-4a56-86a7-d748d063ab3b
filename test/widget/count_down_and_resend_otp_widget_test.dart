import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/otp/count_down_and_resend_otp_widget.dart';
import 'package:flutter_common_package/widget/otp/count_down_timer_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

class MockCallback extends Mock {
  void call();
}

void main() {
  const int resendInSec = 4;
  const String fakeTextOtpResendText = 'fake_otp_resend_text';
  const String fakeTextOtpDoNotReceived = 'fake_otp_do_not_received_text';

  late CommonColors mockCommonColor;
  late CommonTextStyles mockCommonTextStyles;
  late MockCallback mockOnResendOtp;
  late MockCallback mockOnFinish;

  setUpAll(() {
    registerFallbackValue(Colors.black);
    registerFallbackValue(Colors.white);
    registerFallbackValue(Colors.green);

    getItRegisterColor();
    getItRegisterTextStyle();

    mockCommonColor = getIt.get<CommonColors>();
    mockCommonTextStyles = getIt.get<CommonTextStyles>();
    mockOnResendOtp = MockCallback();
    mockOnFinish = MockCallback();

    when(() => mockCommonColor.textPassive).thenReturn(Colors.black);
    when(() => mockCommonColor.highlighted).thenReturn(Colors.green);
    when(() => mockCommonTextStyles.bodyLarge(any())).thenReturn(
      const TextStyle(color: Colors.black),
    );
  });

  Widget initCountDownAndResendOtpWidget() {
    return MaterialApp(
      home: Scaffold(
        body: CountDownAndResendOtpWidget(
          commonColors: mockCommonColor,
          commonTextStyles: mockCommonTextStyles,
          resendInSec: resendInSec,
          onResendOtp: mockOnResendOtp.call,
          textOtpResendText: fakeTextOtpResendText,
          textOtpDoNotReceived: fakeTextOtpDoNotReceived,
          onFinish: mockOnFinish.call,
        ),
      ),
    );
  }

  group('test UI CountDownAndResendOtpWidget', () {
    testWidgets('verify CountDownAndResendOtpWidget with default value',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CountDownAndResendOtpWidget(
              commonColors: mockCommonColor,
              commonTextStyles: mockCommonTextStyles,
              resendInSec: resendInSec,
            ),
          ),
        ),
      );

      final Finder textOtpDoNotReceivedFinder = find.text(CommonStrings.otpDoNotReceived);
      expect(textOtpDoNotReceivedFinder, findsOneWidget);

      final Text textOtpDoNotReceived = widgetTester.widget(textOtpDoNotReceivedFinder);
      expect(textOtpDoNotReceived.style, const TextStyle(color: Colors.black));

      expect(find.byType(CountDownTimerWidget), findsOneWidget);

      final Finder textOtpResendTextFinder = find.text(CommonStrings.otpResend);
      expect(textOtpResendTextFinder, findsOneWidget);
      final Text textOtpResendText = widgetTester.widget(textOtpResendTextFinder);
      expect(textOtpResendText.style, const TextStyle(color: Colors.black));
    });

    testWidgets('verify CountDownAndResendOtpWidget with custom value and isCountDownFinish = true',
        (WidgetTester widgetTester) async {
          await widgetTester.pumpWidget(initCountDownAndResendOtpWidget());

      final CountDownAndResendOtpWidgetState state =
          widgetTester.state(find.byType(CountDownAndResendOtpWidget));
      state.isCountDownFinish = true;

      final Finder textOtpDoNotReceivedFinder = find.text(fakeTextOtpDoNotReceived);
      expect(textOtpDoNotReceivedFinder, findsOneWidget);

      final Text textOtpDoNotReceived = widgetTester.widget(textOtpDoNotReceivedFinder);
      expect(textOtpDoNotReceived.style, const TextStyle(color: Colors.black));

      expect(find.byType(CountDownTimerWidget), findsOneWidget);

      final Finder textOtpResendTextFinder = find.text(fakeTextOtpResendText);
      expect(textOtpResendTextFinder, findsOneWidget);
      final Text textOtpResendText = widgetTester.widget(textOtpResendTextFinder);
      expect(textOtpResendText.style, const TextStyle(color: Colors.black));

      final Finder textCountDownTimerFinder = find.text(' - ');
      expect(textCountDownTimerFinder, findsOneWidget);

      final Text textCountDownTimer = widgetTester.widget(textCountDownTimerFinder);
      expect(textCountDownTimer.style, const TextStyle(color: Colors.black));

      final Finder inkWellFinder = find.byType(InkWell);
      expect(inkWellFinder, findsOneWidget);

      await widgetTester.tap(inkWellFinder);
      await widgetTester.pumpAndSettle();

      verify(() => mockOnResendOtp.call()).called(1);
    });

    testWidgets(
        'verify CountDownAndResendOtpWidget with custom value and isCountDownFinish = false',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(initCountDownAndResendOtpWidget());

      final Finder textOtpDoNotReceivedFinder = find.text(fakeTextOtpDoNotReceived);
      expect(textOtpDoNotReceivedFinder, findsOneWidget);

      final Text textOtpDoNotReceived = widgetTester.widget(textOtpDoNotReceivedFinder);
      expect(textOtpDoNotReceived.style, const TextStyle(color: Colors.black));

      expect(find.byType(CountDownTimerWidget), findsOneWidget);

      final Finder textOtpResendTextFinder = find.text(fakeTextOtpResendText);
      expect(textOtpResendTextFinder, findsOneWidget);
      final Text textOtpResendText = widgetTester.widget(textOtpResendTextFinder);
      expect(textOtpResendText.style, const TextStyle(color: Colors.black));

      final Finder textCountDownTimerFinder = find.text(' - ');
      expect(textCountDownTimerFinder, findsOneWidget);

      final Text textCountDownTimer = widgetTester.widget(textCountDownTimerFinder);
      expect(textCountDownTimer.style, const TextStyle(color: Colors.black));

      final Finder inkWellFinder = find.byType(InkWell);
      expect(inkWellFinder, findsOneWidget);

      await widgetTester.tap(inkWellFinder);
      await widgetTester.pumpAndSettle();

      verifyNever(() => mockOnResendOtp.call());
    });
  });
}
