import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill_mixin.dart';
import 'package:flutter_common_package/widget/otp_listenable/otp_listenable_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFunctions extends Mock {
  void onOtpCodeReceived(String? code);

  void onError(OtpAutoFillException? e);
}

class MockOtpAutoFill extends Mock implements OtpAutoFill {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  const SizedBox child = SizedBox.shrink();
  const String fakeCode = 'fakeCode';
  final OtpAutoFillException fakeError = OtpAutoFillException(
    reason: OtpAutoFillExceptionReason.timeout,
    message: 'timeout',
  );

  final MockFunctions mockFunctions = MockFunctions();
  final OtpAutoFill mockOtpAutoFill = MockOtpAutoFill();
  final DevicePlatform mockDevicePlatform = MockDevicePlatform();
  final LoggingRepo mockLoggingRepo = MockLoggingRepo();

  setUpAll(() {
    getIt.registerSingleton<OtpAutoFill>(mockOtpAutoFill);
    getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);
  });

  setUp(() {
    when(() => mockOtpAutoFill.startUserConsentListening(
      onExtractCode: any(named: 'onExtractCode'),
      onCode: any(named: 'onCode'),
      onError: any(named: 'onError'),
    )).thenAnswer((_) => Future<void>.value());
    when(() => mockOtpAutoFill.stopListening()).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    reset(mockFunctions);
    reset(mockOtpAutoFill);
    reset(mockDevicePlatform);
    reset(mockLoggingRepo);
  });

  testWidgets('OtpListenableWidget will start listen otp on Android', (WidgetTester tester) async {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);

    await tester.pumpWidget(OtpListenableWidget(
      onOtpCodeReceived: mockFunctions.onOtpCodeReceived,
      onError: mockFunctions.onError,
      child: child,
    ));

    final Finder childWidgetFinder = find.descendant(
      of: find.byType(OtpListenableWidget),
      matching: find.byWidget(child),
    );
    expect(childWidgetFinder, findsOneWidget);

    verify(() => mockOtpAutoFill.startUserConsentListening(
          onExtractCode: any(named: 'onExtractCode'),
          onCode: any(named: 'onCode'),
          onError: any(named: 'onError'),
        )).called(1);

    final State<StatefulWidget> state = tester.state(find.byType(OtpListenableWidget));
    expect(state, isA<OtpAutoFillListener>());

    (state as OtpAutoFillListener).onOtpCodeReceived(fakeCode);
    verify(() => mockFunctions.onOtpCodeReceived(fakeCode)).called(1);
    verify(() => mockOtpAutoFill.startUserConsentListening(
      onExtractCode: any(named: 'onExtractCode'),
      onCode: any(named: 'onCode'),
      onError: any(named: 'onError'),
    )).called(1);

    (state as OtpAutoFillListener).onOtpAutoFillError(fakeError);
    verify(() => mockFunctions.onError(fakeError)).called(1);
  });

  testWidgets('OtpListenableWidget will NOT start listen otp on iOS', (WidgetTester tester) async {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);

    await tester.pumpWidget(OtpListenableWidget(
      onOtpCodeReceived: mockFunctions.onOtpCodeReceived,
      onError: mockFunctions.onError,
      child: child,
    ));

    final Finder childWidgetFinder = find.descendant(
      of: find.byType(OtpListenableWidget),
      matching: find.byWidget(child),
    );
    expect(childWidgetFinder, findsOneWidget);

    verifyNever(() => mockOtpAutoFill.startUserConsentListening(
      onExtractCode: any(named: 'onExtractCode'),
      onCode: any(named: 'onCode'),
      onError: any(named: 'onError'),
    ));
  });
}
