import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_dialog_behavior_type.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/dialog_type.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_content.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_cta/common_dialog_bottom_sheet_cta_horizontal.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_cta/common_dialog_bottom_sheet_cta_vertical.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_header.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

class MockCommonColors extends Mock implements CommonColors {}

class MockCommonTextStyles extends Mock implements CommonTextStyles {}

class MockCommonButtonStyles extends Mock implements CommonButtonStyles {}

class MockGlobalKeyProvider extends Mock implements GlobalKeyProvider {}

class MockCallback extends Mock {
  void call();
}

void main() {
  test('Test constants', () {
    expect(
      CommonDialogBottomSheet.defaultHeaderPadding,
      const EdgeInsets.only(top: 16, bottom: 40, left: 16, right: 16),
    );

    expect(
      CommonDialogBottomSheet.defaultContentPadding,
      const EdgeInsets.only(left: 16, right: 16, bottom: 16),
    );

    expect(
      CommonDialogBottomSheet.defaultCTAPadding,
      const EdgeInsets.only(left: 16, right: 16, bottom: 16),
    );

    expect(CommonDialogBottomSheet.defaultContentSpacing, 16);
  });

  group('Test UI CommonDialogBottomSheet', () {
    late EventTrackingUtils eventTrackingUtils;
    late CommonColors commonColors;
    late CommonTextStyles commonTextStyles;
    late CommonButtonStyles commonButtonStyles;

    const String textPositive = 'textPositive';
    const String dialogId = 'dialogId';
    const String title = 'title';
    const String extraMetaDataKey = 'extraMetaDataKey';
    const String extraMetaDataValue = 'extraMetaDataValue';
    final Map<String, dynamic> loggingEventMetaData = <String, dynamic>{
      extraMetaDataKey: extraMetaDataValue,
    };
    const DialogType dialogType = DialogType.dialog;
    const String dialogContent = 'content';
    const String textNegative = 'textNegative';
    final MockCallback onClickNegative = MockCallback();
    const Text footer = Text('footer');
    final MockCallback onClickPositive = MockCallback();
    const Text header = Text('header');
    const ButtonStyle positiveButtonStyle = ButtonStyle();
    const ButtonStyle negativeButtonStyle = ButtonStyle();
    const TextStyle titleTextStyle = TextStyle();
    const TextStyle contentTextStyle = TextStyle();
    final MockCallback onClickClose = MockCallback();
    const Text buttonClose = Text('buttonClose');
    const bool isShowButtonClose = true;
    const TextAlign titleTextAlign = TextAlign.center;
    const TextAlign contentTextAlign = TextAlign.center;
    const ButtonListOrientation buttonListOrientation = ButtonListOrientation.verticalDown;
    const EdgeInsets headerPadding = EdgeInsets.all(8);
    const EdgeInsets contentPadding = EdgeInsets.all(8);
    const EdgeInsets ctaPadding = EdgeInsets.all(8);
    const double contentSpacing = 10;
    const double ctaSpacing = 10;
    const String appEventId = '01';
    const String eventTrackingScreenId = '0001';
    const String fakeOnShowMetaDataKey = 'fake_on_show_key';
    const String fakeOnShowMetaDataValue = 'fake_on_show_value';
    const Map<String, dynamic> loggingEventOnShowMetaData = <String, dynamic>{
      fakeOnShowMetaDataKey: fakeOnShowMetaDataValue,
    };

    setUpAll(() {
      getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
      eventTrackingUtils = getIt.get<EventTrackingUtils>();

      getIt.registerLazySingleton<CommonColors>(() => MockCommonColors());
      commonColors = getIt.get<CommonColors>();

      getIt.registerLazySingleton<CommonTextStyles>(() => MockCommonTextStyles());
      commonTextStyles = getIt.get<CommonTextStyles>();

      getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
      commonButtonStyles = getIt.get<CommonButtonStyles>();

      getIt.registerSingleton<GlobalKeyProvider>(MockGlobalKeyProvider());

      registerFallbackValue(EventType.userAction);
      registerFallbackValue(Colors.red);
      registerFallbackValue(ButtonSize.large);

      when(() => commonColors.bottomSheetBackground).thenReturn(Colors.red);
      when(() => commonColors.textPassive).thenReturn(Colors.green);

      when(() => commonTextStyles.h400()).thenReturn(const TextStyle());
      when(() => commonTextStyles.bodyLarge(any())).thenReturn(const TextStyle());

      when(() => commonButtonStyles.tertiary(any(), isHasShadow: any(named: 'isHasShadow')))
          .thenReturn(const ButtonStyle());
      when(() => commonButtonStyles.primary(any())).thenReturn(const ButtonStyle());

      when(() => eventTrackingUtils.sendUserActionEvent(
            eventId: any(named: 'eventId'),
            metaData: any(named: 'metaData'),
          )).thenAnswer((_) {
        return Future<void>.value();
      });

      when(() => navigatorContext?.pop()).thenReturn(null);
    });

    Widget initWidgetWithAllParams() {
      return MaterialApp(
        home: CommonDialogBottomSheet(
          textPositive: textPositive,
          dialogId: dialogId,
          title: title,
          loggingEventMetaData: loggingEventMetaData,
          dialogType: dialogType,
          content: dialogContent,
          textNegative: textNegative,
          onClickNegative: onClickNegative.call,
          footer: footer,
          onClickPositive: onClickPositive.call,
          header: header,
          positiveButtonStyle: positiveButtonStyle,
          negativeButtonStyle: negativeButtonStyle,
          titleTextStyle: titleTextStyle,
          contentTextStyle: contentTextStyle,
          onClickClose: onClickClose.call,
          buttonClose: buttonClose,
          isShowButtonClose: isShowButtonClose,
          titleTextAlign: titleTextAlign,
          contentTextAlign: contentTextAlign,
          buttonListOrientation: buttonListOrientation,
          headerPadding: headerPadding,
          contentPadding: contentPadding,
          ctaPadding: ctaPadding,
          contentSpacing: contentSpacing,
          ctaSpacing: ctaSpacing,
          eventTrackingScreenId: eventTrackingScreenId,
          loggingEventOnShowMetaData: loggingEventOnShowMetaData,
        ),
      );
    }

    testWidgets('Test UI CommonDialogBottomSheet with only required params',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: CommonDialogBottomSheet(
            textPositive: textPositive,
            dialogId: dialogId,
          ),
        ),
      );

      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId: '01.null.${EventTrackingDialogBehaviorType.show.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              DialogType.bottomSheet.type,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['title'],
              'check title',
              null,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['content'],
              'check content',
              null,
            ),
      );

      expect(find.byType(CommonDialogSheetHeader), findsNothing);

      final Finder contentFinder = find.byType(CommonDialogBottomSheetContent);
      expect(contentFinder, findsOneWidget);

      final CommonDialogBottomSheetContent content =
          tester.widget(contentFinder) as CommonDialogBottomSheetContent;
      expect(content.padding, CommonDialogBottomSheet.defaultContentPadding);
      expect(content.title, isNull);
      expect(content.content, isNull);
      expect(content.footer, isNull);
      expect(content.titleTextStyle, isNull);
      expect(content.contentTextStyle, isNull);
      expect(content.titleTextAlign, isNull);
      expect(content.contentTextAlign, isNull);
      expect(content.contentSpacing, CommonDialogBottomSheet.defaultContentSpacing);

      final Finder ctaFinder = find.byType(CommonDialogBottomSheetCTAHorizontal);
      expect(ctaFinder, findsOneWidget);
      final CommonDialogBottomSheetCTAHorizontal cta =
          tester.widget(ctaFinder) as CommonDialogBottomSheetCTAHorizontal;

      expect(cta.textDirection, TextDirection.ltr);
      expect(cta.ctaSpacing, 16);
      expect(cta.padding, CommonDialogBottomSheet.defaultCTAPadding);

      /// Positive button
      expect(cta.textPositive, textPositive);
      expect(cta.onClickPositive, isNotNull);
      cta.onClickPositive?.call();
      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId: '01.null.${EventTrackingDialogBehaviorType.clickPositive.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              DialogType.bottomSheet.type,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['selected_content'],
              'check selected_content',
              textPositive,
            ),
      );
      expect(cta.positiveButtonStyle, isNull);

      /// Negative button
      expect(cta.textNegative, isNull);
      expect(cta.onClickNegative, isNotNull);
      cta.onClickNegative?.call();
      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId: '01.null.${EventTrackingDialogBehaviorType.clickNegative.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              DialogType.bottomSheet.type,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['selected_content'],
              'check selected_content',
              null,
            ),
      );
      verify(() => navigatorContext?.pop()).called(1);
      expect(cta.negativeButtonStyle, isNull);
    });

    testWidgets('Test UI CommonDialogBottomSheet header', (WidgetTester tester) async {
      await tester.pumpWidget(initWidgetWithAllParams());

      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId:
                '$appEventId.$eventTrackingScreenId.${EventTrackingDialogBehaviorType.show.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              dialogType.type,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['title'],
              'check title',
              title,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['content'],
              'check content',
              dialogContent,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData[fakeOnShowMetaDataKey],
              'check on show key',
              fakeOnShowMetaDataValue,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData[extraMetaDataKey],
              'check extraMetaData',
              extraMetaDataValue,
            ),
      );

      final Finder headerFinder = find.byType(CommonDialogSheetHeader);
      expect(headerFinder, findsOneWidget);
      final CommonDialogSheetHeader headerWidget =
          tester.widget(headerFinder) as CommonDialogSheetHeader;
      expect(headerWidget.padding, headerPadding);
      expect(headerWidget.isShowButtonClose, isShowButtonClose);
      expect(headerWidget.buttonClose, buttonClose);
      expect(headerWidget.onClickClose, isNotNull);
      headerWidget.onClickClose?.call();

      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId:
                '$appEventId.$eventTrackingScreenId.${EventTrackingDialogBehaviorType.clickClose.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              dialogType.type,
            ),
      );
      verify(() => navigatorContext?.pop()).called(1);
      verify(() => onClickClose.call()).called(1);
      expect(headerWidget.child, header);
    });

    testWidgets('Test UI CommonDialogBottomSheet cta and content', (WidgetTester tester) async {
      await tester.pumpWidget(initWidgetWithAllParams());

      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId:
                '$appEventId.$eventTrackingScreenId.${EventTrackingDialogBehaviorType.show.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              dialogType.type,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['title'],
              'check title',
              title,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['content'],
              'check content',
              dialogContent,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData[fakeOnShowMetaDataKey],
              'check on show key',
              fakeOnShowMetaDataValue,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData[extraMetaDataKey],
              'check extraMetaData',
              extraMetaDataValue,
            ),
      );

      final Finder contentFinder = find.byType(CommonDialogBottomSheetContent);
      expect(contentFinder, findsOneWidget);
      final CommonDialogBottomSheetContent contentWidget =
          tester.widget(contentFinder) as CommonDialogBottomSheetContent;
      expect(contentWidget.padding, contentPadding);
      expect(contentWidget.title, title);
      expect(contentWidget.content, dialogContent);
      expect(contentWidget.footer, footer);
      expect(contentWidget.titleTextStyle, titleTextStyle);
      expect(contentWidget.contentTextStyle, contentTextStyle);
      expect(contentWidget.titleTextAlign, titleTextAlign);
      expect(contentWidget.contentTextAlign, contentTextAlign);
      expect(contentWidget.contentSpacing, contentSpacing);

      final Finder ctaFinder = find.byType(CommonDialogBottomSheetCTAVertical);
      expect(ctaFinder, findsOneWidget);
      final CommonDialogBottomSheetCTAVertical cta =
          tester.widget(ctaFinder) as CommonDialogBottomSheetCTAVertical;

      expect(cta.verticalDirection, VerticalDirection.down);
      expect(cta.ctaSpacing, ctaSpacing);
      expect(cta.padding, ctaPadding);

      /// Positive button
      expect(cta.textPositive, textPositive);
      expect(cta.onClickPositive, isNotNull);
      cta.onClickPositive?.call();
      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId:
                '$appEventId.$eventTrackingScreenId.${EventTrackingDialogBehaviorType.clickPositive.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              dialogType.type,
            ),
      );
      verify(() => onClickPositive.call()).called(1);
      expect(cta.positiveButtonStyle, positiveButtonStyle);

      /// Negative button
      expect(cta.textNegative, textNegative);
      expect(cta.onClickNegative, isNotNull);
      cta.onClickNegative?.call();
      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId:
                '$appEventId.$eventTrackingScreenId.${EventTrackingDialogBehaviorType.clickNegative.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              dialogType.type,
            ),
      );
      verifyNever(() => navigatorContext?.pop());
      verify(() => onClickNegative.call()).called(1);
      expect(cta.negativeButtonStyle, negativeButtonStyle);
    });

    testWidgets('Test UI CommonDialogBottomSheet dispose', (WidgetTester tester) async {
      await tester.pumpWidget(initWidgetWithAllParams());
      await tester.pumpAndSettle();

      await tester.pumpWidget(Container());
      await tester.pumpAndSettle();

      /// log event when dispose (dismissByTouchOutsideOrBack)
      expect(
        verify(
          () => eventTrackingUtils.sendUserActionEvent(
            eventId:
                '$appEventId.$eventTrackingScreenId.${EventTrackingDialogBehaviorType.dismissByOtherUserAction.eventId}',
            metaData: captureAny(named: 'metaData'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              dialogId,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              dialogType.type,
            ),
      );
    });
  });
}
