import 'package:flutter_common_package/widget/pin_code/pin_code_utils.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const double fullWidth = 100;
  const int maxPinCodeLength = 5;
  const double spaceBetweenItems = 10;
  const double expectedPinCodeTextFieldWidth = 12;

  test('verify constant value', () {
    expect(PinCodeUtils.defaultTextFieldHeight, 50);
    expect(PinCodeUtils.defaultMaxPinLength, 6);
  });

  test('verify getPinCodeTextFieldWidth', () {
    final double actualPinCodeTextFieldWidth = PinCodeUtils.instance.getPinCodeTextFieldWidth(
      maxWidth: fullWidth,
      maxPinCodeLength: maxPinCodeLength,
      spaceBetweenItems: spaceBetweenItems,
    );
    expect(actualPinCodeTextFieldWidth, expectedPinCodeTextFieldWidth);
  });
}
