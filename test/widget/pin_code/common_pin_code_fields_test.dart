import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_fields.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pin_code_fields/pin_code_fields.dart' hide ErrorAnimationType;
import 'package:pin_code_fields/src/cursor_painter.dart';

void main() {
  group('verify UI', () {
    testWidgets('renders widget with all parameters set', (WidgetTester tester) async {
      final StreamController<ErrorAnimationType> errorController =
          StreamController<ErrorAnimationType>();
      final TextEditingController controller = TextEditingController();
      final FocusNode focusNode = FocusNode();

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 5,
                controller: controller,
                focusNode: focusNode,
                obscureText: true,
                obscuringCharacter: '#',
                blinkWhenObscuring: true,
                blinkDuration: const Duration(milliseconds: 300),
                enableActiveFill: true,
                autoFocus: false,
                enabled: true,
                readOnly: false,
                textStyle: const TextStyle(fontSize: 20, color: Colors.blue),
                pastedTextStyle: const TextStyle(fontWeight: FontWeight.bold, color: Colors.green),
                backgroundColor: Colors.grey[200],
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                animationDuration: const Duration(milliseconds: 200),
                animationCurve: Curves.easeInOutCubic,
                keyboardType: TextInputType.number,
                autoDisposeControllers: false,
                textCapitalization: TextCapitalization.characters,
                textInputAction: TextInputAction.next,
                autoDismissKeyboard: true,
                autoUnfocus: true,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(10),
                  fieldHeight: 55,
                  fieldWidth: 45,
                  activeFillColor: Colors.orange,
                  selectedFillColor: Colors.yellow,
                  inactiveFillColor: Colors.white,
                  activeColor: Colors.purple,
                  selectedColor: Colors.indigo,
                  inactiveColor: Colors.grey,
                  disabledColor: Colors.grey.shade300,
                  errorBorderColor: Colors.red,
                  borderWidth: 2,
                  activeBorderWidth: 3,
                  selectedBorderWidth: 4,
                  inactiveBorderWidth: 1.5,
                  errorBorderWidth: 2.5,
                ),
                errorAnimationController: errorController,
                errorAnimationDuration: 300,
                showCursor: true,
                cursorColor: Colors.teal,
                cursorWidth: 3,
                cursorHeight: 25,
                cursorPosition: CursorPosition.bottom,
                boxShadows: <BoxShadow>[
                  BoxShadow(
                    color: Colors.black.withAlpha(51), // 0.2 opacity = 51 alpha
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  )
                ],
                hintCharacter: '*',
                hintStyle: TextStyle(color: Colors.grey.shade400),
                useHapticFeedback: true,
                hapticFeedbackTypes: HapticFeedbackTypes.light,
                scrollPadding: const EdgeInsets.all(15),
                enablePinAutofill: true,
                separatorBuilder: (BuildContext context, int index) => const SizedBox(width: 8),
              );
            },
          ),
        ),
      ));

      // Verify widget was built
      expect(find.byType(CommonPinCodeFields), findsOneWidget);

      // Verify the correct number of fields were created
      expect(find.byType(AnimatedContainer), findsNWidgets(5));

      // Verify hint characters are shown
      expect(find.text('*'), findsNWidgets(5));

      // Clean up resources
      errorController.close();
      controller.dispose();
      focusNode.dispose();
    });

    testWidgets('handles input correctly', (WidgetTester tester) async {
      String currentText = '';
      String? completedText;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                onChanged: (String text) => currentText = text,
                onCompleted: (String text) => completedText = text,
              );
            },
          ),
        ),
      ));

      await tester.tap(find.byType(CommonPinCodeFields), warnIfMissed: false);
      await tester.pump();

      await tester.enterText(find.byType(TextFormField), '123');
      await tester.pump();
      expect(currentText, '123');

      await tester.enterText(find.byType(TextFormField), '1234');
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 300));
      expect(completedText, '1234');
    });

    testWidgets('shows obscured text correctly', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                obscureText: true,
                obscuringCharacter: '*',
              );
            },
          ),
        ),
      ));

      await tester.tap(find.byType(CommonPinCodeFields), warnIfMissed: false);
      await tester.pump();

      await tester.enterText(find.byType(TextFormField), '1');
      await tester.pump();

      expect(find.text('*'), findsOneWidget);
    });

    testWidgets('buildLastItemCursor creates cursor widget correctly', (WidgetTester tester) async {
      // Setup the widget
      final cursorColor = Colors.red;
      final cursorHeight = 20.0;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                cursorColor: cursorColor,
                cursorHeight: cursorHeight,
                showCursor: true,
              );
            },
          ),
        ),
      ));

      // Get the state
      final state = tester.state<CommonPinCodeFieldsState>(find.byType(CommonPinCodeFields));

      // Call the method
      final cursor = state.buildLastItemCursor(cursorHeight, cursorColor);

      // Verify cursor is created with correct properties
      expect(cursor, isA<Widget>());
      expect(find.byType(AnimatedBuilder), findsNWidgets(4)); // Not yet in widget tree

      // Add cursor to widget tree to test
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: cursor),
      ));

      // Now we should find the cursor components
      expect(find.byType(AnimatedBuilder), findsNWidgets(2));

      // Verify cursor has FadeTransition with CustomPaint inside
      final fadeTransition = find.byType(FadeTransition);
      expect(fadeTransition, findsOneWidget);
      expect(
          find.descendant(
            of: fadeTransition,
            matching: find.byType(CustomPaint),
          ),
          findsOneWidget);
    });

    testWidgets('applies enableActiveFill correctly', (WidgetTester tester) async {
      final MaterialColor activeFillColor = Colors.amber;
      final MaterialColor selectedFillColor = Colors.cyan;
      final Color inactiveFillColor = Colors.white;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                enableActiveFill: true,
                pinTheme: PinTheme(
                  activeFillColor: activeFillColor,
                  selectedFillColor: selectedFillColor,
                  inactiveFillColor: inactiveFillColor,
                ),
              );
            },
          ),
        ),
      ));

      // Find the first animated container
      final Finder animatedContainers = find.byType(AnimatedContainer);
      final AnimatedContainer container = tester.widget(animatedContainers.at(0));
      final BoxDecoration decoration = container.decoration as BoxDecoration;

      // Should have the inactive fill color initially
      expect(decoration.color, equals(inactiveFillColor));

      // Tap to select first field
      await tester.tap(find.byType(CommonPinCodeFields), warnIfMissed: false);
      await tester.pump();

      await tester.enterText(find.byType(TextFormField), '1');
      await tester.pump();

      final AnimatedContainer selectedContainer0 = tester.widget(animatedContainers.at(0));
      final BoxDecoration selectedDecoration0 = selectedContainer0.decoration as BoxDecoration;
      expect(selectedDecoration0.color, equals(activeFillColor));

      // Now it should have the selected fill color
      final AnimatedContainer selectedContainer = tester.widget(animatedContainers.at(1));
      final BoxDecoration selectedDecoration = selectedContainer.decoration as BoxDecoration;
      expect(selectedDecoration.color, equals(selectedFillColor));
    });

    testWidgets('readOnly prevents text input', (WidgetTester tester) async {
      String? changedText;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                readOnly: true,
                onChanged: (String text) => changedText = text,
              );
            },
          ),
        ),
      ));

      await tester.tap(
        find.byType(CommonPinCodeFields),
        warnIfMissed: false,
      );
      await tester.pump();
      await tester.enterText(find.byType(TextFormField), '123');
      await tester.pump();

      // No change should be registered
      expect(changedText, null);
    });

    testWidgets('shows error animation', (WidgetTester tester) async {
      final StreamController<ErrorAnimationType> errorController =
          StreamController<ErrorAnimationType>();

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                errorAnimationController: errorController,
              );
            },
          ),
        ),
      ));

      final CommonPinCodeFieldsState state = tester.state(find.byType(CommonPinCodeFields));
      expect(state.isInErrorMode, false);

      errorController.add(ErrorAnimationType.shake);
      await tester.pump();
      expect(state.isInErrorMode, true);

      errorController.close();
    });

    testWidgets('custom cursor color', (WidgetTester tester) async {
      final MaterialColor cursorColor = Colors.red;
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                showCursor: true,
                cursorColor: cursorColor,
                cursorPosition: CursorPosition.bottom,
              );
            },
          ),
        ),
      ));

      await tester.tap(
        find.byType(CommonPinCodeFields),
        warnIfMissed: false,
      );
      await tester.pump();

      await tester.enterText(find.byType(TextFormField), '1');
      await tester.pump();

      final Finder cursorFinder = find
          .byWidgetPredicate((Widget widget) => widget is CustomPaint && widget.painter != null);
      final Widget widget = tester.widget(cursorFinder);
      expect(
          (widget as CustomPaint).painter,
          isA<CursorPainter>().having(
            (CursorPainter painter) => painter.cursorColor,
            'verify cursor color',
            cursorColor,
          ));
      // Verify custom painters are present (for cursor)
    });

    testWidgets('displays hint characters', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                hintCharacter: '0',
              );
            },
          ),
        ),
      ));

      expect(find.text('0'), findsNWidgets(4));
    });

    testWidgets('disables when enabled is false', (WidgetTester tester) async {
      String? changedText;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                enabled: false,
                onChanged: (String text) => changedText = text,
              );
            },
          ),
        ),
      ));

      await tester.tap(find.byType(CommonPinCodeFields), warnIfMissed: false);
      await tester.pump();
      await tester.enterText(find.byType(TextFormField), '1234');
      await tester.pump();

      expect(changedText, null);
    });

    testWidgets('debounceBlink toggles hasBlinked when entering text', (WidgetTester tester) async {
      // Create widget with blinkWhenObscuring enabled and a short blink duration
      final Duration blinkDuration = Duration(milliseconds: 300);

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                obscureText: true,
                blinkWhenObscuring: true,
                // Enable blinking
                blinkDuration: blinkDuration,
                // Short duration for testing
                obscuringCharacter: '●',
              );
            },
          ),
        ),
      ));

      // Tap to focus
      await tester.tap(find.byType(CommonPinCodeFields), warnIfMissed: false);
      await tester.pump();

      // Get the state to verify internal state (normally we'd use a different approach,
      // but this is the simplest way to verify private state changes)
      final CommonPinCodeFieldsState state =
          tester.state<CommonPinCodeFieldsState>(find.byType(CommonPinCodeFields));

      // Initially hasBlinked should be true (initialization in initState)
      expect(state.hasBlinked, isTrue);

      // Enter text to trigger _debounceBlink
      await tester.enterText(find.byType(TextFormField), '1');
      await tester.pump();

      // Immediately after entering text, hasBlinked should be false
      expect(state.hasBlinked, isFalse);

      // Wait slightly less than blink duration
      await tester.pump(blinkDuration - Duration(milliseconds: 50));
      expect(state.hasBlinked, isFalse); // Should still be false

      // Wait until after blink duration
      await tester.pump(Duration(milliseconds: 100));
      expect(state.hasBlinked, isTrue); // Should be true again
    });

    group('verify border and borderRadius ', () {
      testWidgets('default with box shape', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return CommonPinCodeFields(
                  appContext: context,
                  length: 4,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    activeColor: Colors.blue,
                    selectedColor: Colors.green,
                    inactiveColor: Colors.grey,
                    borderRadius: BorderRadius.circular(8),
                  ),
                );
              },
            ),
          ),
        ));

        // Find all animated containers (pin fields)
        final Finder boxContainers = find.byType(AnimatedContainer);
        final AnimatedContainer boxContainer = tester.widget(boxContainers.at(0));
        final BoxDecoration boxDecoration = boxContainer.decoration as BoxDecoration;

        // Box shape should use the pinTheme.borderRadius
        expect(boxDecoration.borderRadius, equals(BorderRadius.circular(8)));
        expect(boxDecoration.border, isA<Border>());
        expect(boxDecoration.shape, equals(BoxShape.rectangle));
      });

      testWidgets('default with underline shape', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return CommonPinCodeFields(
                  appContext: context,
                  length: 4,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.underline,
                    activeColor: Colors.blue,
                    selectedColor: Colors.green,
                    inactiveColor: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ));

        await tester.pump();

        // Find all animated containers (pin fields)
        final Finder underlineContainers = find.byType(AnimatedContainer);
        final AnimatedContainer underlineContainer = tester.widget(underlineContainers.at(0));
        final BoxDecoration underlineDecoration = underlineContainer.decoration as BoxDecoration;

        // Underline shape should have no borderRadius and only bottom border
        expect(underlineDecoration.borderRadius, isNull);
        expect(underlineDecoration.border, isA<Border>());
        expect((underlineDecoration.border as Border).bottom.style, equals(BorderStyle.solid));
      });

      testWidgets('default with circle shape', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return CommonPinCodeFields(
                  appContext: context,
                  length: 4,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.circle,
                    activeColor: Colors.blue,
                    selectedColor: Colors.green,
                    inactiveColor: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ));

        await tester.pump();

        // Find all animated containers (pin fields)
        final Finder circleContainers = find.byType(AnimatedContainer);
        final AnimatedContainer circleContainer = tester.widget(circleContainers.at(0));
        final BoxDecoration circleDecoration = circleContainer.decoration as BoxDecoration;

        // Circle shape should have no borderRadius and circle shape
        expect(circleDecoration.borderRadius, isNull);
        expect(circleDecoration.shape, equals(BoxShape.circle));
      });

      testWidgets('applied custom borderBuilder and borderRadiusBuilder',
          (WidgetTester tester) async {
        const double customWidth = 60.0;
        const double customHeight = 70.0;

        // Define custom border builder
        Border customBorder(
            BuildContext context, FieldStatus status, BorderSide borderSide, int index) {
          if (status == FieldStatus.selected) {
            return Border.all(color: Colors.purple, width: 3.0);
          } else if (status == FieldStatus.active) {
            return Border.all(color: Colors.orange, width: 2.0);
          }
          return Border.all(color: Colors.grey, width: 1.0);
        }

        // Define custom border radius builder
        BorderRadius customBorderRadius(BuildContext context, FieldStatus status, int index) {
          if (index == 0) {
            return const BorderRadius.only(
              topLeft: Radius.circular(12),
              bottomLeft: Radius.circular(12),
            );
          } else if (index == 3) {
            return const BorderRadius.only(
              topRight: Radius.circular(12),
              bottomRight: Radius.circular(12),
            );
          }
          return BorderRadius.zero;
        }

        await tester.pumpWidget(MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return CommonPinCodeFields(
                  appContext: context,
                  length: 4,
                  pinTheme: PinTheme(
                    fieldWidth: customWidth,
                    fieldHeight: customHeight,
                    activeColor: Colors.blue,
                    selectedColor: Colors.green,
                    inactiveColor: Colors.grey,
                    shape: PinCodeFieldShape.box,
                  ),
                  borderBuilder: customBorder,
                  borderRadiusBuilder: customBorderRadius,
                );
              },
            ),
          ),
        ));

        // Find all animated containers (pin fields)
        final Finder animatedContainers = find.byType(AnimatedContainer);
        expect(animatedContainers, findsNWidgets(4));

        // Select first field
        await tester.tap(
          find.byType(CommonPinCodeFields),
          warnIfMissed: false,
        );
        await tester.pump();

        // First field should have selected border and custom border radius
        final AnimatedContainer firstContainer = tester.widget(animatedContainers.at(0));
        final BoxDecoration firstDecoration = firstContainer.decoration as BoxDecoration;
        expect(firstDecoration.border, isA<Border>());
        expect((firstDecoration.borderRadius as BorderRadius).topLeft,
            equals(const Radius.circular(12)));
        expect((firstDecoration.borderRadius as BorderRadius).bottomLeft,
            equals(const Radius.circular(12)));

        // Enter text to change states
        await tester.enterText(find.byType(TextFormField), '1');
        await tester.pump();

        // Now first field should have active border
        final AnimatedContainer updatedFirstContainer = tester.widget(animatedContainers.at(0));
        final BoxDecoration updatedDecoration = updatedFirstContainer.decoration as BoxDecoration;
        expect(updatedDecoration.border, isA<Border>());
      });
    });
  });

  group('verify event', () {
    testWidgets('_getActionButtons creates correct platform-specific buttons',
        (WidgetTester tester) async {
      const String pasteText = '1234';
      final TextEditingController controller = TextEditingController();

      // Test iOS buttons
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                controller: controller,
                dialogConfig: DialogConfig(
                  platform: PinCodePlatform.iOS,
                  affirmativeText: 'Paste',
                  negativeText: 'Cancel',
                ),
              );
            },
          ),
        ),
      ));

      // Access the state and get buttons directly without showing dialog
      final state = tester.state<CommonPinCodeFieldsState>(find.byType(CommonPinCodeFields));
      final List<Widget> iOSButtons = state.getActionButtons(pasteText);

      // Verify we got 2 buttons of the right type
      expect(iOSButtons.length, 2);
      expect(iOSButtons[0], isA<CupertinoDialogAction>());
      expect(iOSButtons[1], isA<CupertinoDialogAction>());

      // Verify button properties
      final CupertinoDialogAction cancelButton = iOSButtons[0] as CupertinoDialogAction;
      final CupertinoDialogAction pasteButton = iOSButtons[1] as CupertinoDialogAction;

      expect((cancelButton.child as Text).data, 'Cancel');
      expect((pasteButton.child as Text).data, 'Paste');

      // Test Android buttons
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                controller: controller,
                dialogConfig: DialogConfig(
                  platform: PinCodePlatform.other,
                  affirmativeText: 'Paste',
                  negativeText: 'Cancel',
                ),
              );
            },
          ),
        ),
      ));

      final androidState = tester.state<CommonPinCodeFieldsState>(find.byType(CommonPinCodeFields));
      final List<Widget> androidButtons = androidState.getActionButtons(pasteText);

      // Verify we got 2 buttons of the right type
      expect(androidButtons.length, 2);
      expect(androidButtons[0], isA<TextButton>());
      expect(androidButtons[1], isA<TextButton>());

      // Verify button properties
      final TextButton androidCancelButton = androidButtons[0] as TextButton;
      final TextButton androidPasteButton = androidButtons[1] as TextButton;

      expect((androidCancelButton.child as Text).data, 'Cancel');
      expect((androidPasteButton.child as Text).data, 'Paste');

      // Test paste functionality by directly calling the onPressed handler
      androidPasteButton.onPressed!();
      expect(controller.text, equals(pasteText));
    });

    testWidgets('runHapticFeedback is called when enabled', (WidgetTester tester) async {
      // Mock haptic feedback
      final List<MethodCall> hapticLog = <MethodCall>[];

      // Set up mock handler
      TestWidgetsFlutterBinding.ensureInitialized();
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        SystemChannels.platform,
        (MethodCall methodCall) async {
          if (methodCall.method.startsWith('HapticFeedback.')) {
            hapticLog.add(methodCall);
          }
          return null;
        },
      );

      // Create widget with haptic feedback enabled
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return CommonPinCodeFields(
                appContext: context,
                length: 4,
                useHapticFeedback: true,
                hapticFeedbackTypes: HapticFeedbackTypes.light,
                onChanged: (_) {},
              );
            },
          ),
        ),
      ));

      // Tap to focus and wait for animations
      await tester.tap(find.byType(CommonPinCodeFields), warnIfMissed: false);
      await tester.pump();

      // Enter text to trigger haptic feedback
      await tester.enterText(find.byType(TextFormField), '1');
      await tester.pump();

      // Check for any haptic feedback method
      final List<String> expectedMethods = <String>[
        'HapticFeedback.lightImpact',
        'HapticFeedback.mediumImpact',
        'HapticFeedback.heavyImpact',
        'HapticFeedback.selectionClick',
        'HapticFeedback.vibrate'
      ];

      expect(
        hapticLog.any((MethodCall call) => expectedMethods.contains(call.method)),
        isTrue,
        reason:
            'Expected one of $expectedMethods but got methods: ${hapticLog.map((MethodCall c) => c.method).toList()}',
      );

      // Clean up
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(SystemChannels.platform, null);
    });

    group('verify showPasteDialog', () {
      testWidgets('should calls showDialog with CupertinoAlertDialog for iOS platform',
          (WidgetTester tester) async {
        // Setup test controller
        final TextEditingController controller = TextEditingController();

        // Build the widget with iOS platform configuration
        await tester.pumpWidget(MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return CommonPinCodeFields(
                  appContext: context,
                  length: 4,
                  controller: controller,
                  dialogConfig: DialogConfig(
                    platform: PinCodePlatform.iOS,
                    affirmativeText: 'Paste',
                    negativeText: 'Cancel',
                    dialogTitle: 'iOS Dialog',
                    dialogContent: 'Paste content ',
                  ),
                );
              },
            ),
          ),
        ));

        // Get state to access the method
        final CommonPinCodeFieldsState state =
            tester.state<CommonPinCodeFieldsState>(find.byType(CommonPinCodeFields));

        // Call the method to show dialog
        state.showPasteDialog('1234');
        await tester.pump();

        // Verify CupertinoAlertDialog appears in the widget tree
        expect(find.byType(CupertinoAlertDialog), findsOneWidget);
        expect(find.byType(AlertDialog), findsNothing);
      });

      testWidgets('should calls showDialog with AlertDialog for other platform',
          (WidgetTester tester) async {
        // Setup test controller
        final TextEditingController controller = TextEditingController();

        // Build the widget
        await tester.pumpWidget(MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return CommonPinCodeFields(
                  appContext: context,
                  length: 4,
                  controller: controller,
                );
              },
            ),
          ),
        ));

        // Get state to access the method
        final CommonPinCodeFieldsState state =
            tester.state<CommonPinCodeFieldsState>(find.byType(CommonPinCodeFields));

        // Call the method to show dialog
        state.showPasteDialog('1234');
        await tester.pump();

        // Verify dialog appears in the widget tree
        expect(find.byType(AlertDialog), findsOneWidget);
      });
    });
  });
}
