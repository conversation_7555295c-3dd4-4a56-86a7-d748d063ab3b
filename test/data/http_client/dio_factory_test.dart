import 'dart:ui';

import 'package:dio/dio.dart';
import 'package:flutter_common_package/data/http_client/dio_factory.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/data_collection/firebase_performance/fp_network_request_interceptor.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:package_info_plus/package_info_plus.dart';

class MockDeviceInfoPluginWrapper extends Mock implements DeviceInfoPluginWrapper {}
class MockDevicePlatform extends Mock implements DevicePlatform {}
class MockPackageInfo extends Mock implements PackageInfo {}
class MockDataCollector extends Mock implements DataCollector {}
class MockInterceptor extends Mock implements Interceptor {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late GetIt getIt;
  late DioFactory dioFactory;
  late MockDeviceInfoPluginWrapper mockDeviceInfoPluginWrapper;
  late MockDevicePlatform mockDevicePlatform;
  late MockPackageInfo mockPackageInfo;
  late MockDataCollector mockDataCollector;

  setUp(() {
    getIt = GetIt.instance;
    getIt.reset();

    dioFactory = const DioFactory();
    mockDeviceInfoPluginWrapper = MockDeviceInfoPluginWrapper();
    mockDevicePlatform = MockDevicePlatform();
    mockPackageInfo = MockPackageInfo();
    mockDataCollector = MockDataCollector();

    // Set up mocks
    when(() => mockDeviceInfoPluginWrapper.getDeviceModel())
        .thenReturn('Test Device');
    when(() => mockDeviceInfoPluginWrapper.getPlatformName())
        .thenReturn('test-platform');
    when(() => mockDeviceInfoPluginWrapper.getOSVersion())
        .thenReturn('1.0.0');
    when(() => mockDeviceInfoPluginWrapper.getAndroidBuildNumber())
        .thenReturn('123');
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
    when(() => mockDevicePlatform.isIOS()).thenReturn(true);
    when(() => mockPackageInfo.version).thenReturn('1.0.0');
    when(() => mockPackageInfo.buildNumber).thenReturn('100');
    when(() => mockDataCollector.getDeviceId())
        .thenAnswer((_) async => 'test-device-id');

    // Register dependencies
    getIt.registerSingleton<DeviceInfoPluginWrapper>(mockDeviceInfoPluginWrapper);
    getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    getIt.registerSingletonAsync<PackageInfo>(() async => mockPackageInfo);

    // Set up FlavorConfig
    FlavorConfig(
      flavor: 'test',
      values: CommonFlavorValues(
        baseUrl: 'https://test.example.com',
        initializeFirebaseSdk: false, // Set to false to avoid Firebase initialization
        oneSignalAppId: null,
      ),
    );
  });

  tearDown(() {
    getIt.reset();
  });

  group('DioFactory', () {
    test('should create Dio with correct configuration', () async {
      // Act
      final dio = await dioFactory.createDio(getIt, locale: const Locale('en'));

      // Assert
      expect(dio.options.headers[HeaderKey.appVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.appBuildNumber], '100');
      expect(dio.options.headers[HeaderKey.deviceModel], 'Test Device');
      expect(dio.options.headers[HeaderKey.platform], 'test-platform');
      expect(dio.options.headers[HeaderKey.osVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.language], 'en');
      expect(dio.options.headers[HeaderKey.timeZoneOffset], isNotNull);
      expect(dio.options.headers[HeaderKey.timeZone], isNotNull);
      expect(dio.options.connectTimeout, const Duration(seconds: 10));
      expect(dio.options.receiveTimeout, const Duration(seconds: 10));
      expect(dio.options.sendTimeout, const Duration(seconds: 10));
    });

    test('should use provided baseUrl when specified', () async {
      // Act
      final dio = await dioFactory.createDio(
        getIt,
        baseUrl: 'https://custom.example.com',
      );

      // Assert
      expect(dio.options.baseUrl, 'https://custom.example.com');
    });

    test('should set Android-specific headers when platform is Android', () async {
      // Arrange
      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);
      when(() => mockDeviceInfoPluginWrapper.getAndroidBuildNumber())
          .thenReturn('123');

      // Act
      final dio = await dioFactory.createDio(getIt);

      // Assert
      expect(dio.options.headers[HeaderKey.osBuildNumber], '123');
    });

    test('should not set Android-specific headers when platform is iOS', () async {
      // Arrange
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
      when(() => mockDevicePlatform.isIOS()).thenReturn(true);

      // Act
      final dio = await dioFactory.createDio(getIt);

      // Assert
      expect(dio.options.headers[HeaderKey.osBuildNumber], isNull);
    });

    test('should set device ID when DataCollector is registered', () async {
      // Arrange
      getIt.registerSingleton<DataCollector>(mockDataCollector);

      // Act
      final dio = await dioFactory.createDio(getIt);

      // Assert
      expect(dio.options.headers[HeaderKey.deviceId], 'test-device-id');
    });

    test('should not set device ID when DataCollector is not registered', () async {
      // Act
      final dio = await dioFactory.createDio(getIt);

      // Assert
      expect(dio.options.headers[HeaderKey.deviceId], isNull);
    });

    test('should use custom timeouts when provided', () async {
      // Act
      final dio = await dioFactory.createDio(
        getIt,
        connectTimeout: const Duration(seconds: 20),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 40),
      );

      // Assert
      expect(dio.options.connectTimeout, const Duration(seconds: 20));
      expect(dio.options.receiveTimeout, const Duration(seconds: 30));
      expect(dio.options.sendTimeout, const Duration(seconds: 40));
    });

    test('should add custom interceptors when provided', () async {
      // Arrange
      final mockInterceptor1 = MockInterceptor();
      final mockInterceptor2 = MockInterceptor();

      // Act
      final dio = await dioFactory.createDio(
        getIt,
        interceptors: [mockInterceptor1, mockInterceptor2],
      );

      // Assert
      expect(dio.interceptors, contains(mockInterceptor1));
      expect(dio.interceptors, contains(mockInterceptor2));
      expect(dio.interceptors.any((i) => i is DioFirebasePerformanceInterceptor), isFalse);
    });

    test('should not add Firebase interceptor when Firebase is disabled', () async {
      // Act
      final dio = await dioFactory.createDio(getIt);

      // Assert - Firebase is disabled in our test setup
      expect(dio.interceptors.any((i) => i is DioFirebasePerformanceInterceptor), isFalse);
    });

    test('should add Firebase interceptor when Firebase is enabled', () async {
      // Arrange - update FlavorConfig to enable Firebase
      FlavorConfig(
        flavor: 'test',
        values: CommonFlavorValues(
          baseUrl: 'https://test.example.com',
          initializeFirebaseSdk: true, // Enable Firebase
          oneSignalAppId: null,
        ),
      );

      // Create a mock interceptor to avoid actual Firebase initialization
      final mockInterceptor = MockInterceptor();

      // Act - use custom interceptors to avoid Firebase initialization issues
      final dio = await dioFactory.createDio(
        getIt,
        interceptors: [mockInterceptor],
      );

      // Assert
      expect(dio.interceptors, contains(mockInterceptor));
    });
  });
}
