import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockHttpClient extends Mock implements CommonHttpClient {}

class MockDatadogLogger extends Mock implements DatadogLogger {}

void main() {
  late MockHttpClient httpClient;
  late MockDatadogLogger mockDatadogLogger;
  late LoggingRepoImpl loggingRepoImpl;

  const String key1 = 'key1';
  const String value1 = 'value1';

  const String namedDataArg = 'data';
  const String errorType = 'error_type';

  setUpAll(() {
    httpClient = MockHttpClient();
    registerFallbackValue(LogLevel.info);

    when(() => httpClient.post(any(), data: any(named: namedDataArg))).thenAnswer((_) async {
      return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: null);
    });
  });

  setUp(() {
    mockDatadogLogger = MockDatadogLogger();
    when(() => mockDatadogLogger.log(any(), any(), attributes: any(named: 'attributes')))
        .thenReturn(null);
    getIt.registerLazySingleton<DatadogLogger>(() => mockDatadogLogger);
    loggingRepoImpl = LoggingRepoImpl(commonHttpClient: httpClient);
  });

  tearDown(() {
    getIt.reset();
  });

  test('Test constant values', () {
    expect(LoggingRepoImpl.eventDataTypeKey, 'type');
    expect(LoggingRepoImpl.eventTypeKey, 'event_type');
    expect(LoggingRepoImpl.dataKey, 'data');
    expect(LoggingRepoImpl.deviceTimeKey, 'device_time');
    expect(LoggingRepoImpl.screenNameKey, 'screen_name');
    expect(LoggingRepoImpl.previousScreenNameKey, 'previous_screen_name');
    expect(LoggingRepoImpl.unknownScreenName, 'unknown');
    expect(LoggingRepoImpl.argsKey, 'args');
    expect(LoggingRepoImpl.logUrl, 'events');
  });

  group('verify buildLogData()', () {
    test('verify buildLogData function work correctly with data is NULL', () {
      final Map<String, dynamic> logData =
          loggingRepoImpl.buildLogData(EventType.userAction.code, null);

      /// verify if logData always be initialized with 'event_type' and 'data'
      expect(logData.length, 2);
      expect(logData[LoggingRepoImpl.eventTypeKey], EventType.userAction.code);

      /// verify if given data is null, it will be initialized with 'device_time'
      expect(logData[LoggingRepoImpl.dataKey], isNotNull);
      expect(logData[LoggingRepoImpl.dataKey][LoggingRepoImpl.deviceTimeKey], isNotNull);
    });

    test('verify buildLogData function work correctly with data is EMPTY', () {
      final Map<String, dynamic> logData =
          loggingRepoImpl.buildLogData(EventType.userAction.code, <String, dynamic>{});

      /// verify if logData always be initialized with 'event_type' and 'data'
      expect(logData.length, 2);
      expect(logData[LoggingRepoImpl.eventTypeKey], EventType.userAction.code);

      /// verify if given data is empty, it will be initialized with 'device_time'
      expect(logData[LoggingRepoImpl.dataKey], isNotNull);
      expect(logData[LoggingRepoImpl.dataKey][LoggingRepoImpl.deviceTimeKey], isNotNull);
    });

    test('verify buildLogData function work correctly with data', () async {
      final Map<String, dynamic> logData = loggingRepoImpl.buildLogData(
        EventType.userAction.code,
        <String, dynamic>{key1: value1},
      );

      /// verify if logData always be initialized with 'event_type' and 'data'
      expect(logData.length, 2);
      expect(logData[LoggingRepoImpl.eventTypeKey], EventType.userAction.code);

      /// verify it will be initialized with 'device_time' and given data
      expect(logData[LoggingRepoImpl.dataKey], isNotNull);
      expect(logData[LoggingRepoImpl.dataKey][LoggingRepoImpl.deviceTimeKey], isNotNull);
      expect(logData[LoggingRepoImpl.dataKey][key1], value1);
    });
  });

  group('verify logEvent()', () {
    test('logEvent() work correctly if given data is NULL', () async {
      await loggingRepoImpl.logEvent(eventType: EventType.userAction);
      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.userAction.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
          },
        },
      );
    });

    test('logEvent() work correctly if given data is NOT NULL', () async {
      await loggingRepoImpl
          .logEvent(eventType: EventType.userAction, data: <String, dynamic>{key1: value1});
      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.userAction.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
            key1: value1,
          },
        },
      );
    });
  });

  group('verify logUserAction()', () {
    test('verify upload data correctly if given data is NULL', () async {
      await loggingRepoImpl.logUserAction();

      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.userAction.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
          },
        },
      );
    });

    test('verify upload data correctly if given data is EMPTY', () async {
      await loggingRepoImpl.logUserAction(
        data: <String, dynamic>{},
      );

      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.userAction.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
          },
        },
      );
    });

    test('verify upload data correctly if given data is NOT NULL', () async {
      await loggingRepoImpl.logUserAction(
        data: <String, dynamic>{key1: value1},
      );

      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.userAction.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
            key1: value1,
          },
        },
      );
    });
  });

  group('verify logUserActionV2()', () {
    test('verify upload data correctly if given data is EMPTY', () async {
      await loggingRepoImpl.logUserActionV2(
        data: <String, dynamic>{},
      );

      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.userActionV2.code,
        },
      );

      verify(() => mockDatadogLogger.log(
            LogLevel.info,
            EventType.userActionV2.code,
            attributes: <String, dynamic>{
              LoggingRepoImpl.eventTypeKey: EventType.userActionV2.code,
            },
          ));
    });

    test('verify upload data correctly if given data is NOT NULL', () async {
      await loggingRepoImpl.logUserActionV2(
        data: <String, dynamic>{key1: value1},
      );

      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.userActionV2.code,
          key1: value1,
        },
      );

      verify(() => mockDatadogLogger.log(
            LogLevel.info,
            EventType.userActionV2.code,
            attributes: <String, dynamic>{
              LoggingRepoImpl.eventTypeKey: EventType.userActionV2.code,
              key1: value1,
            },
          ));
    });
  });

  group('verify logErrorEvent() ', () {
    test('verify upload data correctly if given args is NULL', () async {
      await loggingRepoImpl.logErrorEvent(errorType: errorType);
      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.error.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
            LoggingRepoImpl.eventDataTypeKey: errorType,
            LoggingRepoImpl.argsKey: isNull
          },
        },
      );

      // Verify that logDataDog is called with the correct parameters
      verify(() => mockDatadogLogger.log(
            LogLevel.error,
            EventType.error.code,
            attributes: any(named: 'attributes'),
          ));
    });

    test('verify upload data correctly if given args is EMPTY', () async {
      const String errorType = 'error_type';
      await loggingRepoImpl.logErrorEvent(
        errorType: errorType,
        args: <String, dynamic>{},
      );

      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.error.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
            LoggingRepoImpl.eventDataTypeKey: errorType,
            LoggingRepoImpl.argsKey: isEmpty,
          },
        },
      );

      // Verify that logDataDog is called with the correct parameters
      verify(() => mockDatadogLogger.log(
            LogLevel.error,
            EventType.error.code,
            attributes: any(named: 'attributes'),
          ));
    });

    test('verify upload data correctly with given args', () async {
      const String errorType = 'error_type';
      await loggingRepoImpl.logErrorEvent(
        errorType: errorType,
        args: <String, dynamic>{key1: value1},
      );

      expect(
        verify(() => httpClient.post(
              LoggingRepoImpl.logUrl,
              data: captureAny(named: namedDataArg),
            )).captured.single,
        <String, dynamic>{
          LoggingRepoImpl.eventTypeKey: EventType.error.code,
          LoggingRepoImpl.dataKey: <String, dynamic>{
            LoggingRepoImpl.deviceTimeKey: isNotNull,
            LoggingRepoImpl.eventDataTypeKey: errorType,
            LoggingRepoImpl.argsKey: <String, dynamic>{key1: value1},
          },
        },
      );

      // Verify that logDataDog is called with the correct parameters
      verify(() => mockDatadogLogger.log(
            LogLevel.error,
            EventType.error.code,
            attributes: any(named: 'attributes'),
          ));
    });
  });

  group('verify sendLogRequest()', () {
    test('sendLogRequest should call logDataDog with default log level', () async {
      await loggingRepoImpl.sendLogRequest(EventType.userAction.code, <String, dynamic>{key1: value1});

      // Verify that logDataDog is called with the default log level (info)
      verify(() => mockDatadogLogger.log(
            LogLevel.info,
            EventType.userAction.code,
            attributes: any(named: 'attributes'),
          ));

      // Verify that post is called with the correct data
      verify(() => httpClient.post(
            LoggingRepoImpl.logUrl,
            data: any(named: namedDataArg),
          ));
    });

    test('sendLogRequest should call logDataDog with specified log level', () async {
      await loggingRepoImpl.sendLogRequest(
        EventType.error.code,
        <String, dynamic>{key1: value1},
        level: LogLevel.error,
      );

      // Verify that logDataDog is called with the specified log level (error)
      verify(() => mockDatadogLogger.log(
            LogLevel.error,
            EventType.error.code,
            attributes: any(named: 'attributes'),
          ));

      // Verify that post is called with the correct data
      verify(() => httpClient.post(
            LoggingRepoImpl.logUrl,
            data: any(named: namedDataArg),
          ));
    });
  });
}
