import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/ekyc_client_settings_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EkycClientSettingsEntity', () {
    test('unserializable constructor should set settings to null and have INVALID_FORMAT code', () {
      final EkycClientSettingsEntity entity = EkycClientSettingsEntity.unserializable();

      expect(entity.settings, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('constructor should set settings and statusCode', () {
      final Map<String, String> settings = <String, String>{'key': 'value'};
      final int statusCode = 200;
      final EkycClientSettingsEntity entity = EkycClientSettingsEntity(
        settings: settings,
        statusCode: statusCode,
      );

      expect(entity.settings, settings);
      expect(entity.statusCode, statusCode);
    });

    test('fromBaseResponse should set settings from baseResponse data', () {
      final Map<String, String> settings = <String, String>{'key': 'value'};
      final BaseResponse baseResponse = BaseResponse(
        response: <String, dynamic>{
          'data': settings,
        },
        statusCode: 200,
      );
      final EkycClientSettingsEntity entity = EkycClientSettingsEntity.fromBaseResponse(
        baseResponse,
      );

      expect(entity.settings, settings);
      expect(entity.statusCode, baseResponse.statusCode);
    });

    test('toJson should return a map with settings included', () {
      final Map<String, String> settings = <String, String>{'key': 'value'};
      final EkycClientSettingsEntity entity = EkycClientSettingsEntity(settings: settings);

      final Map<String, dynamic> json = entity.toJson();

      expect(json['data'], settings);
    });
  });
}
