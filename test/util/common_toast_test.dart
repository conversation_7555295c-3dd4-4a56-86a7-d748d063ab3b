import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/common_toast.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFlutterToastWrapper extends Mock implements FlutterToastWrapper {}

void main() {
  late MockFlutterToastWrapper mockFlutterToastWrapper;
  late CommonToast commonToast;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
  });

  setUp(() {
    mockFlutterToastWrapper = MockFlutterToastWrapper();
    commonToast = CommonToast(mockFlutterToastWrapper);
  });

  tearDown(() {
    reset(mockFlutterToastWrapper);
  });

  group('Common Toast should return the same as the lib', () {
    test('should_return_show_success', () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      final bool? result = await commonToast.show('toast message');
      verify(() => mockFlutterToastWrapper.getFlutterToast(any())).called(1);
      expect(result, await libResult);
      expect(result, true);
    });

    test('should_return_show_fail', () async {
      // premise is if there's a call to the library it will be failed
      final Future<bool> libResult = Future<bool>.value(false);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      final bool? result = await commonToast.show('toast message');
      verify(() => mockFlutterToastWrapper.getFlutterToast(any())).called(1);
      expect(result, await libResult);
      expect(result, false);
    });

    test('"cancel" method should return "true" same as the lib', () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.cancelToast()).thenAnswer((_) => libResult);

      final bool? result = await commonToast.cancelToast();
      expect(result, await libResult);
      expect(result, true);
    });

    test('"cancel" method should return "false" same as the lib', () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(false);
      when(() => mockFlutterToastWrapper.cancelToast()).thenAnswer((_) => libResult);

      final bool? result = await commonToast.cancelToast();
      expect(result, await libResult);
      expect(result, false);
    });
  });

  group('The toast feature should be enable/disable by a flag', () {
    test('The toast feature should be enable by a flag', () async {
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      commonToast.setEnable(true);
      final bool? result = await commonToast.show('toast message');
      expect(result, true);
    });

    test('The toast feature should be disable by a flag', () async {
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      commonToast.setEnable(false);
      final bool? result = await commonToast.show('toast message');
      expect(result, false);
    });
  });

  group('Same messages should not be allowed to be shown consecutively in a small amount of time',
      () {
    test('should detect previous message is the same, and shown recently', () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);
      const String firstMessage = 'toast message';
      final bool? isShowingFirstMessage = await commonToast.show(firstMessage);
      expect(isShowingFirstMessage, true);
      final bool? isShowingFirstMessageAgain = await commonToast.show(firstMessage);
      expect(isShowingFirstMessageAgain, false);

      const String secondMessage = 'different message';
      final bool? isShowingSecondMessage = await commonToast.show(secondMessage);
      expect(isShowingSecondMessage, true);
    });

    test('Different messages should allowed to be shown consecutively', () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      const String firstMessage = 'toast message';
      final bool? firstResult = await commonToast.show(firstMessage);
      verify(() => mockFlutterToastWrapper.getFlutterToast(firstMessage)).called(1);
      expect(firstResult, true);

      const String secondMessage = 'different message';
      final bool? secondResult = await commonToast.show(secondMessage);
      verify(() => mockFlutterToastWrapper.getFlutterToast(secondMessage)).called(1);
      expect(secondResult, true);
    });

    test('Same messages should not be allowed to be shown consecutively in a small amount of time',
        () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      const String message = 'toast message';
      final bool? firstResult = await commonToast.show(message);
      verify(() => mockFlutterToastWrapper.getFlutterToast(message)).called(1);
      expect(firstResult, true);

      final bool? secondResult = await commonToast.show(message);
      verifyNever(() => mockFlutterToastWrapper.getFlutterToast(message));
      expect(secondResult, false);
    });

    test(
        'Same messages should not be allowed to be shown consecutively in a small amount of time - No delay',
        () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      const String message = 'toast message';
      final bool? firstResult = await commonToast.show(message);
      verify(() => mockFlutterToastWrapper.getFlutterToast(message)).called(1);
      expect(firstResult, true);

      final bool? secondResult = await commonToast.show(message);
      verifyNever(() => mockFlutterToastWrapper.getFlutterToast(message));
      expect(secondResult, false);
    });

    test(
        'Same messages should not be allowed to be shown consecutively in a small amount of time - with delay',
        () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      const String message = 'toast message';
      final bool? firstResult = await commonToast.show(message);
      verify(() => mockFlutterToastWrapper.getFlutterToast(message)).called(1);
      expect(firstResult, true);

      /// delay 1 second before calling 2nd time,
      /// which is still shorter than [iOSToastShowingDurationInSec] 3 seconds
      /// which is the default minimum time to show the same message.
      await Future<void>.delayed(const Duration(seconds: 1));
      final bool? secondResult = await commonToast.show(message);
      verifyNever(() => mockFlutterToastWrapper.getFlutterToast(message));
      expect(secondResult, false);
    });

    test('Same messages are allowed to be shown consecutively when the interval time is big enough',
        () async {
      // premise is if there's a call to the library it will be succeed
      final Future<bool> libResult = Future<bool>.value(true);
      when(() => mockFlutterToastWrapper.getFlutterToast(any())).thenAnswer((_) => libResult);

      const String message = 'toast message';
      final bool? firstResult = await commonToast.show(message);
      verify(() => mockFlutterToastWrapper.getFlutterToast(message)).called(1);
      expect(firstResult, true);

      /// delay 4 second before calling 2nd time,
      /// which is bigger than [iOSToastShowingDurationInSec] 3 seconds
      /// the default minimum time to show the same message.
      await Future<void>.delayed(const Duration(seconds: 4));
      final bool? secondResult = await commonToast.show(message);
      verify(() => mockFlutterToastWrapper.getFlutterToast(message)).called(1);
      expect(secondResult, true);
    });
  });
}
