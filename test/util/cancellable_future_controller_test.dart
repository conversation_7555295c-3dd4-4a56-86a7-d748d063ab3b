import 'package:flutter_common_package/util/cancelable_task_controller.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String kCancelMessage = 'operation is cancelled';
  const String expectedValue = 'fake_data';
  late CancelableTaskController controller;

  setUp(() {
    controller = CancelableTaskController();
  });

  test('performTask should return the result of the task', () async {
    final Future<String> task =
        Future<String>.delayed(const Duration(milliseconds: 100), () => expectedValue);
    final String? result = await controller.perform<String>(task);
    expect(result, expectedValue);
  });

  test('cancelTask should throw CancelableException', () async {
    final Future<String> task =
        Future<String>.delayed(const Duration(milliseconds: 100), () => expectedValue);

    // ignore: argument_type_not_assignable_to_error_handler
    controller.perform<String>(task).catchError((Object error) {
      expect(error,
          isA<CancelableException>().having((CancelableException e) => e.cause, 'verify message', kCancelMessage));
      return '';
    });

    controller.cancel();
  });

  test('cancelTask should not throw CancelableException if task is already completed', () async {
    final Future<String?> task =
        Future<String?>.delayed(const Duration(milliseconds: 100), () => expectedValue);
    final String? result = await controller.perform<String>(task);
    expect(result, expectedValue);

    expect(() => controller.cancel(), returnsNormally);
  });
}
