import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:permission_handler/permission_handler.dart';

class MockDevicePlatform extends Mock implements DevicePlatform {}

class PermissionHandlerTest with PermissionHandlerMixin {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockPermissionHandlerTest with PermissionHandlerMixin {
  bool hasCalledMapToPermissionHandler = false;

  Permission permission = Permission.unknown;

  @override
  Permission mapToPermissionHandler(TsDevicePermission devicePermission) {
    hasCalledMapToPermissionHandler = true;
    return permission;
  }
}

class MockPermissionHandlerPermissionStatusTest with PermissionHandlerMixin {
  bool hasCalledMapToPermissionHandler = false;
  bool hasCalledCheckAndRequestPermission = false;
  bool hasCalledHandlePermissionStatus = false;

  Permission permission = Permission.unknown;

  @override
  Permission mapToPermissionHandler(TsDevicePermission devicePermission) {
    hasCalledMapToPermissionHandler = true;
    return permission;
  }

  @override
  Future<PermissionStatus> checkAndRequestPermission(TsDevicePermission permission) {
    hasCalledCheckAndRequestPermission = true;
    return Future<PermissionStatus>.value(PermissionStatus.denied);
  }

  @override
  void handlePermissionStatus({
    required TsDevicePermission permission,
    required PermissionStatus permissionStatus,
    PermissionHandlerCallback? callback,
  }) {
    hasCalledHandlePermissionStatus = true;
  }
}

class MockPermissionHandlerCallback extends PermissionHandlerCallback {
  bool hasCalledOnGrantedPermission = false;
  bool hasCalledOnDeniedPermission = false;

  @override
  void onGrantedPermission(TsDevicePermission permission) {
    hasCalledOnGrantedPermission = true;
  }

  @override
  void onDeniedPermission(TsDevicePermission permission) {
    hasCalledOnDeniedPermission = true;
  }
}

void main() {
  const String flutterPermission = 'flutter.baseflow.com/permissions/methods';
  const String methodCheckPermissionStatus = 'checkPermissionStatus';
  const String methodRequestPermissions = 'requestPermissions';
  const int permissionStatusDenied = 0;
  const int permissionStatusGranted = 1;
  const int permissionStatusPermanentlyDenied = 4;

  late PermissionHandlerTest permissionHandlerTest;
  late DevicePlatform mockDevicePlatform;
  const MethodChannel channel = MethodChannel(flutterPermission);
  late LoggingRepo loggingRepo;
  bool hasCallRequestPermission = false;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<DevicePlatform>(MockDevicePlatform());
    mockDevicePlatform = getIt.get<DevicePlatform>();
    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    loggingRepo = getIt.get<LoggingRepo>();

    permissionHandlerTest = PermissionHandlerTest();

    when(() => loggingRepo.logEvent(
          eventType: EventType.appTrackingTransparency,
          data: any(named: 'data'),
        )).thenAnswer((_) async => Future<void>.value());
  });

  tearDown(() {
    hasCallRequestPermission = false;
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      null,
    );
  });

  void createGrantedPermissionStatus() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        if (methodCall.method == methodCheckPermissionStatus) {
          return permissionStatusGranted;
        }

        if (methodCall.method == methodRequestPermissions) {
          return <int, int>{TsDevicePermission.camera.index: permissionStatusGranted};
        }

        return permissionStatusDenied;
      },
    );
  }

  void createDeniedPermissionStatus() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        if (methodCall.method == methodCheckPermissionStatus) {
          return permissionStatusDenied;
        }

        if (methodCall.method == methodRequestPermissions) {
          hasCallRequestPermission = true;
          return <int, int>{TsDevicePermission.camera.index: permissionStatusDenied};
        }

        return permissionStatusDenied;
      },
    );
  }

  void createPermanentlyDeniedPermissionStatus() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        if (methodCall.method == methodCheckPermissionStatus) {
          return permissionStatusPermanentlyDenied;
        }

        if (methodCall.method == methodRequestPermissions) {
          hasCallRequestPermission = true;
          return <int, int>{TsDevicePermission.camera.index: permissionStatusPermanentlyDenied};
        }

        return permissionStatusDenied;
      },
    );
  }

  group('verify checkPermissionStatusWhenAppOnResume()', () {
    late MockPermissionHandlerPermissionStatusTest mockPermissionHandlerPermissionStatusTest;
    late MockPermissionHandlerCallback callback;

    setUp(() {
      mockPermissionHandlerPermissionStatusTest = MockPermissionHandlerPermissionStatusTest();
      callback = MockPermissionHandlerCallback();
    });

    test('verify to call method', () async {
      createGrantedPermissionStatus();

      await mockPermissionHandlerPermissionStatusTest.checkPermissionStatusWhenAppOnResume(
        devicePermission: TsDevicePermission.camera,
        callback: callback,
      );

      expect(mockPermissionHandlerPermissionStatusTest.hasCalledMapToPermissionHandler, true);
      expect(mockPermissionHandlerPermissionStatusTest.hasCalledHandlePermissionStatus, true);
    });
  });

  group('verify requestPermissionWhenPageIsInit()', () {
    late MockPermissionHandlerPermissionStatusTest mockPermissionHandlerPermissionStatusTest;
    late MockPermissionHandlerCallback callback;

    setUp(() {
      mockPermissionHandlerPermissionStatusTest = MockPermissionHandlerPermissionStatusTest();
      callback = MockPermissionHandlerCallback();
    });

    test('verify to call method', () async {
      await mockPermissionHandlerPermissionStatusTest.requestPermissionWhenPageIsInit(
        devicePermission: TsDevicePermission.camera,
        callback: callback,
      );

      expect(mockPermissionHandlerPermissionStatusTest.hasCalledCheckAndRequestPermission, true);
      expect(mockPermissionHandlerPermissionStatusTest.hasCalledHandlePermissionStatus, true);
    });
  });

  group('verify checkAndRequestPermission()', () {
    late MockPermissionHandlerTest mockPermissionHandlerTest;

    setUp(() {
      mockPermissionHandlerTest = MockPermissionHandlerTest();
    });

    test('verify with status isGranted', () async {
      createGrantedPermissionStatus();

      mockPermissionHandlerTest.permission = Permission.camera;

      final PermissionStatus permissionStatus =
          await mockPermissionHandlerTest.checkAndRequestPermission(TsDevicePermission.camera);

      expect(permissionStatus, PermissionStatus.granted);
      expect(hasCallRequestPermission, false);
    });

    test('verify with status isDenied', () async {
      createDeniedPermissionStatus();

      mockPermissionHandlerTest.permission = Permission.camera;

      final PermissionStatus permissionStatus =
          await mockPermissionHandlerTest.checkAndRequestPermission(TsDevicePermission.camera);

      expect(permissionStatus, PermissionStatus.denied);
      expect(hasCallRequestPermission, true);
    });

    test('verify with status isPermanentlyDenied', () async {
      createPermanentlyDeniedPermissionStatus();

      mockPermissionHandlerTest.permission = Permission.camera;

      final PermissionStatus permissionStatus =
          await mockPermissionHandlerTest.checkAndRequestPermission(TsDevicePermission.camera);

      expect(permissionStatus, PermissionStatus.denied);
      expect(hasCallRequestPermission, true);
    });
  });

  group('handlePermissionStatus tests', () {
    late MockPermissionHandlerCallback callback;

    setUp(() {
      callback = MockPermissionHandlerCallback();
    });

    test('when permissionStatus isGranted, call onGrantedPermission', () {
      const TsDevicePermission permission = TsDevicePermission.camera;
      const PermissionStatus permissionStatus = PermissionStatus.granted;

      permissionHandlerTest.handlePermissionStatus(
        permission: permission,
        permissionStatus: permissionStatus,
        callback: callback,
      );

      expect(callback.hasCalledOnGrantedPermission, true);
      expect(callback.hasCalledOnDeniedPermission, false);
    });

    test('when permissionStatus is PermanentlyDenied, call onDeniedPermission', () {
      const TsDevicePermission permission = TsDevicePermission.camera;
      const PermissionStatus permissionStatus = PermissionStatus.permanentlyDenied;

      permissionHandlerTest.handlePermissionStatus(
        permission: permission,
        permissionStatus: permissionStatus,
        callback: callback,
      );

      expect(callback.hasCalledOnGrantedPermission, false);
      expect(callback.hasCalledOnDeniedPermission, true);
    });

    test('when permissionStatus is Denied, call onDeniedPermission', () {
      const TsDevicePermission permission = TsDevicePermission.camera;
      const PermissionStatus permissionStatus = PermissionStatus.denied;

      permissionHandlerTest.handlePermissionStatus(
        permission: permission,
        permissionStatus: permissionStatus,
        callback: callback,
      );

      expect(callback.hasCalledOnGrantedPermission, false);
      expect(callback.hasCalledOnDeniedPermission, true);
    });
  });

  group('verify mapToPermissionHandler()', () {
    test('mapToPermissionHandler returns correct Permission for a given TsDevicePermission', () {
      const TsDevicePermission tsDevicePermission = TsDevicePermission.camera;
      const Permission expectedPermission = Permission.camera;

      final Permission actualPermission =
          permissionHandlerTest.mapToPermissionHandler(tsDevicePermission);

      expect(actualPermission, expectedPermission);
    });
  });

  group('verify checkPermissionsStatus()', () {
    late MockPermissionHandlerTest mockPermissionHandlerTest;

    setUp(() {
      mockPermissionHandlerTest = MockPermissionHandlerTest();
    });

    test('verify checkPermissionsStatus return correct value', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == methodCheckPermissionStatus) {
            return permissionStatusGranted;
          }

          return permissionStatusDenied;
        },
      );

      mockPermissionHandlerTest.permission = Permission.appTrackingTransparency;

      final Permission permission = mockPermissionHandlerTest
          .mapToPermissionHandler(TsDevicePermission.appTrackingTransparency);

      final PermissionStatus status = await mockPermissionHandlerTest
          .checkPermissionsStatus(TsDevicePermission.appTrackingTransparency);

      expect(mockPermissionHandlerTest.hasCalledMapToPermissionHandler, true);
      expect(permission, Permission.appTrackingTransparency);

      expect(status, PermissionStatus.granted);
    });
  });

  group('verify requestAppTrackingAuthorizationPermission()', () {
    late MockPermissionHandlerTest mockPermissionHandlerTest;

    setUp(() {
      mockPermissionHandlerTest = MockPermissionHandlerTest();
    });

    test('verify on Android', () async {
      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);

      await mockPermissionHandlerTest.requestAppTrackingAuthorizationPermission();

      expect(mockPermissionHandlerTest.hasCalledMapToPermissionHandler, false);
    });

    test('verify on iOS with permission is denied', () async {
      createDeniedPermissionStatus();

      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);

      mockPermissionHandlerTest.mapToPermissionHandler(TsDevicePermission.appTrackingTransparency);

      await mockPermissionHandlerTest.requestAppTrackingAuthorizationPermission();

      expect(mockPermissionHandlerTest.hasCalledMapToPermissionHandler, true);

      final PermissionStatus permission = await mockPermissionHandlerTest.permission.status;

      verify(() => loggingRepo.logEvent(
            eventType: EventType.appTrackingTransparency,
            data: <String, dynamic>{'status': permission.name},
          )).called(1);
    });

    test('verify on iOS with permission is granted', () async {
      createGrantedPermissionStatus();

      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);

      mockPermissionHandlerTest.mapToPermissionHandler(TsDevicePermission.appTrackingTransparency);

      await mockPermissionHandlerTest.requestAppTrackingAuthorizationPermission();

      expect(mockPermissionHandlerTest.hasCalledMapToPermissionHandler, true);

      verifyNever(
        () => loggingRepo.logEvent(
          eventType: EventType.appTrackingTransparency,
          data: any(named: 'data'),
        ),
      );
    });
  });
}
