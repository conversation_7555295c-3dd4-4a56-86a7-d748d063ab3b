import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/shared_preferences_impl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'flutter_test_config.dart';

void main() {
  const String keyTest = 'key_test';
  const String valueTest = 'value_test';
  late SharedPreferences pref;
  late CommonSharedPreferencesHelperImpl dataSource;

  setUpAll(() async {
    pref = await testSharedPreferencesExecutable();
    dataSource = CommonSharedPreferencesHelperImpl();
    getIt.registerLazySingleton(() => CommonUtilFunction());
  });

  tearDownAll(() {
    getIt.reset();
  });

  tearDown(() {
    if (pref.containsKey(keyTest)) {
      pref.remove(keyTest);
    }
  });

  group('Test SharedPreferences library', () {
    test('should_save_value_correctly', () async {
      expect(pref.containsKey(keyTest), false);

      pref.setString(keyTest, valueTest);
      expect(pref.containsKey(keyTest), true);
      expect(pref.getString(keyTest), valueTest);

      const String valueOtherTest = 'value_other_test';
      pref.setString(keyTest, valueOtherTest);
      expect(pref.getString(keyTest), valueOtherTest);
    });

    test('should_delete_key_value_correctly', () async {
      pref.setString(keyTest, valueTest);

      expect(pref.containsKey(keyTest), true);

      pref.remove(keyTest);

      expect(pref.containsKey(keyTest), false);
      expect(pref.getString(keyTest), null);
    });

    test('should_delete_all_data_correctly', () async {
      expect(pref.containsKey(keyTest), false);

      pref.setString(keyTest, valueTest);
      expect(pref.containsKey(keyTest), true);

      expect(pref.getKeys(), <String>[keyTest]);

      pref.clear();

      expect(pref.containsKey(keyTest), false);
      expect(pref.getString(keyTest), null);

      expect(pref.getKeys(), <String>[]);
    });
  });

  group('Test SharedPreferences implement', () {
    test('should_check_method_isExistingKey()_correctly', () async {
      expect(pref.containsKey(keyTest), false);
      pref.setString(keyTest, valueTest);
      expect(pref.containsKey(keyTest), true);

      final bool result = await dataSource.isExistingKey(keyTest);
      expect(result, true);

      await pref.remove(keyTest);
      expect(await dataSource.isExistingKey(keyTest), false);
    });

    test('should_check_method_readData()_correctly', () async {
      expect(pref.containsKey(keyTest), false);

      pref.setString(keyTest, valueTest);
      expect(pref.containsKey(keyTest), true);

      final dynamic result = await dataSource.readData<dynamic>(keyTest);
      expect(result, valueTest);
      expect(result.runtimeType, String);

      // Key is not contained
      const String otherKey = 'other_key';
      final dynamic resultOther = await dataSource.readData<dynamic>(otherKey);
      expect(resultOther, null);
    });

    test('should_check_method_removeKey()_correctly', () async {
      expect(pref.containsKey(keyTest), false);
      pref.setString(keyTest, valueTest);
      expect(pref.containsKey(keyTest), true);

      final bool result = await dataSource.removeKey(keyTest);
      expect(result, true);

      expect(pref.containsKey(keyTest), false);
      expect(pref.getString(keyTest), null);
    });

    test('should_check_method_saveData()_correctly', () async {
      expect(pref.containsKey(keyTest), false);

      final bool result = await dataSource.saveData(keyTest, valueTest);
      expect(result, true);

      expect(pref.containsKey(keyTest), true);

      expect(pref.getString(keyTest), valueTest);

      final bool resultOther = await dataSource.saveData(keyTest, null);
      expect(resultOther, false);
    });

    test('should_check_save_value_PassedTutorial_correctly', () async {
      const String keyTutorial = CommonSharedPreferencesHelperImpl.tutorial;
      const bool valueTutorialTest = true;
      expect(pref.containsKey(keyTutorial), false);

      await dataSource.setPassedTutorial(valueTutorialTest);

      expect(pref.containsKey(keyTutorial), true);

      final bool? result = pref.getBool(keyTutorial);
      expect(result, valueTutorialTest);

      // clear key test
      pref.remove(keyTutorial);
    });

    test('should_check_get_value_isPassedTutorial_correctly', () async {
      const String keyTutorial = CommonSharedPreferencesHelperImpl.tutorial;
      const bool valueTutorialTest = true;

      expect(pref.containsKey(keyTutorial), false);

      await pref.setBool(keyTutorial, valueTutorialTest);

      final bool? result = await dataSource.isPassedTutorial();
      expect(result, valueTutorialTest);

      await pref.remove(keyTutorial);
      expect(await dataSource.isPassedTutorial(), false);
    });

    test('should_check_save_value_FirstAppLaunch_correctly', () async {
      const String keyFirstAppLaunch = CommonSharedPreferencesHelperImpl.firstAppLaunch;
      const bool valueFirstAppLaunch = true;

      expect(pref.containsKey(keyFirstAppLaunch), false);

      await dataSource.setFirstAppLaunch(valueFirstAppLaunch);

      expect(pref.containsKey(keyFirstAppLaunch), true);

      final bool? result = pref.getBool(keyFirstAppLaunch);
      expect(result, valueFirstAppLaunch);

      // clear key test
      pref.remove(keyFirstAppLaunch);
    });

    test('should_check_get_value_isFirstAppLaunch_correctly', () async {
      const String keyFirstAppLaunch = CommonSharedPreferencesHelperImpl.firstAppLaunch;
      const bool valueFirstAppLaunchTest = true;

      expect(pref.containsKey(keyFirstAppLaunch), false);

      await pref.setBool(keyFirstAppLaunch, valueFirstAppLaunchTest);

      final bool result = await dataSource.isFirstAppLaunch();
      expect(result, valueFirstAppLaunchTest);

      await pref.remove(keyFirstAppLaunch);
      expect(await dataSource.isFirstAppLaunch(), true);
    });
  });
}
