import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockAndroidDeviceInfo extends Mock implements AndroidDeviceInfo {}

class MockIosDeviceInfo extends Mock implements IosDeviceInfo {}

class MockIosUtsname extends Mock implements IosUtsname {}

class MockAndroidBuildVersion extends Mock implements AndroidBuildVersion {}

void main() {
  late DeviceInfoPlugin mockDeviceInfoPlugin;
  late DevicePlatform mockDevicePlatform;
  late DeviceInfoPluginWrapper deviceInfoPluginWrapper;

  const String testId = 'test_device_id';
  late MockIosDeviceInfo iosInfo;
  late MockAndroidDeviceInfo androidDeviceInfo30;



  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerSingleton<DeviceInfoPlugin>(MockDeviceInfoPlugin());
    mockDeviceInfoPlugin = getIt.get<DeviceInfoPlugin>();

    getIt.registerSingleton<DevicePlatform>(MockDevicePlatform());
    mockDevicePlatform = getIt.get<DevicePlatform>();
  });

  setUp(() {
    // Create mock iOS and Android device info
    iosInfo = MockIosDeviceInfo();
    androidDeviceInfo30 = MockAndroidDeviceInfo();

    // Configure iOS mock
    when(() => iosInfo.systemVersion).thenReturn('systemVersion');
    final mockIosUtsname = MockIosUtsname();
    when(() => mockIosUtsname.machine).thenReturn('machine');
    when(() => iosInfo.utsname).thenReturn(mockIosUtsname);
    when(() => iosInfo.identifierForVendor).thenReturn(testId);

    // Configure Android mock
    final mockAndroidBuildVersion = MockAndroidBuildVersion();
    when(() => mockAndroidBuildVersion.sdkInt).thenReturn(30);
    when(() => androidDeviceInfo30.version).thenReturn(mockAndroidBuildVersion);
    when(() => androidDeviceInfo30.manufacturer).thenReturn('.manufacturer');
    when(() => androidDeviceInfo30.model).thenReturn('.model');
    when(() => androidDeviceInfo30.id).thenReturn('.id');

    deviceInfoPluginWrapper = DeviceInfoPluginWrapper(
      deviceInfo: mockDeviceInfoPlugin,
      platform: mockDevicePlatform,
    );

    when(() => mockDeviceInfoPlugin.iosInfo).thenAnswer((_) {
      return Future<IosDeviceInfo>.value(iosInfo);
    });

    when(() => mockDeviceInfoPlugin.androidInfo).thenAnswer((_) {
      return Future<AndroidDeviceInfo>.value(androidDeviceInfo30);
    });
  });

  void mockDeviceIsAndroid() {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);
  }

  void mockDeviceIsIOS() {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
    when(() => mockDevicePlatform.isIOS()).thenReturn(true);
  }

  void mockDeviceIsOtherPlatform() {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);
  }

  group('verify initDeviceInfo()', () {
    test('initDeviceInfo should initialize androidInfo for Android platform', () async {
      mockDeviceIsAndroid();

      await deviceInfoPluginWrapper.initDeviceInfo();

      expect(deviceInfoPluginWrapper.androidInfo, androidDeviceInfo30);
      expect(deviceInfoPluginWrapper.iosInfo, null);
    });

    test('initDeviceInfo should initialize iOSInfo for iOS platform', () async {
      mockDeviceIsIOS();

      await deviceInfoPluginWrapper.initDeviceInfo();

      expect(deviceInfoPluginWrapper.androidInfo, null);
      expect(deviceInfoPluginWrapper.iosInfo, iosInfo);
    });

    test('initDeviceInfo should return exception with others platform', () async {
      mockDeviceIsOtherPlatform();

      expect(deviceInfoPluginWrapper.initDeviceInfo(), throwsA(isA<Exception>()));
    });
  });

  group('verify getPlatformName()', () {
    test('getPlatformName should return "android" for Android platform', () {
      mockDeviceIsAndroid();

      final String platformName = deviceInfoPluginWrapper.getPlatformName();

      expect(platformName, 'android');
    });

    test('getPlatformName should return "ios" for iOS platform', () {
      mockDeviceIsIOS();

      final String platformName = deviceInfoPluginWrapper.getPlatformName();

      expect(platformName, 'ios');
    });

    test('getPlatformName should throw exception for other platform', () {
      mockDeviceIsOtherPlatform();

      expect(() => deviceInfoPluginWrapper.getPlatformName(), throwsA(isA<Exception>()));
    });
  });

  group('verify getDeviceModel(), getOSVersion(), getAndroidBuildNumber()', () {
    test('verify on Android with androidInfo is not null', () {
      mockDeviceIsAndroid();

      deviceInfoPluginWrapper.androidInfo = androidDeviceInfo30;

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, '.manufacturer .model');
      expect(deviceOsVersion, '30');
      expect(deviceAndroidBuildNumber, '.id');
    });

    test('verify on Android with androidInfo is null', () {
      mockDeviceIsAndroid();

      deviceInfoPluginWrapper.androidInfo = null;

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, null);
      expect(deviceOsVersion, null);
      expect(deviceAndroidBuildNumber, null);
    });

    test('verify on iOS with iosInfo is not null', () {
      deviceInfoPluginWrapper.iosInfo = iosInfo;

      mockDeviceIsIOS();

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, 'machine');
      expect(deviceOsVersion, 'systemVersion');
      expect(deviceAndroidBuildNumber, null);
    });

    test('verify on iOS with iosInfo is null', () {
      deviceInfoPluginWrapper.iosInfo = null;

      mockDeviceIsIOS();

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, null);
      expect(deviceOsVersion, null);
      expect(deviceAndroidBuildNumber, null);
    });
  });
}
