import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill_mixin.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockOtpAutoFill extends Mock implements OtpAutoFill {}

class MOckOtpAutoFillListener extends Mock implements OtpAutoFillListener {}

class MockWidgetWithMixin with OtpAutoFillMixin {}

class MockDeviceNativePlatform extends Mock implements DevicePlatform {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  late MockWidgetWithMixin mockWidgetWithCodeAutoFill;
  late MockOtpAutoFill mockSmsAutoFill;
  late MockDeviceNativePlatform mockDeviceNativePlatform;
  late MockLoggingRepo mockLoggingRepo;

  setUpAll(() {
    registerFallbackValue(OtpAutoFillException(reason: OtpAutoFillExceptionReason.others));

    getIt.registerLazySingleton<OtpAutoFill>(() => MockOtpAutoFill());
    mockSmsAutoFill = getIt.get<OtpAutoFill>() as MockOtpAutoFill;

    getIt.registerLazySingleton<DevicePlatform>(() => MockDeviceNativePlatform());
    mockDeviceNativePlatform = getIt.get<DevicePlatform>() as MockDeviceNativePlatform;

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>() as MockLoggingRepo;
  });

  setUp(() {
    mockWidgetWithCodeAutoFill = MockWidgetWithMixin();
  });

  group('verify listenForCode()', () {
    setUp(() {
      when(() => mockSmsAutoFill.startUserConsentListening(
            onExtractCode: any(named: 'onExtractCode'),
            onCode: any(named: 'onCode'),
            onError: any(named: 'onError'),
          )).thenAnswer((_) => Future<void>.value());
    });

    test(
        'For Android Device, listenForCode should call startUserConsentListening with correct parameters'
        '& should call onSMSCodeReceived if detect OTP code success', () {
      // arrange
      final MOckOtpAutoFillListener listener = MOckOtpAutoFillListener();
      mockWidgetWithCodeAutoFill.otpAutoFillListener = listener;

      when(() => mockDeviceNativePlatform.isAndroid()).thenReturn(true);

      // act
      mockWidgetWithCodeAutoFill.listenForCode();

      // get captured data
      final List<dynamic> capturedData = verify(() => mockSmsAutoFill.startUserConsentListening(
            onExtractCode: any(named: 'onExtractCode'),
            onCode: captureAny(named: 'onCode'),
            onError: any(named: 'onError'),
          )).captured;

      // act
      final void Function(String code) onCode = capturedData[0] as void Function(String);
      onCode.call('123456');

      // assert
      verify(() => listener.onOtpCodeReceived('123456')).called(1);
    });

    test(
        'For Android Device, listenForCode should call startUserConsentListening with correct parameters'
        '& should call onSMSAutoFillError if error happened', () {
      // arrange
      final MOckOtpAutoFillListener listener = MOckOtpAutoFillListener();
      mockWidgetWithCodeAutoFill.otpAutoFillListener = listener;

      when(() => mockDeviceNativePlatform.isAndroid()).thenReturn(true);
      when(() => mockLoggingRepo.logErrorEvent(
          errorType: any(named: 'errorType'),
          args: any(named: 'args'))).thenAnswer((_) => Future<void>.value());

      // act
      mockWidgetWithCodeAutoFill.listenForCode();

      // get captured data
      final List<dynamic> capturedData = verify(() => mockSmsAutoFill.startUserConsentListening(
            onExtractCode: any(named: 'onExtractCode'),
            onCode: any(named: 'onCode'),
            onError: captureAny(named: 'onError'),
          )).captured;

      // act
      final void Function(OtpAutoFillException exception) onError =
          capturedData[0] as void Function(OtpAutoFillException);
      onError
          .call(OtpAutoFillException(reason: OtpAutoFillExceptionReason.others, message: 'others'));

      // assert
      verify(() => mockLoggingRepo.logErrorEvent(errorType: EventType.otpAutoFill.name, args: any(named: 'args'))).called(1);
      verify(() => listener.onOtpAutoFillError(any())).called(1);
    });

    test('For iOS Device, listenForCode should NOT call startUserConsentListening', () {
      // arrange
      final MOckOtpAutoFillListener listener = MOckOtpAutoFillListener();
      mockWidgetWithCodeAutoFill.otpAutoFillListener = listener;

      when(() => mockDeviceNativePlatform.isAndroid()).thenReturn(false);
      when(() => mockDeviceNativePlatform.isIOS()).thenReturn(true);

      // act
      mockWidgetWithCodeAutoFill.listenForCode();

      // assert
      verifyNever(() => mockSmsAutoFill.startUserConsentListening(
            onExtractCode: any(named: 'onExtractCode'),
            onCode: any(named: 'onCode'),
            onError: captureAny(named: 'onError'),
          ));
    });
  });

  group('verify createExtractCode()', () {
    test(
        'should return code (6 digit) if using DEFAULT regex pattern (find out 6 digit code from sms content)',
        () {
      // arrange
      const String smsContent = 'Your verification code is: 666666';
      final String Function(String?) extractCode = mockWidgetWithCodeAutoFill.createExtractCode();

      // act
      final String code = extractCode(smsContent);

      // assert
      expect(code, '666666');
    });

    test('should return first code (6 digit) if sms content does contain multiple code (6 digit)',
        () {
      // arrange
      const String smsContent = 'Your verification code is: 111111. and other code is 666666';
      final String Function(String?) extractCode = mockWidgetWithCodeAutoFill.createExtractCode();

      // act
      final String code = extractCode(smsContent);

      // assert
      expect(code, '111111');
    });

    test('should return code (5 digit) if sms content does contain code (6 digit)', () {
      // arrange
      const String smsContent = 'Your verification code is: 123456';
      const String codeRegexPattern = r'(\d{5})';
      final String Function(String?) extractCode =
          mockWidgetWithCodeAutoFill.createExtractCode(smsCodeRegexPattern: codeRegexPattern);

      // act
      final String code = extractCode(smsContent);

      // assert
      expect(code, '12345');
    });

    test('should return empty if sms content does not contain code', () {
      // arrange
      const String smsContent = 'Your verification code is';
      const String codeRegexPattern = r'(\d{5})';

      // act
      final String Function(String?) extractCode =
          mockWidgetWithCodeAutoFill.createExtractCode(smsCodeRegexPattern: codeRegexPattern);
      final String code = extractCode(smsContent);

      // assert
      expect(code, '');
    });
  });

  group('verify stopListening()', () {
    test('For Android device, stopListening should complete without errors', () async {
      // arrange
      when(() => mockDeviceNativePlatform.isAndroid()).thenReturn(true);
      when(() => mockSmsAutoFill.stopListening()).thenAnswer((_) => Future<void>.value());

      // act
      await mockWidgetWithCodeAutoFill.stopListening();

      // assert
      verify(() => mockSmsAutoFill.stopListening()).called(1);
    });

    test('For iOS device, stopListening should not call', () async {
      // arrange
      when(() => mockDeviceNativePlatform.isAndroid()).thenReturn(false);
      when(() => mockDeviceNativePlatform.isIOS()).thenReturn(true);
      when(() => mockSmsAutoFill.stopListening()).thenAnswer((_) => Future<void>.value());

      // act
      await mockWidgetWithCodeAutoFill.stopListening();

      // assert
      verifyNever(() => mockSmsAutoFill.stopListening());
    });
  });
}
