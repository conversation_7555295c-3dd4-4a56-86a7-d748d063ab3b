// ignore_for_file: prefer_function_declarations_over_variables

import 'package:flutter/services.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/util/otp_auto_fill/sms_otp_auto_fill_impl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:otp_autofill/otp_autofill.dart';

class MockOTPInteractor extends Mock implements OTPInteractor {}

void main() {
  group('SmsAutoFillImpl', () {
    late OtpAutoFill otpAutoFill;
    late MockOTPInteractor mockOTPInteractor;

    const String smsContent = 'Your verification code is: 123456';

    late String Function(String? content) onExtractCode;

    setUp(() {
      mockOTPInteractor = MockOTPInteractor();
      otpAutoFill = SmsOtpAutoFillImpl(mockOTPInteractor);

      onExtractCode = (String? content) {
        return content?.split(': ')[1] ?? '';
      };
    });

    group('verify stopListening()', () {
      test('stopListening work as correctly', () async {
        // arrange
        when(() => mockOTPInteractor.stopListenForCode())
            .thenAnswer((_) => Future<Object?>.value(true));

        // act
        await otpAutoFill.stopListening();

        // assert
        verify(() => mockOTPInteractor.stopListenForCode()).called(1);
      });
    });

    group('verify startUserConsentListening() with autoStop = FALSE', () {
      test('startUserConsentListening should call onCode with extracted code', () async {
        // arrange
        when(() => mockOTPInteractor.startListenUserConsent())
            .thenAnswer((_) => Future<String?>.value(smsContent));

        // act
        await otpAutoFill.startUserConsentListening(
          onExtractCode: onExtractCode,
          onCode: (String code) {
            expect(code, '123456');
          },
          autoStop: false,
        );

        // assert
        verify(() => mockOTPInteractor.startListenUserConsent()).called(1);
      });

      test(
          'startUserConsentListening should call onError with SmsAutoFillException if PlatformException with timeout code is thrown',
          () async {
        // arrange
        final void Function(String code) onCode = (String code) {};

        when(() => mockOTPInteractor.startListenUserConsent())
            .thenThrow(PlatformException(code: SmsOtpAutoFillImpl.timeoutExceptionCode));

        // act
        await otpAutoFill.startUserConsentListening(
          onExtractCode: onExtractCode,
          onCode: onCode,
          onError: (OtpAutoFillException e) {
            expect(e.reason, OtpAutoFillExceptionReason.timeout);
          },
          autoStop: false,
        );

        // assert
        verify(() => mockOTPInteractor.startListenUserConsent()).called(1);
      });

      test(
          'startUserConsentListening should call onError with SmsAutoFillException if PlatformException is thrown',
          () async {
        // arrange
        final void Function(String code) onCode = (String code) {};

        when(() => mockOTPInteractor.startListenUserConsent())
            .thenThrow(PlatformException(message: 'platform_exception', code: 'any'));

        // act
        await otpAutoFill.startUserConsentListening(
          onExtractCode: onExtractCode,
          onCode: onCode,
          onError: (OtpAutoFillException e) {
            expect(e.reason, OtpAutoFillExceptionReason.nativePlatform);
          },
          autoStop: false,
        );

        // assert
        verify(() => mockOTPInteractor.startListenUserConsent()).called(1);
      });

      test(
          'startUserConsentListening should call onError with SmsAutoFillException if UnsupportedPlatform is thrown',
          () async {
        // arrange
        final void Function(String code) onCode = (String code) {};

        when(() => mockOTPInteractor.startListenUserConsent()).thenThrow(UnsupportedPlatform());

        // act
        await otpAutoFill.startUserConsentListening(
          onExtractCode: onExtractCode,
          onCode: onCode,
          onError: (OtpAutoFillException e) {
            expect(e.reason, OtpAutoFillExceptionReason.unsupported);
          },
          autoStop: false,
        );

        // assert
        verify(() => mockOTPInteractor.startListenUserConsent()).called(1);
      });

      test(
          'startUserConsentListening should call onError with SmsAutoFillException if Error is thrown',
          () async {
        // arrange
        final void Function(String code) onCode = (String code) {};

        when(() => mockOTPInteractor.startListenUserConsent()).thenThrow(Error());

        // act
        await otpAutoFill.startUserConsentListening(
          onExtractCode: onExtractCode,
          onCode: onCode,
          onError: (OtpAutoFillException e) {
            expect(e.reason, OtpAutoFillExceptionReason.others);
          },
          autoStop: false,
        );

        // assert
        verify(() => mockOTPInteractor.startListenUserConsent()).called(1);
      });
    });

    group('verify startUserConsentListening() with autoStop == TRUE (default value)', () {
      setUp(() {
        when(() => mockOTPInteractor.stopListenForCode())
            .thenAnswer((_) => Future<Object?>.value(true));
      });

      test(
          'startUserConsentListening should call onCode with extracted code & stopListenForCode() is called',
          () async {
        // arrange
        when(() => mockOTPInteractor.startListenUserConsent())
            .thenAnswer((_) => Future<String?>.value(smsContent));

        // act
        await otpAutoFill.startUserConsentListening(
          onExtractCode: onExtractCode,
          onCode: (String code) {
            expect(code, '123456');
          },
        );

        // assert
        verify(() => mockOTPInteractor.startListenUserConsent()).called(1);
        verify(() => mockOTPInteractor.stopListenForCode()).called(1);
      });

      test(
          'startUserConsentListening should call onError with SmsAutoFillException'
          ' if PlatformException with timeout code is thrown & stopListenForCode() is called',
          () async {
        // arrange
        final void Function(String code) onCode = (String code) {};

        when(() => mockOTPInteractor.startListenUserConsent())
            .thenThrow(PlatformException(code: SmsOtpAutoFillImpl.timeoutExceptionCode));

        // act
        await otpAutoFill.startUserConsentListening(
          onExtractCode: onExtractCode,
          onCode: onCode,
          onError: (OtpAutoFillException e) {
            expect(e.reason, OtpAutoFillExceptionReason.timeout);
          },
        );

        // assert
        verify(() => mockOTPInteractor.startListenUserConsent()).called(1);
        verify(() => mockOTPInteractor.stopListenForCode()).called(1);
      });
    });
  });
}
