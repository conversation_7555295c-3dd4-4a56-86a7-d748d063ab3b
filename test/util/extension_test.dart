import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:mocktail/mocktail.dart';

// ignore: avoid_implementing_value_types
class MockDateTime extends Mock implements DateTime {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockBuildContext extends Mock implements BuildContext {}

class TestPageBase extends PageBase {
  @override
  final RouteSettings routeSettings;

  @override
  final EventTrackingScreenId eventTrackingScreenId = const EventTrackingScreenId('test');

  const TestPageBase(this.routeSettings, {super.key});

  @override
  State<StatefulWidget> createState() => TestPageBaseState();
}

class TestPageBaseState extends State<TestPageBase> {
  @override
  Widget build(BuildContext context) {
    return Container();
  }
}

class TestPageBaseArg extends PageBaseArg {}

class Person {
  final String name;
  final int age;

  Person(this.name, this.age);
}

void main() {
  late MockCommonNavigator mockNavigator;
  late MockBuildContext mockContext;
  late TestPageBase testPageBase;
  late RoutePredicate routePredicate;

  setUpAll(() async {
    await initializeDateFormatting();
    getIt.registerLazySingleton(() => CommonUtilFunction());
  });

  setUp(() {
    mockNavigator = MockCommonNavigator();
    mockContext = MockBuildContext();
    testPageBase = TestPageBase(const RouteSettings(name: 'test_route'));
    routePredicate = (Route<dynamic> route) => true;

    getIt.registerSingleton<CommonNavigator>(mockNavigator);
  });

  tearDown(() {
    getIt.unregister<CommonNavigator>();
  });

  tearDownAll(() {
    getIt.reset();
  });

  /// server is using RFC 3339
  group('test_date_to_server_format', () {
    test('should_format_to_server_format_in_second_correctly', () async {
      final DateTime dateTime = DateTime(2022, 11, 02, 14, 19, 30); // 2022, Nov 02, 14:19:30
      final String timeZone = dateTime.getTimeZoneOffset();
      expect(dateTime.toServerFormatInSec(), '2022-11-02T14:19:30$timeZone');
    });

    test('should_format_to_server_format_in_ms_correctly', () async {
      final DateTime dateTime =
          DateTime(2022, 11, 02, 14, 19, 30, 200); // 2022, Nov 02, 14:19:30 200ms
      final String timeZone = dateTime.getTimeZoneOffset();
      expect(dateTime.toServerFormatInMs(), '2022-11-02T14:19:30.200$timeZone');
    });

    test('should_get_timezone_correctly', () async {
      final DateTime dateTime = DateTime(2022, 11, 02, 14, 19, 30, 200);
      expect(DateTime.now().getTimeZoneOffset(), dateTime.getTimeZoneOffset());
    });
  });

  group('DateTime getTimeZoneOffset', () {
    // because cannot set specific timezone for DateTime, so we need to mock it
    test('returns correct offset for positive timezone +05:30', () {
      final DateTime dateTime = MockDateTime();
      when(() => dateTime.timeZoneOffset).thenReturn(Duration(minutes: 5 * 60 + 30));

      final String offset = dateTime.getTimeZoneOffset();
      expect(offset, '+05:30');
    });

    test('returns correct offset for positive timezone +00:30', () {
      final DateTime dateTime = MockDateTime();
      when(() => dateTime.timeZoneOffset).thenReturn(Duration(minutes: 30));

      final String offset = dateTime.getTimeZoneOffset();
      expect(offset, '+00:30');
    });

    test('returns correct offset for negative timezone -00:30', () {
      final DateTime dateTime = MockDateTime();
      when(() => dateTime.timeZoneOffset).thenReturn(Duration(minutes: -30));

      final String offset = dateTime.getTimeZoneOffset();
      expect(offset, '-00:30');
    });

    test('returns correct offset for negative timezone -05:30', () {
      final DateTime dateTime = MockDateTime();
      when(() => dateTime.timeZoneOffset).thenReturn(Duration(minutes: -(5 * 60 + 30)));

      final String offset = dateTime.getTimeZoneOffset();
      expect(offset, '-05:30');
    });

    test('returns correct offset for UTC timezone', () {
      final DateTime dateTime = MockDateTime();
      when(() => dateTime.timeZoneOffset).thenReturn(Duration.zero);

      final String offset = dateTime.getTimeZoneOffset();
      expect(offset, '+00:00');
    });
  });

  group('DateTime getDifferenceDays', () {
    test('returns correct difference for same day', () {
      final DateTime date1 = DateTime(2023, 5, 15, 10, 30); // May 15, 2023 10:30 AM
      final DateTime date2 = DateTime(2023, 5, 15, 18, 45); // May 15, 2023 6:45 PM

      expect(date1.getDifferenceDays(date2), 0);
      expect(date2.getDifferenceDays(date1), 0);
    });

    test('returns correct difference for consecutive days', () {
      final DateTime date1 = DateTime(2023, 5, 15, 23, 59); // May 15, 2023 11:59 PM
      final DateTime date2 = DateTime(2023, 5, 16, 0, 1); // May 16, 2023 12:01 AM

      expect(date1.getDifferenceDays(date2), -1);
      expect(date2.getDifferenceDays(date1), 1);
    });

    test('returns correct difference for dates in different months', () {
      final DateTime date1 = DateTime(2023, 5, 31); // May 31, 2023
      final DateTime date2 = DateTime(2023, 6); // June 1, 2023

      expect(date1.getDifferenceDays(date2), -1);
      expect(date2.getDifferenceDays(date1), 1);
    });

    test('returns correct difference for dates in different years', () {
      final DateTime date1 = DateTime(2022, 12, 31); // Dec 31, 2022
      final DateTime date2 = DateTime(2023); // Jan 1, 2023

      expect(date1.getDifferenceDays(date2), -1);
      expect(date2.getDifferenceDays(date1), 1);
    });

    test('returns correct difference for dates many days apart', () {
      final DateTime date1 = DateTime(2023); // Jan 1, 2023
      final DateTime date2 = DateTime(2023, 1, 31); // Jan 31, 2023

      expect(date1.getDifferenceDays(date2), -30);
      expect(date2.getDifferenceDays(date1), 30);
    });
  });

  test('should toDateTime correctly', () async {
    final String timeZone = DateTime.now().getTimeZoneOffset();
    final DateTime? timeA = commonUtilFunction.toDateTime('2022-11-02T15:04:05$timeZone');
    final DateTime expectedA = DateTime(2022, 11, 02, 15, 04, 05);
    expect(timeA, expectedA);

    // UTC
    final DateTime? timeB = commonUtilFunction.toDateTime('2022-11-02T15:04:05Z');
    final DateTime expectedB =
        DateTime(2022, 11, 02, 15 + DateTime.now().timeZoneOffset.inHours, 04, 05);
    expect(timeB, expectedB);
  });

  group('test_apply_string_format', () {
    test('should_display_phone', () {
      const String input_10 = '0901234567';
      expect('09 012 34567',
          input_10.applyStringFormat(prefixGroup: 2, stringFormatType: StringFormatType.phone));
      expect('09012 34567',
          input_10.applyStringFormat(suffixGroup: 5, stringFormatType: StringFormatType.phone));
      expect(
          '0 90123 4567',
          input_10.applyStringFormat(
              prefixGroup: 1, suffixGroup: 4, stringFormatType: StringFormatType.phone));

      const String input_11 = '09012345678';
      expect(
          '09012345 678',
          input_11.applyStringFormat(
              prefixGroup: 8, suffixGroup: 7, stringFormatType: StringFormatType.phone));
      expect(
          '090 12345678',
          input_11.applyStringFormat(
              prefixGroup: 3, suffixGroup: 9, stringFormatType: StringFormatType.phone));
      expect('************8', input_11.applyStringFormat(stringFormatType: StringFormatType.phone));

      const String input_12 = '090123456789';
      expect(
          '************ 89',
          input_12.applyStringFormat(
              prefixGroup: 3, stringFormatType: StringFormatType.phone, suffixGroup: 2));

      const String input_13 = '0901234567';
      expect(
          '0901 23 45 67',
          input_13.applyStringFormat(
              prefixGroup: 4, stringFormatType: StringFormatType.phone2digits));

      const String input_14 = '09012345';
      expect('0901 2345',
          input_14.applyStringFormat(prefixGroup: 4, stringFormatType: StringFormatType.phone));
    });

    test('should_display_bar_code', () {
      const String input_1 = '12345';
      const String output_1 = '12345';
      expect(output_1, input_1.applyStringFormat(stringFormatType: StringFormatType.barcode));

      const String input_2 = '12345';
      const String output_2 = '12345';
      expect(output_2,
          input_2.applyStringFormat(stringFormatType: StringFormatType.barcode, suffixGroup: 8));

      const String input_3 = '1234567';
      const String output_3 = '12 34567';
      expect(output_3,
          input_3.applyStringFormat(stringFormatType: StringFormatType.barcode, prefixGroup: 2));

      const String input_4 = '1234356457678768456345623452364575678';
      const String output_4 = '12343564 5767 8768 4563 4562 3452 3645 75678';
      expect(output_4,
          input_4.applyStringFormat(stringFormatType: StringFormatType.barcode, prefixGroup: 8));

      const String input_5 = '45342314';
      const String output_5 = '453423 14';
      expect(output_5,
          input_5.applyStringFormat(stringFormatType: StringFormatType.barcode, suffixGroup: 2));

      const String input_6 = '658768675676767867867';
      const String output_6 = '6 5876 8675 6767 67867 867';
      expect(
          output_6,
          input_6.applyStringFormat(
              stringFormatType: StringFormatType.barcode, prefixGroup: 1, suffixGroup: 3));

      const String input_7 = '32423345345';
      const String output_7 = '3242334 5345';
      expect(output_7,
          input_7.applyStringFormat(stringFormatType: StringFormatType.barcode, suffixGroup: 4));

      const String input_8 = '1';
      const String output_8 = '1';
      expect(output_8,
          input_8.applyStringFormat(stringFormatType: StringFormatType.barcode, prefixGroup: 4));
    });

    test('verify to display CCCD/CMND', () {
      // 12 digits
      const String input_1 = '123854847112';
      expect('123 854 847 112',
          input_1.applyStringFormat(prefixGroup: 3, stringFormatType: StringFormatType.idCard));

      // 9 digits
      const String input_2 = '123854847';
      expect('123 854 847',
          input_2.applyStringFormat(prefixGroup: 3, stringFormatType: StringFormatType.idCard));
    });
  });

  group('test hidden string by format', () {
    test('test_apply_hidden_format_string_with_valid_case', () {
      const String input = '0123456789';
      expect('xxxxxxx789', input.hiddenByFormat(7));
    });
    test('test_apply_hidden_format_string_with_startIndex less than 0', () {
      const String input = '0123456789';
      expect('0123456789', input.hiddenByFormat(0, startIndex: -1));
    });
    test('test_apply_hidden_format_string_with_endIndex less than 0', () {
      const String input = '0123456789';
      expect('0123456789', input.hiddenByFormat(-1));
    });
    test('test_apply_hidden_format_string_with_startIndex>endIndex', () {
      const String input = '0123456789';
      expect('0123456789', input.hiddenByFormat(4, startIndex: 5));
    });
    test('test_apply_hidden_format_string_with_endIndex>len', () {
      const String input = '0123456789';
      expect('0123456789', input.hiddenByFormat(11));
    });
  });

  test('test parseBool function', () {
    expect('True'.toBool(), true);
    expect('true'.toBool(), true);
    expect('False'.toBool(), false);
    expect('false'.toBool(), false);
    expect('not a bool'.toBool(), null);
    expect(''.toBool(), null);
  });

  group('test replace string multi params', () {
    test('no params', () {
      const String inputTest = 'Test no param';
      expect(inputTest.replaceVariableByValue(<String>[]), inputTest);
    });

    test('text no params and input param to function', () {
      const String inputTest = 'Test no param';
      expect(inputTest.replaceVariableByValue(<String>['one']), inputTest);
    });

    test('text has params and no input param to function', () {
      const String inputTest = 'Test {0} param';
      expect(inputTest.replaceVariableByValue(<String>[]), inputTest);
    });

    test('text error param', () {
      const String inputTest = 'Test {01} param';
      expect(inputTest.replaceVariableByValue(<String>['one', 'two']), inputTest);
    });

    test('one params', () {
      const String inputTest = 'Test {0} param';
      expect(inputTest.replaceVariableByValue(<String>['one']), 'Test one param');
    });

    test('two params', () {
      const String inputTest = 'Test {0} {1} params';
      expect(inputTest.replaceVariableByValue(<String>['one', 'two']), 'Test one two params');
    });

    test('five params', () {
      const String inputTest = 'Test {0} {1} {2} {3} {4} params';
      expect(inputTest.replaceVariableByValue(<String>['one', 'two', 'three', 'four', 'five']),
          'Test one two three four five params');
    });
  });

  group('StringExt uppercase methods', () {
    test('test uppercaseFirstLetter function', () {
      final String emptyStr = ''.uppercaseFirstLetter();
      final String lowerStrOneChar = 'a'.uppercaseFirstLetter();
      final String lowerStr = 'abc def'.uppercaseFirstLetter();

      expect(emptyStr, '');
      expect(lowerStrOneChar, 'A');
      expect(lowerStr, 'Abc def');
    });

    test('test uppercaseFirstLetterEachWord function', () {
      final String emptyStr = ''.uppercaseFirstLetterEachWord();
      final String singleWord = 'hello'.uppercaseFirstLetterEachWord();
      final String multipleWords = 'hello world'.uppercaseFirstLetterEachWord();
      final String withEmptySpaces = 'hello  world'.uppercaseFirstLetterEachWord();
      final String alreadyCapitalized = 'Hello World'.uppercaseFirstLetterEachWord();

      expect(emptyStr, '');
      expect(singleWord, 'Hello');
      expect(multipleWords, 'Hello World');
      expect(withEmptySpaces, 'Hello  World');
      expect(alreadyCapitalized, 'Hello World');
    });
  });

  group('verify removeWWWPattern() method', () {
    test('check removeWWWPattern work correctly with https', () {
      const String url = 'https://www.google.com/';
      final String actualUrl = url.removeWWWPattern();
      expect(actualUrl, 'https://google.com/');
    });

    test('check removeWWWPattern work correctly with http', () {
      const String url = 'http://www.google.com/';
      final String actualUrl = url.removeWWWPattern();
      expect(actualUrl, 'http://google.com/');
    });
  });

  group('Test toResolution', () {
    test('Should return right resolution format in height x width', () {
      const Size size = Size(100, 200);
      expect(size.toResolution(), '200 x 100');
    });
  });

  test('Test Duration.remainder()', () {
    expect(const Duration(seconds: -1).remainder(), equals('00:00'));
    expect(const Duration(minutes: -1).remainder(), equals('00:00'));
    expect(const Duration(hours: -1).remainder(), equals('00:00'));
    expect(const Duration().remainder(), equals('00:00'));

    expect(const Duration(milliseconds: 1).remainder(), equals('00:00'));
    expect(const Duration(milliseconds: 1001).remainder(), equals('00:01'));

    expect(const Duration(seconds: 1).remainder(), equals('00:01'));
    expect(const Duration(seconds: 61).remainder(), equals('01:01'));

    expect(const Duration(minutes: 1, seconds: 1).remainder(), equals('01:01'));
    expect(const Duration(minutes: 61, seconds: 1).remainder(), equals('01:01:01'));

    expect(const Duration(hours: 1, minutes: 1, seconds: 1).remainder(), equals('01:01:01'));
    expect(const Duration(hours: 25, minutes: 1, seconds: 1).remainder(), equals('25:01:01'));
    expect(
        const Duration(days: 1, hours: 1, minutes: 1, seconds: 1).remainder(), equals('25:01:01'));
  });

  // Skipping CommonLog tests as they require more complex mocking of kDebugMode
  // which is a compile-time constant

  group('ObjectExt.let', () {
    test('should apply the operation to the object and return the result', () {
      // Arrange
      const String value = 'test';

      // Act
      final String result = value.let((String it) => 'Value is: $it');

      // Assert
      expect(result, 'Value is: test');
    });

    test('should work with different return types', () {
      // Arrange
      const int value = 42;

      // Act
      final bool result = value.let((int it) => it > 10);

      // Assert
      expect(result, true);
    });

    test('should work with complex objects', () {
      // Arrange
      final Person person = Person('John', 30);

      // Act
      final String result = person.let((Person it) => '${it.name} is ${it.age} years old');

      // Assert
      expect(result, 'John is 30 years old');
    });

    test('should allow chaining operations', () {
      // Arrange
      const String value = 'test';

      // Act
      final String result =
          value.let((String it) => it.toUpperCase()).let((String it) => '$it!').let(
                (String it) => 'Result: $it',
              );

      // Assert
      expect(result, 'Result: TEST!');
    });
  });

  group('NullableExt.letOrElse', () {
    test('should execute action when value is not null', () {
      // Arrange
      final String? nonNullValue;
      nonNullValue = 'test';

      // Act
      final String result = nonNullValue.letOrElse(
        action: (String value) => 'Value is: $value',
        fallbackFn: () => 'No value',
      );

      // Assert
      expect(result, 'Value is: test');
    });

    test('should execute fallback when value is null', () {
      // Arrange
      final String? nullValue = null;

      // Act
      final String result = nullValue.letOrElse(
        action: (String value) => 'Value is: $value',
        fallbackFn: () => 'No value',
      );

      // Assert
      expect(result, 'No value');
    });

    test('should handle different return types', () {
      // Arrange
      int? nonNullValue;
      nonNullValue = 42;

      final int? nullValue = null;

      // Act
      final bool resultNonNull = nonNullValue.letOrElse(
        action: (int value) => value > 10,
        fallbackFn: () => false,
      );

      final bool resultNull = nullValue.letOrElse(
        action: (int value) => value > 10,
        fallbackFn: () => false,
      );

      // Assert
      expect(resultNonNull, true);
      expect(resultNull, false);
    });

    test('should preserve the original value in action function', () {
      // Arrange
      Person? person;
      person = Person('John', 30);

      // Act
      final String result = person.letOrElse(
        action: (Person value) => '${value.name} is ${value.age} years old',
        fallbackFn: () => 'Unknown person',
      );

      // Assert
      expect(result, 'John is 30 years old');
    });
  });

  group('Navigation extension', () {
    test('goPage should call CommonNavigator.goPage with correct parameters', () {
      // Arrange
      final PageBaseArg extra = TestPageBaseArg();

      // Act
      mockContext.goPage(testPageBase, extra: extra);

      // Assert
      verify(() => mockNavigator.goPage(mockContext, testPageBase, extra: extra)).called(1);
    });

    test('goNamed should call CommonNavigator.goNamed with correct parameters', () {
      // Arrange
      const String pageName = 'testPage';
      final PageBaseArg extra = TestPageBaseArg();

      // Act
      mockContext.goNamed(pageName, extra: extra);

      // Assert
      verify(() => mockNavigator.goNamed(mockContext, pageName, extra: extra)).called(1);
    });

    test('pushPage should call CommonNavigator.pushPage with correct parameters', () {
      // Arrange
      final PageBaseArg extra = TestPageBaseArg();

      // Act
      mockContext.pushPage(testPageBase, extra: extra);

      // Assert
      verify(() => mockNavigator.pushPage(mockContext, testPageBase, extra: extra)).called(1);
    });

    test('pushNamed should call CommonNavigator.pushNamed with correct parameters', () {
      // Arrange
      const String pageName = 'testPage';
      final PageBaseArg extra = TestPageBaseArg();

      // Act
      mockContext.pushNamed(pageName, extra: extra);

      // Assert
      verify(() => mockNavigator.pushNamed(mockContext, pageName, extra: extra)).called(1);
    });

    test(
        'pushReplacementPage should call CommonNavigator.pushReplacementPage with correct parameters',
        () {
      // Arrange
      final PageBaseArg extra = TestPageBaseArg();

      // Act
      mockContext.pushReplacementPage(testPageBase, extra: extra);

      // Assert
      verify(() => mockNavigator.pushReplacementPage(mockContext, testPageBase, extra: extra))
          .called(1);
    });

    test(
        'pushReplacementNamed should call CommonNavigator.pushReplacementNamed with correct parameters',
        () {
      // Arrange
      const String pageName = 'testPage';
      final PageBaseArg extra = TestPageBaseArg();

      // Act
      mockContext.pushReplacementNamed(pageName, extra: extra);

      // Assert
      verify(() => mockNavigator.pushReplacementNamed(mockContext, pageName, extra: extra))
          .called(1);
    });

    test('pop should call CommonNavigator.pop with correct parameters', () {
      // Arrange
      const String result = 'result';

      // Act
      mockContext.pop(result: result);

      // Assert
      verify(() => mockNavigator.pop(mockContext, result: result)).called(1);
    });

    test('popBack should call CommonNavigator.canPop and return its result', () {
      // Arrange
      when(() => mockNavigator.canPop(mockContext)).thenReturn(true);

      // Act
      final bool result = mockContext.popBack();

      // Assert
      verify(() => mockNavigator.canPop(mockContext)).called(1);
      expect(result, true);
    });

    test(
        'maybePop should call CommonNavigator.maybePop with correct parameters and return its result',
        () {
      // Arrange
      const String result = 'result';
      when(() => mockNavigator.maybePop(mockContext, result: result)).thenReturn(true);

      // Act
      final bool popResult = mockContext.maybePop(result: result);

      // Assert
      verify(() => mockNavigator.maybePop(mockContext, result: result)).called(1);
      expect(popResult, true);
    });

    test('popUntilNamed should call CommonNavigator.popUntilNamed with correct parameters', () {
      // Arrange
      const String pageName = 'testPage';
      onPageFound() {}
      onPageNotFound() {}

      // Act
      mockContext.popUntilNamed(
        pageName,
        onPageFound: onPageFound,
        onPageNotFound: onPageNotFound,
      );

      // Assert
      verify(() => mockNavigator.popUntilNamed(
            mockContext,
            pageName,
            onPageFound: onPageFound,
            onPageNotFound: onPageNotFound,
          )).called(1);
    });

    test(
        'popUntilAndReplaceNamed should call CommonNavigator.popUntilAndReplaceNamed with correct parameters',
        () {
      // Arrange
      const String pageName = 'testPage';
      final PageBaseArg extra = TestPageBaseArg();
      onDone() {}

      // Act
      mockContext.popUntilAndReplaceNamed(
        pageName,
        extra: extra,
        onDone: onDone,
      );

      // Assert
      verify(() => mockNavigator.popUntilAndReplaceNamed(
            mockContext,
            pageName,
            extra: extra,
            onDone: onDone,
          )).called(1);
    });

    test(
        'removeUntilAndPushReplacementNamed should call CommonNavigator.removeUntilAndPushReplacementNamed with correct parameters',
        () {
      // Arrange
      const String pageName = 'testPage';
      final PageBaseArg extra = TestPageBaseArg();

      // Act
      mockContext.removeUntilAndPushReplacementNamed(
        pageName,
        routePredicate,
        extra: extra,
      );

      // Assert
      verify(() => mockNavigator.removeUntilAndPushReplacementNamed(
            mockContext,
            pageName,
            routePredicate,
            extra: extra,
          )).called(1);
    });
  });
}
