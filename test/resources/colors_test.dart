import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';

class MockCommonColors extends CommonColors {
  @override
  final Color primary = Colors.blue;
  @override
  final Color foreground = Colors.black;
  @override
  final Color background = Colors.white;
  @override
  final Color error = Colors.red;
  @override
  final Color highlighted = Colors.yellow;
  @override
  final Color appBarShadow = Colors.grey;
  @override
  final Color iconColor = Colors.green;
  @override
  final Color selectedRadioButton = Colors.orange;
  @override
  final Color loadingViewColor = Colors.purple;

  @override
  final Color bottomSheetBackground = Colors.brown;
  @override
  final Color bottomSheetSelectedItem = Colors.cyan;
  @override
  final Color bottomSheetUnselectedItem = Colors.indigo;

  @override
  final Color textActive = Colors.black87;
  @override
  final Color textPassive = Colors.black54;
  @override
  final Color textPassive2 = Colors.black38;
  @override
  final Color textHint = Colors.grey;
  @override
  final Color textNormal = Colors.black;
  @override
  final Color icon = Colors.blueGrey;

  @override
  final Color focusedTextFieldBorder = Colors.blueAccent;
  @override
  final Color textFieldBorder = Colors.grey;
  @override
  final Color disableTextFieldBorder = Colors.grey.shade400;
  @override
  final Color disableTextFieldBg = Colors.grey.shade200;
  @override
  final Color textFieldBg = Colors.white;
  @override
  final Color textSelectedBg = Colors.lightBlue.shade50;

  @override
  final Color inputFocusedColor = Colors.blue;
  @override
  final Color inputUnfocusedColor = Colors.grey;

  @override
  final Color primaryButtonForeground = Colors.white;
  @override
  final Color primaryButtonBg = Colors.blue;
  @override
  final Color primaryButtonForegroundDisable = Colors.grey;
  @override
  final Color primaryButtonBgDisable = Colors.grey.shade300;

  @override
  final Color secondaryButtonForeground = Colors.black;
  @override
  final Color secondaryButtonBg = Colors.grey.shade200;
  @override
  final Color secondaryButtonForegroundDisable = Colors.grey.shade600;
  @override
  final Color secondaryButtonBgDisable = Colors.grey.shade400;

  @override
  final Color accentButtonForeground = Colors.white;
  @override
  final Color accentButtonBg = Colors.pink;
  @override
  final Color accentButtonForegroundDisable = Colors.grey;
  @override
  final Color accentButtonBgDisable = Colors.grey.shade300;

  @override
  final Color tertiaryButtonForeground = Colors.black;
  @override
  final Color tertiaryButtonBg = Colors.grey.shade100;
  @override
  final Color tertiaryButtonForegroundDisable = Colors.grey.shade600;
  @override
  final Color tertiaryButtonBgDisable = Colors.grey.shade400;

  @override
  final Color negativeButtonForeground = Colors.white;
  @override
  final Color negativeButtonBg = Colors.red;
  @override
  final Color negativeButtonForegroundDisable = Colors.grey;
  @override
  final Color negativeButtonBgDisable = Colors.grey.shade300;

  @override
  final Color webViewProgressBg = Colors.grey.shade200;
  @override
  final Color webViewProgressValue = Colors.blue;
}

void main() {
  group('CommonColors Tests', () {
    late MockCommonColors mockColors;

    setUp(() {
      mockColors = MockCommonColors();
    });

    test('Verify common colors', () {
      expect(mockColors.primary, Colors.blue);
      expect(mockColors.foreground, Colors.black);
      expect(mockColors.background, Colors.white);
      expect(mockColors.error, Colors.red);
      expect(mockColors.highlighted, Colors.yellow);
      expect(mockColors.appBarShadow, Colors.grey);
      expect(mockColors.iconColor, Colors.green);
      expect(mockColors.selectedRadioButton, Colors.orange);
      expect(mockColors.loadingViewColor, Colors.purple);
    });

    test('Verify bottom sheet colors', () {
      expect(mockColors.bottomSheetBackground, Colors.brown);
      expect(mockColors.bottomSheetSelectedItem, Colors.cyan);
      expect(mockColors.bottomSheetUnselectedItem, Colors.indigo);
    });

    test('Verify text colors', () {
      expect(mockColors.textActive, Colors.black87);
      expect(mockColors.textPassive, Colors.black54);
      expect(mockColors.textPassive2, Colors.black38);
      expect(mockColors.textHint, Colors.grey);
      expect(mockColors.textNormal, Colors.black);
      expect(mockColors.icon, Colors.blueGrey);
    });

    test('Verify button colors', () {
      expect(mockColors.primaryButtonForeground, Colors.white);
      expect(mockColors.primaryButtonBg, Colors.blue);
      expect(mockColors.primaryButtonForegroundDisable, Colors.grey);
      expect(mockColors.primaryButtonBgDisable, Colors.grey.shade300);

      expect(mockColors.secondaryButtonForeground, Colors.black);
      expect(mockColors.secondaryButtonBg, Colors.grey.shade200);
      expect(mockColors.secondaryButtonForegroundDisable, Colors.grey.shade600);
      expect(mockColors.secondaryButtonBgDisable, Colors.grey.shade400);

      expect(mockColors.accentButtonForeground, Colors.white);
      expect(mockColors.accentButtonBg, Colors.pink);
      expect(mockColors.accentButtonForegroundDisable, Colors.grey);
      expect(mockColors.accentButtonBgDisable, Colors.grey.shade300);
    });

    test('Verify web view progress colors', () {
      expect(mockColors.webViewProgressBg, Colors.grey.shade200);
      expect(mockColors.webViewProgressValue, Colors.blue);
    });
  });
}
