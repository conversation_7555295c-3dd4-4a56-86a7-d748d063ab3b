import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:mocktail/mocktail.dart';

class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {}

class MockFirebaseAnalyticsObserver extends Mock implements FirebaseAnalyticsObserver {}

Future<void> setupFirebaseForTest() async {
  if (getIt.isRegistered<FirebaseAnalyticsWrapper>()) {
    getIt.unregister<FirebaseAnalyticsWrapper>();
  }
  final MockFirebaseAnalytics mockFirebaseAnalytics = MockFirebaseAnalytics();
  final MockFirebaseAnalyticsObserver mockFirebaseAnalyticsObserver =
      MockFirebaseAnalyticsObserver();
  when(() => mockFirebaseAnalytics.logEvent(
        name: any(named: 'name'),
        parameters: any(named: 'parameters'),
      )).thenAnswer((_) async {});
  when(() => mockFirebaseAnalyticsObserver.analytics).thenReturn(mockFirebaseAnalytics);

  getIt.registerLazySingleton<FirebaseAnalyticsWrapper>(() => FirebaseAnalyticsWrapper(
        mockFirebaseAnalytics,
        mockFirebaseAnalyticsObserver,
      ));
}

class MockCommonObserver extends Mock implements CommonNavigatorObserver {}

Future<void> setupNavigatorObserversForTest() async {
  if (getIt.isRegistered<CommonNavigatorObserver>()) {
    getIt.unregister<CommonNavigatorObserver>();
  }
  final MockCommonObserver mockCommonObserver = MockCommonObserver();
  getIt.registerLazySingleton<CommonNavigatorObserver>(() => mockCommonObserver);

  setupFirebaseForTest();
}
